import { getRequest, postRequest } from '../http/request'

export const loadApi = {
  // 分页查询
  queryPayloadPage: (param: object) => {
    return postRequest('/payload/queryPage', param)
  },
  // 添加
  addPayload: (param: object) => {
    return postRequest('/payload/add', param)
  },
  // 更新
  updatePayload: (param: object) => {
    return postRequest('/payload/update', param)
  },
  // 删除
  deletePayload: (id: string | number) => {
    return getRequest(`/payload/delete/${id}`, {})
  }
}
