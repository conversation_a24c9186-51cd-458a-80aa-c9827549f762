import App from './App.vue'
import { router, buildRoutes } from './router'
import { antComponents } from './antd'
import { createInstance } from './root'
import { loginApi } from '@/api/loginApi'
import { ELocalStorageKey, ERouterName, EUserType } from '@/types/enums'
import * as antIcons from '@ant-design/icons-vue'
import store from './store'
import { privilegeDirective } from '@/directives/privilege'
import directives from '@/directives'
import lodash from 'lodash'

import moment from 'moment'
// 导入中文语言包
import 'moment/dist/locale/zh-cn'
// 设置 moment 语言为中文
moment.locale('zh-cn')

import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')

// 定义CESIUM_BASE_URL
window.CESIUM_BASE_URL = '/cesium/Build/Cesium/'

async function initVue() {
  let vueApp = createInstance(App)
  let app = vueApp.use(antComponents).use(router).use(store)
  // 注册指令
  Object.keys(directives).forEach(key => {
    app.directive(key, directives[key])
  })
  // 注册图标组件
  Object.keys(antIcons).forEach(key => {
    app.component(key, antIcons[key])
  })
  //全局
  app.config.globalProperties.$antIcons = antIcons
  app.config.globalProperties.$lodash = lodash
  //挂载
  app.mount('#app')
}
/**
 * 获取用户信息和用户权限对应的路由，构建动态路由
 */
async function getLoginInfo() {
  try {
    //获取登录用户信息
    const res = await loginApi.getLoginInfo()
    //构建系统的路由
    let menuRouterList = res.data.menuList.filter(e => e.path || e.frameUrl)
    buildRoutes(menuRouterList)
    await initVue()
    //更新用户信息到vuex
    store.dispatch('user/setLoginInfo', res.data)
  } catch (e) {
    // console.log(e)
    initVue()
  }
}

const getPathToken = () => {
  const url = new URL(window.location.href)
  const index = url.search.indexOf('?')
  const params = index !== -1 ? url.search.substring(index + 1) : ''
  const hashParams = new URLSearchParams(params)
  const pathToken = hashParams.get('token')
  if (pathToken) localStorage.setItem(ELocalStorageKey.Token, pathToken)
}

getPathToken()
let token = localStorage.getItem(ELocalStorageKey.Token)
if (!token) {
  await initVue()
  // console.log(store)
} else {
  await getLoginInfo()
}
