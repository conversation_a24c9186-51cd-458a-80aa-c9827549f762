import { getRequest, postRequest, postEncryptRequest } from '../http/request'

export const userApi = {
  // 分页查询
  queryEmployeePage: (param: object) => {
    return postRequest('/employee/query', param)
  },
  // 添加
  addEmployee: (param: object) => {
    return postRequest('/employee/add', param)
  },
  // 更新
  updateEmployee: (param: object) => {
    return postRequest('/employee/update', param)
  },
  // 删除
  batchDeleteEmployee: (param: Array<number>) => {
    return postRequest('/employee/update/batch/delete', param)
  },
  // 重置密码
  resetEmployeePassword: (employeeId: string | number) => {
    return getRequest(`/employee/update/password/reset/${employeeId}`, {})
  },
  // 更新员工禁用-启用状态
  updateEmployeeDisabled: (employeeId: string | number) => {
    return getRequest(`/employee/update/disabled/${employeeId}`, {})
  },
  // 查询所有员工
  queryEmployeeAll: (param: object) => {
    return getRequest(`/employee/queryAll`, param)
  },
  // 获取密码复杂度
  getPasswordComplexityEnabled: () => {
    return getRequest(`/employee/getPasswordComplexityEnabled`, {})
  },
  uploadFile: param => {
    return postRequest(`/support/file/upload`, param)
  },
  // 更新头像
  updateAvatar: param => {
    return postRequest(`/employee/update/avatar`, param)
  },
  // 更新邮箱
  updateEmail: param => {
    return postRequest(`/employee/update/email`, param)
  },
  // 更新邮箱
  updatePhone: param => {
    return postRequest(`/employee/update/phone`, param)
  },
  // 修改密码
  updatePassword: param => {
    return postEncryptRequest(`/employee/update/password`, param)
  },
  // 获取密码安全等级
  getPasswordSecurityLevel: param => {
    return getRequest(`/employee/getPasswordSecurityLevel`, param)
  }
}
