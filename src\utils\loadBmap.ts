export function BMAPGL(ak: string) {
  return new Promise(function (resolve, reject) {
    if (window.BMapGL) {
      resolve(window.BMapGL)
      return
    }
    // 使用类型断言将 window 转换为 any 类型，避免类型检查
    ;(window as any).BMapGLInit = function () {
      // 确保 BMapGL 存在
      if (typeof window.BMapGL !== 'undefined') {
        resolve(window.BMapGL)
      } else {
        reject(new Error('BMapGL is not defined'))
      }
    }
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.src = `http://api.map.baidu.com/api?v=3.0&type=webgl&ak=${ak}&callback=BMapGLInit`
    script.onerror = reject
    document.head.appendChild(script)
  })
}
