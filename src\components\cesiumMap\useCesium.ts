import { viewer, handler } from "./initCesium";
import * as Cesium from "cesium";
import { ref, nextTick } from "vue";
import rootStore from "@/store/index";

import startBg from "@/assets/images/startBg.png";
import endBg from "@/assets/images/endBg.png";
import vehicle from "@/assets/images/vehicle.png";

export function useCesium() {
  const popFlag = ref(false); // 弹框显示
  const popData = ref(); // 点击数据
  const mapPopup = ref(); // 弹框dom
  const vehicleEntity = ref();

  const flyToEntitiesCenter = (points: any) => {
    // 获取所有点的边界框
    const pointPositions = points.map((point: any) =>
      Cesium.Cartesian3.fromDegrees(point.lon, point.lat)
    );

    const boundingBox = Cesium.BoundingSphere.fromPoints(pointPositions);
    // 设置视角包含所有内容
    viewer.zoomTo(
      viewer.entities,
      new Cesium.HeadingPitchRange(
        0,
        -Cesium.Math.PI_OVER_TWO,
        boundingBox.radius * 6
      )
    );
    // // 设置相机位置和方向
    // viewer.camera.flyTo({
    //   destination: Cesium.Cartesian3.fromDegrees(
    //     Cesium.Math.toDegrees(
    //       Cesium.Cartographic.fromCartesian(boundingBox.center).longitude
    //     ),
    //     Cesium.Math.toDegrees(
    //       Cesium.Cartographic.fromCartesian(boundingBox.center).latitude
    //     ),
    //     boundingBox.radius * 2.5
    //   ),
    //   orientation: {
    //     heading: Cesium.Math.toRadians(0), // 方向为正东
    //     pitch: Cesium.Math.toRadians(-90), // 垂直向下看
    //     roll: 0.0, // 无翻滚
    //   },
    //   duration: 2, // 飞行时间，单位为秒
    // });
  };

  // 添加点没有上方名称
  const addpointNoLabel = (point: any, image: any) => {
    if (image) {
      viewer.entities.add({
        id: point.id,
        data: point,
        position: Cesium.Cartesian3.fromDegrees(point.lon, point.lat),
        billboard: {
          image: image, // 使用相对路径引入图片资源
          width: 30,
          height: 30,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 确保图片底部中心指向坐标点
        },
      });
    } else {
      viewer.entities.add({
        id: point.id,
        data: point,
        position: Cesium.Cartesian3.fromDegrees(point.lon, point.lat),
        billboard: {
          image: image, // 使用相对路径引入图片资源
          width: 50,
          height: 50,
        },
        point: {
          pixelSize: 20, // 点的大小，以像素为单位
          color: Cesium.Color.RED, // 点的颜色
          outlineColor: Cesium.Color.WHITE, // 点轮廓颜色
          outlineWidth: 2, // 点轮廓宽度
        },
      });
    }
  };

  // 添加点
  const addpoint = (point: any, image: any, labelColor: any) => {
    if (image) {
      viewer.entities.add({
        id: point.id,
        data: point,
        position: Cesium.Cartesian3.fromDegrees(point.lon, point.lat),
        billboard: {
          image: image, // 使用相对路径引入图片资源
          width: 40,
          height: 40,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 确保图片底部中心指向坐标点
        },
        label: {
          text: point.name,
          font: "14px sans-serif",
          fillColor: Cesium.Color.fromCssColorString(
            labelColor ? labelColor : "#D20000"
          ),
          outlineColor: Cesium.Color.fromCssColorString("#AAA"),
          outlineWidth: 0,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 确保标签位于点的上方
          pixelOffset: new Cesium.Cartesian2(6, -44), // 调整标签位置，确保标签位于点的上方
        },
      });
    } else {
      viewer.entities.add({
        id: point.id,
        data: point,
        position: Cesium.Cartesian3.fromDegrees(point.lon, point.lat),
        billboard: {
          image: image, // 使用相对路径引入图片资源
          width: 50,
          height: 50,
        },
        point: {
          pixelSize: 20, // 点的大小，以像素为单位
          color: Cesium.Color.RED, // 点的颜色
          outlineColor: Cesium.Color.WHITE, // 点轮廓颜色
          outlineWidth: 2, // 点轮廓宽度
        },
      });
    }
  };
  // 添加绑定事件
  const setEvent = () => {
    // 左键点击事件
    let leftclick = Cesium.ScreenSpaceEventType.LEFT_CLICK;
    viewer.screenSpaceEventHandler.removeInputAction(leftclick);
    handler.setInputAction((pickClick: any) => {
      // 返回笛卡尔2坐标系 - 为点击点位置
      // 获取点击的实体
      const pick = viewer.scene.pick(pickClick.position);

      if (!pick || !pick.id) {
        return false;
      }
      const pick_obj = Cesium.defaultValue(pick.id, pick.primitive.id);
      // 判断是否是Cesium实体
      // if (pick_obj instanceof Cesium.Entity) {
      const data = (pick_obj as any).data; // 使用类型断言绕过检查
      if (!data || !data.lat || !data.lon) {
        return;
      }
      // 经纬度转笛卡尔3
      const cartesian3 = Cesium.Cartesian3.fromDegrees(
        Number(data.lon),
        Number(data.lat),
        0.1
      );
      // 获取实体笛卡尔2坐标系
      const screenposition = Cesium.SceneTransforms.worldToWindowCoordinates(
        viewer.scene,
        cartesian3
      );
      rootStore.dispatch("workspace/mapPointClick", data);
      // }
    }, leftclick);
  };
  // 添加绑定事件,弹出气泡框
  const setPopEvent = () => {
    // 左键点击事件
    let mouseMove = Cesium.ScreenSpaceEventType.MOUSE_MOVE;
    viewer.screenSpaceEventHandler.removeInputAction(mouseMove);
    handler.setInputAction((movement: any) => {
      // 返回笛卡尔2坐标系 - 为点击点位置
      // 获取实体
      const pick = viewer.scene.pick(movement.endPosition);
      if (!pick || !pick.id) {
        closeMapPopup();
        return false;
      }
      const pick_obj = Cesium.defaultValue(pick.id, pick.primitive.id);
      // 判断是否是Cesium实体
      const data = (pick_obj as any).data; // 使用类型断言绕过检查
      if (!data || !data.lat || !data.lon) {
        return;
      }
      // 经纬度转笛卡尔3
      const cartesian3 = Cesium.Cartesian3.fromDegrees(
        Number(data.lon),
        Number(data.lat),
        0.1
      );
      // 获取实体笛卡尔2坐标系
      const screenposition = Cesium.SceneTransforms.worldToWindowCoordinates(
        viewer.scene,
        cartesian3
      );
      // 这里使用实体的坐标而不是用点击点的坐标，为了防止弹框位置相对于点位置不固定
      createPopwinOnMap(data, screenposition);
    }, mouseMove);
  };
  // 存放监听事件
  const closePopEvent: any[] = [];
  // 弹窗相关
  const createPopwinOnMap = async (value: any, clickPostion: any) => {
    // popFlag.value = false;
    popData.value = value;
    popFlag.value = true;
    await nextTick();

    if (mapPopup.value) {
      // 获取根节点
      const domref = mapPopup.value.$el;

      if (!domref) {
        return;
      }
      const position = viewer.scene.camera.pickEllipsoid(
        clickPostion,
        viewer.scene.globe.ellipsoid
      );

      let c = new Cesium.Cartesian2(clickPostion.x, clickPostion.y);
      //球面转动后 UI跟随物体处理
      let temEvent = viewer.scene.postRender.addEventListener(() => {
        if (position && c) {
          const changedC = Cesium.SceneTransforms.worldToWindowCoordinates(
            viewer.scene,
            position
          );
          domref.style.bottom = viewer.canvas.clientHeight - c.y + "px";
          domref.style.left = c.x - 100 + "px";
          if (changedC) {
            c = changedC;
          }
        }
      });
      closePopEvent.push(temEvent);
    }
  };
  // 清除弹框监听事件
  const removeEvent = () => {
    if (closePopEvent.length > 0) {
      closePopEvent.forEach((item) => {
        // 执行监听事件本身，可删除监听事件
        item();
      });
    }
    closePopEvent.length = 0;
  };

  // 关闭地图弹框
  const closeMapPopup = () => {
    popFlag.value = false;
    removeEvent();
  };

  // 添加轨迹
  const addTrack = (trackPointsArr: any) => {
    // 不做平滑处理的线,将坐标转换为 Cartesian3 格式
    let trackPoints = trackPointsArr.map((point: any) =>
      Cesium.Cartesian3.fromDegrees(point.lon, point.lat)
    );

    // 去除重复的点
    const cartesianPositions = trackPoints.filter(
      (point: any, index: number, self: any) =>
        index === 0 || !Cesium.Cartesian3.equals(point, self[index - 1])
    );

    // const trackDataSource = new Cesium.CustomDataSource("trackDataSource");
    // viewer.dataSources.add(trackDataSource);

    // 创建一条折线来连接这些点
    viewer.entities.add({
      polyline: {
        positions: new Cesium.CallbackProperty(() => cartesianPositions, false),
        width: 4,
        material: new Cesium.ColorMaterialProperty(Cesium.Color.BLUE),
      },
    });

    // 给第一个点添加绿色水滴背景和"S"标记
    if (trackPointsArr.length > 0) {
      const firstPointPosition = Cesium.Cartesian3.fromDegrees(
        trackPointsArr[0].lon,
        trackPointsArr[0].lat
      );
      viewer.entities.add({
        position: firstPointPosition,
        billboard: {
          image: startBg, // 替换为实际路径
          width: 54,
          height: 70,
          scale: 0.5,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 确保图片底部中心指向坐标点
        },
      });
    }

    // // 给最后一个点添加红色水滴背景和"E"标记
    if (trackPointsArr.length > 1) {
      const lastPointPosition = Cesium.Cartesian3.fromDegrees(
        trackPointsArr[trackPointsArr.length - 1].lon,
        trackPointsArr[trackPointsArr.length - 1].lat
      );
      viewer.entities.add({
        position: lastPointPosition,
        billboard: {
          image: endBg, // 替换为实际路径
          width: 54,
          height: 70,
          scale: 0.5,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 确保图片底部中心指向坐标点
        },
      });
    }

    flyToEntitiesCenter(trackPointsArr);
  };

  // 添加带移动点位的轨迹
  const addSampleTrack = (trackPointsArr: any) => {
    clearMap();
    // 将坐标转换为 Cartesian3 格式，并将时间戳转换为 JulianDate
    const positions: {
      position: Cesium.Cartesian3;
      time: Cesium.JulianDate;
    }[] = trackPointsArr.map((point: any) => {
      const dateTimeString = point.triggerTime;
      const date = new Date(dateTimeString);
      const time = Cesium.JulianDate.fromDate(date);
      return {
        position: Cesium.Cartesian3.fromDegrees(point.lon, point.lat),
        time: time,
      };
    });

    // 创建一条折线来连接这些点
    // viewer.entities.add({
    //   polyline: {
    //     positions: new Cesium.CallbackProperty(() => positions, false),
    //     width: 3,
    //     material: Cesium.Color.BLUE,
    //   },
    // });

    // // 给第一个点添加绿色水滴背景和"S"标记
    // if (trackPoints.length > 0) {
    //   const firstPointPosition = Cesium.Cartesian3.fromDegrees(
    //     trackPoints[0].lon,
    //     trackPoints[0].lat
    //   );
    //   viewer.entities.add({
    //     position: firstPointPosition,
    //     billboard: {
    //       image: startBg, // 替换为实际路径
    //       width: 100,
    //       height: 100,
    //       scale: 0.5,
    //       verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 确保图片底部中心指向坐标点
    //     },
    //   });
    //   viewer.entities.add({
    //     position: firstPointPosition,
    //     label: {
    //       text: "S",
    //       font: "24px sans-serif",
    //       fillColor: Cesium.Color.WHITE,
    //       outlineColor: Cesium.Color.BLACK,
    //       outlineWidth: 2,
    //       style: Cesium.LabelStyle.FILL_AND_OUTLINE,
    //       pixelOffset: new Cesium.Cartesian2(1, -32), // 调整文本位置，确保文字位于图片中心
    //     },
    //   });
    // }

    // // 给最后一个点添加红色水滴背景和"E"标记
    // if (trackPoints.length > 1) {
    //   const lastPointPosition = Cesium.Cartesian3.fromDegrees(
    //     trackPoints[trackPoints.length - 1].lon,
    //     trackPoints[trackPoints.length - 1].lat
    //   );
    //   viewer.entities.add({
    //     position: lastPointPosition,
    //     billboard: {
    //       image: endBg, // 替换为实际路径
    //       width: 100,
    //       height: 100,
    //       scale: 0.5,
    //       verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 确保图片底部中心指向坐标点
    //     },
    //   });
    //   viewer.entities.add({
    //     position: lastPointPosition,
    //     label: {
    //       text: "E",
    //       font: "24px sans-serif",
    //       fillColor: Cesium.Color.WHITE,
    //       outlineColor: Cesium.Color.BLACK,
    //       outlineWidth: 2,
    //       style: Cesium.LabelStyle.FILL_AND_OUTLINE,
    //       pixelOffset: new Cesium.Cartesian2(1, -32), // 调整文本位置，确保文字位于图片中心
    //     },
    //   });
    // }

    // 创建 SampledPositionProperty 对象
    const positionProperty = new Cesium.SampledPositionProperty();

    // 向 SampledPositionProperty 添加样本点
    positions.forEach(({ position, time }) => {
      positionProperty.addSample(time, position);
    });

    // 设置时钟范围以匹配数据集的时间范围
    const startTime = positions[0].time;
    const stopTime = positions[positions.length - 1].time;
    viewer.clock.startTime = startTime.clone();
    viewer.clock.stopTime = stopTime.clone();
    viewer.clock.currentTime = startTime.clone();
    viewer.timeline.zoomTo(startTime, stopTime);

    // 创建一个跟随路径的实体
    viewer.entities.add({
      availability: new Cesium.TimeIntervalCollection([
        new Cesium.TimeInterval({
          start: startTime,
          stop: stopTime,
        }),
      ]),
      position: positionProperty,
      billboard: {
        image: vehicle, // 使用相对路径引入图片资源
        width: 40,
        height: 40,
      },
      path: {
        resolution: 1,
        material: new Cesium.PolylineGlowMaterialProperty({
          glowPower: 0.1,
          color: Cesium.Color.YELLOW,
        }),
        width: 10,
        leadTime: 0,
        trailTime: 10,
        show: true,
      },
    });

    // 获取所有位置用于飞行到中心
    const cartesianPositions = positions.map((p) => p.position);
    flyToEntitiesCenter(cartesianPositions);
  };

  const isPlaying = ref(false);
  let samplestartTime = ref();
  const initialPoint = ref(); // 起始点
  // 在 addSample 中保存原始 position property
  const originalPositionProperty = ref<Cesium.SampledPositionProperty | null>(null);
  // 添加移动点位
  const addSample = (trackPointsArr: any) => {
    console.log(trackPointsArr);
    
    const positionProperty = new Cesium.SampledPositionProperty();
    originalPositionProperty.value = positionProperty; // 直接赋值给外部变量=

    // 记录起始点用于返回
    initialPoint.value = trackPointsArr[0];

    const startTime = Cesium.JulianDate.fromIso8601(
      trackPointsArr[0].triggerTimeT
    );
    samplestartTime.value = trackPointsArr[0].triggerTimeT;
    const endTime = Cesium.JulianDate.fromIso8601(
      trackPointsArr[trackPointsArr.length - 1].triggerTimeT
    );
    trackPointsArr.forEach((point: any) => {
      const time = Cesium.JulianDate.fromIso8601(point.triggerTimeT);
      const position = Cesium.Cartesian3.fromDegrees(point.lon, point.lat);
      if (positionProperty) {
        positionProperty.addSample(time, position);
      }
    });

    vehicleEntity.value = viewer.entities.add({
      id: "moveSample1",
      availability: new Cesium.TimeIntervalCollection([
        new Cesium.TimeInterval({
          start: startTime,
          stop: endTime,
        }),
      ]),
      position: positionProperty,
      billboard: {
        image: vehicle, // 替换为你的背景图片路径
        width: 60,
        height: 60,
        scale: 0.5,
      },
    });

    // 设置viewer的起始时间
    viewer.clock.startTime = startTime;
    // 设置viewer的结束时间
    viewer.clock.stopTime = endTime;
    // 将当前时间设置为起始时间
    viewer.clock.currentTime = viewer.clock.startTime;
    // 设置viewer的动画状态为暂停
    viewer.clock.shouldAnimate = false;
    // 监听时钟的tick事件，在动画结束时返回原点
    viewer.clock.onTick.addEventListener((clock: any) => {
      if (Cesium.JulianDate.equalsEpsilon(clock.currentTime, endTime, 0.2)) {
        console.log("播放结束");
        // 回到起始点
        // const initialPosition = Cesium.Cartesian3.fromDegrees(
        //   trackPointsArr[0].lon,
        //   trackPointsArr[0].lat
        // );
        // // 返回到起始位置
        // vehicleEntity.value.position = new Cesium.ConstantPositionProperty(
        //   initialPosition
        // );
        // viewer.clock.currentTime = viewer.clock.startTime;

        // 停留在最后一点
        // 获取最后一个点的位置
        // const endPosition = Cesium.Cartesian3.fromDegrees(
        //   trackPointsArr[trackPointsArr.length - 1].lon,
        //   trackPointsArr[trackPointsArr.length - 1].lat
        // );

        // 将 position 设置为 ConstantPositionProperty，保持在终点
        // vehicleEntity.value.position = new Cesium.ConstantPositionProperty(
        //   endPosition
        // );

        // 停止动画，但保留实体
        isPlaying.value = false;
        viewer.clock.shouldAnimate = false;

        // 可选：保持当前时间为结束时间
        viewer.clock.currentTime = endTime;
      }
    });
  };

  const setAnimateTime = (time: any) => {
    console.log(time);
    if (samplestartTime.value === time) {
      viewer.clock.currentTime = viewer.clock.startTime;
    } else {
      viewer.clock.currentTime = Cesium.JulianDate.fromIso8601(time);
    }
  };

  // 动画开始
  const startAnimate = () => {
    isPlaying.value = true;
    viewer.clock.shouldAnimate = true;
  };

  // 动画暂停
  const pauseAnimate = () => {
    isPlaying.value = false;
    viewer.clock.shouldAnimate = false;
  };

  // 重新开始动画时恢复 SampledPositionProperty
  const restartAnimation = () => {
    console.log("重新开始动画");
    // console.log(originalPositionProperty.value);
    
    // if (originalPositionProperty.value) {
    //   vehicleEntity.value.position = originalPositionProperty.value;
    // }

    viewer.clock.currentTime = viewer.clock.startTime;
    viewer.clock.shouldAnimate = true;
    isPlaying.value = true;
  };

  // 回到起始点
  const goBackStart = () => {
    console.log("回到起始点");
    if (!viewer) return;
    if (initialPoint.value) {
      const initialPosition = Cesium.Cartesian3.fromDegrees(
        initialPoint.value.lon,
        initialPoint.value.lat
      );
      // 返回到起始位置
      vehicleEntity.value.position = new Cesium.ConstantPositionProperty(
        initialPosition
      );
      isPlaying.value = false;
      viewer.clock.shouldAnimate = false;
      viewer.clock.currentTime = viewer.clock.startTime;
    }
  };

  // 切换时钟状态
  const toggleClock = () => {
    console.log(isPlaying.value);
    isPlaying.value = !isPlaying.value;
    console.log(isPlaying.value);
    if (isPlaying.value) {
      viewer.clock.shouldAnimate = true; // 恢复时钟动画
    } else {
      viewer.clock.shouldAnimate = false; // 暂停时钟动画
    }
  };

  // 动画跳转到某时间点位置
  const jumpToTime = (time: any) => {
    console.log(time);

    const targetTime = Cesium.JulianDate.fromIso8601(time);
    if (
      Cesium.JulianDate.compare(targetTime, viewer.clock.startTime) >= 0 &&
      Cesium.JulianDate.compare(targetTime, viewer.clock.stopTime) <= 0
    ) {
      viewer.clock.currentTime = targetTime;
      viewer.clock.shouldAnimate = false; // 跳转后暂停动画
    }
  };

  // 添加样本点
  const addSamplePoint = (pointData: any) => {
    if (!viewer) return;
    removeEntityById("movePoint1");
    // 校验经纬度是否为有效数字
    const lon = parseFloat(pointData.lon);
    const lat = parseFloat(pointData.lat);

    if (!pointData || !lon || !lat) {
      console.error("点位数据错误:", pointData);
      return;
    }
    if (isNaN(lon) || isNaN(lat)) {
      console.error("Invalid longitude or latitude:", pointData);
      return;
    }
    const position = Cesium.Cartesian3.fromDegrees(lon, lat);
    viewer.entities.add({
      id: "movePoint1",
      position: position,
      billboard: {
        image: vehicle, // 替换为你的背景图片路径
        width: 60,
        height: 60,
        scale: 0.5,
      },
    });

    // 移动到最新的点位
    // viewer.zoomTo(viewer.entities);
    viewer.camera.setView(viewer.entities, {
      offset: {
        heading: Cesium.Math.toRadians(0), // 正北方向
        pitch: Cesium.Math.toRadians(-90), // 垂直向下
        roll: 0,
      },
    });
  };

  const removeEntityById = (id: any) => {
    const entity = viewer.entities.getById(id);
    if (entity) {
      viewer.entities.remove(entity);
    } else {
      console.warn(`未找到ID为 ${id} 的实体`);
    }
  };

  // 清除地图上的所有内容
  const clearMap = () => {
    if (viewer.entities) {
      viewer.entities.removeAll(); // 移除所有实体
    }
    if (viewer.dataSources.length > 0) {
      viewer.dataSources.removeAll();
    }
    removeEvent(); // 清除弹窗监听事件，如果有的话
    closeMapPopup(); // 关闭可能存在的弹窗
  };

  return {
    addpoint,
    addpointNoLabel,
    setEvent,
    setPopEvent,
    flyToEntitiesCenter,
    popFlag,
    popData,
    mapPopup,
    closeMapPopup,
    addTrack,
    addSampleTrack,
    clearMap,
    toggleClock,
    addSample,
    startAnimate,
    pauseAnimate,
    jumpToTime,
    addSamplePoint,
    setAnimateTime,
    goBackStart,
    restartAnimation,
  };
}
