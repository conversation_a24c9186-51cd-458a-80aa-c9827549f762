<template>
  <div class="workRecord workRecordContainer custom-scrollbar">
    <div class="recordListWrap">
      <h5>作业记录</h5>
      <div class="filter">
        <a-date-picker
          v-model:value="workDate"
          style="width: 100%;" 
          @change="workDateChange"
        />
        <a-select
          ref="select"
          v-model:value="vehicleId"
          style="width: 100%"
          placeholder="请选择作业设备"
          @change="vehicleChange"
        >
          <a-select-option :value="'all'">全部检测设备</a-select-option>
          <a-select-option v-for="item in vehicleList" :key="item.id" :value="item.id">{{ item.name }}</a-select-option>
        </a-select>
        <a-select
          ref="select"
          v-if="vehicleId"
          v-model:value="airlineId"
          style="width: 100%"
          placeholder="请选择检测路线"
          allowClear
          @change="airlineChange"
        >
          <a-select-option :value="'all'">全部检测路线</a-select-option>
          <a-select-option v-for="item in airlineList" :key="item.id" :value="item.id">{{ item.name }}</a-select-option>
        </a-select>
      </div>
      <ul class="recordList">
        <li :class="['recordItem', item.id === selectedRecord?.id ? 'recordItemActive' : '']" v-for="item in recordList" :key="item.id" @click="recordSelect(item)">
          <div class="recordTimeRange">
            <div class="startTime">{{ item.startTime }}</div>
            <em>—</em>
            <div class="endTime">{{ item.endTime ? item.endTime : '正在执行' }}</div>
          </div>
          <div class="recordInfo">
            <div class="recordInfoItem">
              <HistoryOutlined />
              <span>{{ item.endTime ? ((item.totalDuration || 0) + 's') : '-' }}</span>
              </div>
            <div class="recordInfoItem">
              <BellOutlined />
              <span>{{ item.riskCount || 0 }}</span>
            </div>
            <div class="recordInfoItem">
              <UserOutlined />
              <span>{{ item.actualName }}</span>
            </div>
          </div>
        </li>
      </ul>
    </div>
    <div class="recordInfosWrap" v-if="recordDetails.jobRecordVO && recordDetails.jobRecordVO.id">
      <div class="devInfo">
        <div class="devImg"><img src="@/assets/images/m350.png" alt=""></div>
        <div class="devName">
          <div class="name">{{ recordDetails.jobRecordVO.vehicleName }}</div>
          <div class="handleBtns">
            <div class="btnItem" @click="testReportOpen">检测报告</div>
            <div class="btnItem" v-if="recordDetails.vehicleVideoVOList && recordDetails.vehicleVideoVOList.length" @click="videoOpen">全程视频</div>
          </div>
        </div>
      </div>
      <div class="recordDataInfos">
        <div class="dataInfoItem">
          <p class="dataInfoItemTitle">检测目标</p>
          <p class="dataInfoItemContent">{{ recordDetails.detectTargetName || '-' }}</p>
        </div>
        <div class="dataInfoItem densityItem">
          <p class="dataInfoItemTitle">当前浓度/最大浓度（{{ currentRecordBaseData.detectTargetUnit }}）</p>
          <p class="dataInfoItemContent" @click="queryDensityAnalyse(currentRecordBaseData)">{{ currentRecordBaseData.maxValue || currentRecordBaseData.maxValue === 0 ? currentRecordBaseData.maxValue : '-' }}/{{ recordDetails.maxValueVO.endMaxValue || recordDetails.maxValueVO.endMaxValue === 0 ? recordDetails.maxValueVO.endMaxValue : '-' }}</p>
        </div>
        <div class="dataInfoItem">
          <p class="dataInfoItemTitle">当前风险等级/最大风险等级</p>
          <p class="dataInfoItemContent">
            {{ currentRecordBaseData.gradeName || '-' }}/{{ recordDetails.maxValueVO.endMaxGradeName || '-' }}
            <a-tooltip placement="bottom">
              <template #title>
                <p v-for="item in recordDetails.alarmRuleGradeVOList" :key="item.id">{{ item.gradeName || '-' }} : {{ item.alarmValue || '-' }}</p>
              </template>
              <InfoCircleOutlined />
            </a-tooltip>
          </p>
        </div>
        <div class="dataInfoItem">
          <p class="dataInfoItemTitle">当前高度/最大高度（m）</p>
          <p class="dataInfoItemContent">{{ currentRecordBaseData.h || currentRecordBaseData.h === 0 ? currentRecordBaseData.h : '-' }}/{{ (recordDetails.maxValueVO.endMaxHeight || recordDetails.maxValueVO.endMaxHeight === 0) ? parseFloat((recordDetails.maxValueVO.endMaxHeight).toFixed(2)) : '-' }}</p>
        </div>
        <div class="dataInfoItem">
          <p class="dataInfoItemTitle">当前距离/最大距离（m）</p>
          <p class="dataInfoItemContent">{{ currentRecordBaseData.distance || currentRecordBaseData.distance === 0 ? currentRecordBaseData.distance : '-' }}/{{ recordDetails.maxValueVO.endMaxDistance || recordDetails.maxValueVO.endMaxDistance === 0 ? recordDetails.maxValueVO.endMaxDistance : '-' }}</p>
        </div>
        <div class="dataInfoItem">
          <p class="dataInfoItemTitle">速度/最大速度（m/s）</p>
          <p class="dataInfoItemContent">{{ currentRecordBaseData.speed || currentRecordBaseData.speed === 0 ? currentRecordBaseData.speed : '-' }}/{{ recordDetails.maxValueVO.endMaxSpeed || recordDetails.maxValueVO.endMaxSpeed === 0 ? recordDetails.maxValueVO.endMaxSpeed : '-' }}</p>
        </div>
      </div>
    </div>
    <div class="progressWrap" v-if="recordDetails.jobRecordVO && recordDetails.jobRecordVO.id">
      <a-spin :spinning="videoLoading">
          <PlayerTimeline 
          ref="playerTimeline"
          v-model:time="currentPlaybackTime" 
          :duration="duration" 
          :startTime="selectedRecord.startTime || ''" 
          :alarmData="alarmDataList"
          @toggleStatus="togglePlay"
          @dateTimeChange="dateTimeChange"
        />
      </a-spin>
    </div>
    <div v-if="videoIsShow" :class="['videoWrap', videoFullScreen ? 'videoFullScreenWrap' : '']">
      <a-spin :spinning="videoLoading">
        <div class="videoTitle">
          <h6>DJI_20240930145116_0007_V</h6>
          <div class="videoBtns">
            <ExpandOutlined v-if="!videoFullScreen" @click="videoScreenChange" />
            <CompressOutlined v-else @click="videoScreenChange" />
            <CloseOutlined @click="videoClose"/>
          </div>
        </div>
        <div class="videoContent">
          <!-- <video id="videoPlayer" controls></video> -->
          <video id="videoPlayer"></video>
          <!-- <video ref="videoPlayer">
        <source :src="CURRENT_CONFIG.baseURL + '/' + currentRecordVideoData.videoPath">
        您的浏览器不支持 video 标签。
      </video> -->
        </div>
      </a-spin>
    </div>
    <div class="monitorResult" v-if="monitorResulShow">
      <div class="monitorResultTitle">
        <h5>检测结果</h5>
        <CloseOutlined @click="monitorResultClose"/>
      </div>
      <div class="hasNoData" v-if="!monitorResultData.startTime">该点无检测数据</div>
      <div class="monitorResoultContent custom-scrollbar" v-else>
        <div class="monitorInfo">
          <div class="monitorInfoItem">
            <em>检测点：</em>
            <span>{{ monitorResultData.pointName || '-' }}</span>
          </div>
          <div class="monitorInfoItem addressItem">
            <em>检测地址：</em>
            <span>{{ monitorResultData.baseDataVO ? monitorResultData.baseDataVO.address : '-' }}{{ monitorResultData.baseDataVO ? ('【' + monitorResultData.baseDataVO.lat + '° N') : '' }}   {{ monitorResultData.baseDataVO ? (monitorResultData.baseDataVO.lon + '° E】') : '' }}</span>
          </div>
          <div class="monitorInfoItem">
            <em>开始时间：</em>
            <span>{{ monitorResultData.startTime }}</span>
          </div>
          <div class="monitorInfoItem">
            <em>结束时间：</em>
            <span>{{ monitorResultData.endTime }}</span>
          </div>
          <div class="monitorInfoItem">
            <em>风险等级：</em>
            <span>{{ monitorResultData.gradeName || '-' }}</span>
          </div>
          <div class="monitorInfoItem">
            <em>最大浓度：</em>
            <span>{{ (monitorResultData.baseDataVO ? (monitorResultData.baseDataVO.maxValue + monitorResultData.baseDataVO.detectTargetUnit) : '-') + (monitorResultData.detectTargetUnit || '') }}</span>
          </div>
        </div>
        <div class="monitorData">
          <h6>浓度趋势</h6>
          <div class="chart">
            <EChart refName="monitorResultChart" :options="monitorResultOption" />
          </div>
        </div>
        <div class="monitorImgs">
          <h6>隐患图片</h6>
          <div class="imgs">
            <div class="imgItem" v-for="(item, index) in monitorResultData.iamgeList" :key="index">
              <a-image :src="CURRENT_CONFIG.baseURL + item.imagePath" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="densityAnalysis" v-if="densityAnalysisShow">
      <div class="densityAnalysistTitle">
        <h5>浓度检测分析</h5>
        <CloseOutlined @click="densityAnalysisClose"/>
      </div>
      <div class="monitorResoultContent custom-scrollbar">
        <div class="monitorInfo">
          <div class="monitorInfoItem">
            <em>开始时间：</em>
            <span>{{ densityAnalysisData.startTime }}</span>
          </div>
          <div class="monitorInfoItem">
            <em>结束时间：</em>
            <span>{{ densityAnalysisData.endTime }}</span>
          </div>
          <div class="monitorInfoItem">
            <em>风险等级：</em>
            <span>{{ densityAnalysisData.maxGradeName }}</span>
          </div>
          <div class="monitorInfoItem">
            <em>最大浓度：</em>
            <span>{{ densityAnalysisData.maxValue + currentDensityAnalyse.detectTargetUnit }}</span>
          </div>
        </div>
        <div class="monitorData">
          <h6>浓度趋势</h6>
          <div class="chart">
            <EChart refName="densityAnalysisChart" :options="densityAnalysisOption" />
          </div>
        </div>
      </div>
    </div>
    <div class="testReport" v-if="testReportShow">
      <TestReport :testReport="testReport" @closeReport="testReportClose" />
    </div>
    <div class="pageLoadingWrap" v-if="pageLoading">
      <a-spin :spinning="pageLoading">
        <div class="pageLoadingBox"></div>
      </a-spin>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, computed, watch } from "vue";
import {
  HistoryOutlined,
  BellOutlined,
  UserOutlined,
  ExpandOutlined,
  CompressOutlined,
  CloseOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue';
import TestReport from '@/components/workspace/TestReport.vue';
import EChart from '@/components/common/EChart.vue';
import PlayerTimeline from './PlayerTimeline.vue';
import { workspaceApi } from '@/api/workspace/workspaceApi'
import { ruleApi } from '@/api/manageTool/ruleApi'
import moment from "moment";
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { viewer } from '@/components/cesiumMap/initCesium';
import { useCesium } from '@/components/cesiumMap/useCesium';
import { useStore } from 'vuex'
import { CURRENT_CONFIG } from '@/api/http/config'
import flvjs from 'flv.js';
import { useRoute } from "vue-router";
import { message } from 'ant-design-vue'

const { clearMap, setAnimateTime, addSamplePoint } = useCesium();


defineOptions({ 
  components: {
    HistoryOutlined,
    BellOutlined,
    UserOutlined,
    ExpandOutlined,
    CompressOutlined,
    CloseOutlined,
    PlayerTimeline,
    InfoCircleOutlined,
    TestReport,
    EChart
  }
})
const emit = defineEmits(['mapDataChange'])
const store = useStore()
const route = useRoute();

const pageLoading = ref(false);

const projectSelectedId = route.query.id;

const workDate = ref<Dayjs>();
const vehicleId = ref<string>('all');
const airlineId = ref<string | null>(null);
const vehicleList = ref<any>([]);
const selectedJob = ref<any>()

const workDateChange = (date: any) => {
  localStorage.removeItem('selectedJob')
  selectedRecord.value = {}
  recordDetails.value = {}
  if (viewer) {
    clearMap()
  }
  currentPlaybackTime.value = 0
  videoIsShow.value = false
  testReportShow.value = false
  densityAnalysisShow.value = false
  monitorResulShow.value = false
  workDate.value = date;
  queryRecords()
};
const queryVehicleList = async () => {
  try {
    const result = await workspaceApi.getVehicleListHasRecord(projectSelectedId)
    vehicleList.value = [...result.data]
    // vehicleId.value = vehicleList.value[0].id
    queryRecords()
  } catch (e) {
    vehicleList.value = []
  } finally {
  }
}
const selectedVehicle = computed(() => {
  return vehicleList.value.find((item: any) => item.id === vehicleId.value)
})
const vehicleChange = (item: any) => {
  localStorage.removeItem('selectedJob')
  selectedRecord.value = {}
  recordDetails.value = {}
  if (viewer) {
    clearMap()
  }
  currentPlaybackTime.value = 0
  videoIsShow.value = false
  testReportShow.value = false
  densityAnalysisShow.value = false
  monitorResulShow.value = false
  airlineList.value = []
  airlineId.value = null
  queryAirlineList()
  queryRecords()
};
const airlineList = ref<any>([]);
const queryAirlineList = async () => {
  try {
    const result = await workspaceApi.getAirlineListHasRecord(projectSelectedId, {
      vehicleId: vehicleId.value === 'all' ? '' : vehicleId.value
    })
    airlineList.value = [...result.data]
  } catch (e) {
    airlineList.value = []
  } finally {
  }
}
const airlineChange = () => {
  localStorage.removeItem('selectedJob')
  selectedRecord.value = {}
  recordDetails.value = {}
  if (viewer) {
    clearMap()
  }
  currentPlaybackTime.value = 0
  videoIsShow.value = false
  testReportShow.value = false
  densityAnalysisShow.value = false
  monitorResulShow.value = false
  queryRecords()
}

const recordList = ref<any>([]);
const loading = ref(false);
const queryRecords = async () => {
  try {
    loading.value = true
    const result = await workspaceApi.getWorkRecords({
      projectId: projectSelectedId,
      date: workDate.value ? workDate.value.format('YYYY-MM-DD') : '',
      vehicleId: !vehicleId.value || (vehicleId.value === 'all') ? '' : vehicleId.value,
      airlineId: !airlineId.value || (airlineId.value === 'all') ? '' : airlineId.value,
      type: airlineId.value ? '' : 0
    })
    recordList.value = [...result.data]
    if(selectedJob.value) {
      selectedRecord.value = recordList.value.filter((item: any) => item.id === selectedJob.value.jobId)[0]
    }
  } catch (e) {
    recordList.value = []
  } finally {
    loading.value = false
  }
}

const selectedRecord = ref<any>({
  id: ''
})
const recordDetails = ref<any>({})
// 进度条返回时间
const currentPlaybackTime = ref<any>(0)

const recordSelect = (record: any) => {
  selectedRecord.value = {}
  recordDetails.value = {}
  if (viewer) {
    clearMap()
  }
  currentPlaybackTime.value = 0
  videoIsShow.value = false
  testReportShow.value = false
  densityAnalysisShow.value = false
  monitorResulShow.value = false
  if (!record.endTime) {
    message.error('任务正在执行中')
    return
  }
  selectedRecord.value = {...record}
  queryRecordDetail()
}

const currentRecordBaseData = ref<any>({})
const recordVideoData = ref<any>([])
const detectionData = ref<any>([])
const duration = ref<number>(0);
// 时间轴展示的报警数据
const alarmDataList = ref<any>([])

// 时间转成秒
const timeToSeconds = (timeStr: string) => {
  const [hours, minutes, seconds] = timeStr.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds;
}

// 计算时间差
const getTimeDiffInSeconds = (start: string, end: string) => {
  const startSeconds = timeToSeconds(start.split(' ')[1]);
  const endSeconds = timeToSeconds(end.split(' ')[1]);
  return endSeconds - startSeconds;
}

// 格式化函数
const formatDate = (date: any) => {
  const pad = (n: any) => n.toString().padStart(2, '0');
  return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
}

// 补全baseData数据
const completeBaseData = (data: any) => {
  console.log(data);
  if (!data) return []
  const result: any = [...data]
  const startTimeDiff = getTimeDiffInSeconds(selectedRecord.value.startTime, data[0].triggerTime)
  const endTimeDiff = getTimeDiffInSeconds(data[data.length - 1].triggerTime, selectedRecord.value.endTime)
  console.log(startTimeDiff, endTimeDiff);
  if(startTimeDiff > 0) {
    for (let i = 1; i <= startTimeDiff; i++) {
      console.log(data[0].triggerTime);
      const newDate = formatDate(new Date(new Date(data[0].triggerTime).getTime() - (i * 1000)));
      const newDateT = newDate.replace(' ', 'T') + '.000Z';
      const newData = { ...data[0], triggerTime: newDate, triggerTimeT: newDateT };
      result.unshift(newData);
    }
  }
  if(endTimeDiff > 0) {
    for (let i = 1; i <= endTimeDiff; i++) {
      const newDate = formatDate(new Date(new Date(data[data.length - 1].triggerTime).getTime() + (i * 1000)));
      const newDateT = newDate.replace(' ', 'T') + '.000Z';
      const newData = { ...data[data.length - 1], triggerTime: newDate, triggerTimeT: newDateT };
      result.push(newData);
    }
  }
  console.log(result);
  return result
}

const queryRecordDetail = async (jobId?: string) => {
  pageLoading.value = true
  try {
    if (selectedRecord.value) {
      const result = await workspaceApi.getWorkRecordDetail({
        projectId: projectSelectedId,
        jobId: jobId ? jobId : selectedRecord.value.id
      })
      recordDetails.value = {...result.data}
      console.log(recordDetails.value);
      const completeBaseDataList = completeBaseData(result.data.baseDataVOList)
      recordDetails.value.baseDataVOList = [...completeBaseDataList]
      recordDetails.value.maxValueVO = recordDetails.value.maxValueVO || {} //防止返回null报错
      emit('mapDataChange', 'record', recordDetails.value)
      // detectionData.value = recordDetails.value.baseDataVOList || []
      detectionData.value = [...completeBaseDataList]
      currentRecordBaseData.value = recordDetails.value.baseDataVOList[0] || {}
      duration.value = recordDetails.value.totalDuration
      recordVideoData.value = recordDetails.value.vehicleVideoVOList || []

      // 报警数据汇总
      const airlineData = recordDetails.value.airlineDataVOList || []
      const alarmAirlineData = airlineData.map((item: any) => {
        const itemAlarmData = { ...item.alarmVO }
        delete itemAlarmData.id
        const dataItem = { ...item, ...itemAlarmData }
        if (dataItem.alarmVO && dataItem.gradeId) {
          return dataItem
        }
      })
      alarmDataList.value = recordDetails.value.alarmDataVOList ? [...recordDetails.value.alarmDataVOList, ...alarmAirlineData] : []
    }
  } catch (e) {
    console.log(e);
    pageLoading.value = false
    recordDetails.value = {}
  } finally {
    pageLoading.value = false
    loading.value = false
  }
}

const videoIsShow = ref(false)
const playerTimeline = ref<HTMLVideoElement | null>(null);

const videoOpen = () => {
  if(videoIsShow.value) return
  videoIsShow.value = true
  if (playerTimeline.value) {
    playerTimeline.value.pause();
  }
  setTimeout(() => {
    creatvideoFlvPlayer()
  }, 100);
}
const videoClose = () => {
  if (videoflvPlayer.value) {
    videoflvPlayer.value.pause(); // 首先暂停播放
    videoflvPlayer.value.unload(); // 卸载播放器内容
    videoflvPlayer.value.destroy(); // 销毁播放器实例
    videoflvPlayer.value = null; // 清空对播放器实例的引用，以便明确其已被销毁
  }
  videoIsShow.value = false
}

const videoflvPlayer = ref<flvjs.Player | null>(null);
const videoPlayerDom = ref<HTMLVideoElement | null>(null);
const videoIndex = ref(0);
const isFirstStart = ref(true);
const videoLoading = ref(false);
const creatvideoFlvPlayer = () => {
  videoLoading.value = true
  if (videoflvPlayer.value) {
    videoflvPlayer.value.destroy(); // 销毁之前的播放器实例
  }
  videoPlayerDom.value = document.getElementById('videoPlayer') as HTMLVideoElement;
  const currentUrl = CURRENT_CONFIG.baseURL + recordVideoData.value[0].videoPath;
  if (flvjs.isSupported() && videoPlayerDom) {
    videoflvPlayer.value = flvjs.createPlayer({
      type: 'mp4',
      hasAudio: false,
      isLive: false,
      url: currentUrl,
    });
    videoflvPlayer.value.attachMediaElement(videoPlayerDom.value);
    videoflvPlayer.value.load();
    videoPlayerDom.value.currentTime = currentPlaybackTime.value;

    // 设置监听器等待播放器准备就绪
    videoPlayerDom.value.addEventListener('canplaythrough', canplaythrough);

    // 可能还需要监听 loadedmetadata 事件，确保元数据已加载
    // videoPlayerDom.value.addEventListener('loadedmetadata', loadedmetadata);
  }
};

const seekTime = ref(0)
const canplaythrough = () => {
  if (!videoPlayerDom.value) return
  if (!videoflvPlayer.value) return
  videoLoading.value = false

  // 设置从第30秒开始播放
  // var seekTime = 30; // 秒
  // videoPlayerDom.value.currentTime = seekTime.value;

  // 开始播放
  // videoflvPlayer.value.play();
}

const loadedmetadata = () => { 
  if (!videoPlayerDom.value) return
  if (videoPlayerDom.value.duration > 0) { // 确保视频有有效的时长
    var seekTime = 0; // 秒
    if (seekTime < videoPlayerDom.value.duration) {
      videoPlayerDom.value.currentTime = seekTime;
    } else {
      console.error("Seek time exceeds video duration.");
    }
  }
}

const playNextVideo = () => {
  if (!videoflvPlayer.value) return
  videoflvPlayer.value.play();
}

watch(() => currentPlaybackTime.value, (newValue, oldValue) => {
  // if(newValue.value) {
  //   if(videoflvPlayer.value) {
  //     videoflvPlayer.value.currentTime = newValue.value
  //     videoflvPlayer.value.play();
  //   }
  // }
})

const dateTimeChange = (dateTime: string, currentTime: number, type: string) => {
  const filterDataArr = detectionData.value.filter((item: any) => item.triggerTime === dateTime)
  const maxItem = filterDataArr.reduce((prev: any, current: any) =>
    (current.maxValue > prev.maxValue) ? current : prev
  );
  currentRecordBaseData.value = maxItem || currentRecordBaseData.value
  if(type === 'drag') {
    if (videoflvPlayer.value) {
      videoflvPlayer.value.currentTime = currentTime
      // videoflvPlayer.value.play();
    }
    console.log(currentRecordBaseData.value.triggerTimeT);
    setAnimateTime(currentRecordBaseData.value.triggerTimeT)
  }
  // if(!currentRecordBaseData.value.id) return
  // addSamplePoint(currentRecordBaseData.value)
}

const togglePlay = (status: boolean) => {
  if(!videoflvPlayer.value) return
  if (status) {
    if(isFirstStart.value) {
      // playNextVideo()
      videoflvPlayer.value.play()
      isFirstStart.value = false
    } else {
      videoflvPlayer.value.play()
    }
  } else {
    videoflvPlayer.value.pause()
  }
}

const videoFullScreen = ref(false)
const videoScreenChange = () => {
  videoFullScreen.value = !videoFullScreen.value
}

const selectMapPointData = computed(() => {
  return store.state.workspace.clickPointData
})

watch(() => selectMapPointData, (newValue, oldValue) => {
  queryMonitorResult(newValue.value)
}, { deep: true })

const monitorResulShow = ref(false)
const monitorResultOption = ref<any>({})
const monitorResultData = ref<any>({})

const hasField = (obj: any, field: string) => {
  return Object.prototype.hasOwnProperty.call(obj, field);
}

const startIndex = ref(0)
const endIndex = ref(0)
const queryMonitorResult = async (item: any) => {
  console.log(item);
  
  const xAxisData: any[] = []
  const yAxisData: any[] = []
  console.log(hasField(item, 'alarmVO'));
  
  if (hasField(item, 'alarmVO')) {
    try {
      const result = await workspaceApi.getPointDetetionData({
        pointId: item.id,
        baseDataId: item.alarmVO ? item.alarmVO.id : null,
        jobId: item.alarmVO ? null : selectedRecord.value.id
      })
      monitorResultData.value = { ...result.data }
      if(result.data.jobBaseDataVOList) {
        result.data.jobBaseDataVOList.forEach((item: any, index: number) => {
          xAxisData.push(item.triggerTime.split(' ')[1])
          yAxisData.push(item.maxValue)
          if (item.currData && !startIndex.value) {
            startIndex.value = index
          }
          if (!item.currData && startIndex.value && !endIndex.value && (index > startIndex.value)) {
            endIndex.value = index - 1
          }
        })
      }
      monitorResultOpen(xAxisData, yAxisData)
    } catch (e) {
      console.log(e);
    } finally {
    }
  } else {
    try {
      const result = await workspaceApi.getPointDetetionData({
        baseDataId: item.id
      })
      monitorResultData.value = { ...result.data }
      if(result.data.jobBaseDataVOList) {
        result.data.jobBaseDataVOList.forEach((item: any, index: number) => {
          xAxisData.push(item.triggerTime.split(' ')[1])
          yAxisData.push(item.maxValue)
          if (item.currData && !startIndex.value) {
            startIndex.value = index
          }
          if (!item.currData && startIndex.value && !endIndex.value && (index > startIndex.value)) {
            endIndex.value = index - 1
          }
        })
      }
      monitorResultOpen(xAxisData, yAxisData)
    } catch (e) {
      console.log(e);
    } finally {
    }
  }
}

const monitorResultOpen = (xAxisData: any, yAxisData: any) => {
  monitorResulShow.value = true

  if(xAxisData && yAxisData) {
    // 分割数据为三部分
    const part1 = { x: xAxisData.slice(0, startIndex.value), y: yAxisData.slice(0, startIndex.value) }
    const part2 = { x: xAxisData.slice(startIndex.value, endIndex.value + 1), y: yAxisData.slice(startIndex.value, endIndex.value + 1) }
    const part3 = { x: xAxisData.slice(endIndex.value), y: yAxisData.slice(endIndex.value) }
    console.log(part1.x.length, part3.x.length);
    setTimeout(() => {
      // part2 只有一个点时
      if (part2.x.length === 1) {
        console.log(1111111);
        const specialPoint = part2;
        part1.x.push(specialPoint.x[0])
        part1.y.push(specialPoint.y[0])
        console.log(part1.x, part3.x);
        monitorResultOption.value = Object.assign({}, {
          grid: {
            left: '12%',
            right: '12%',
            bottom: '12%',
            top: '10%'
          },
          tooltip: {
            trigger: 'axis', // 关键配置
            formatter: function (params: any) {
              // 确保我们只显示当前序列的提示信息
              let param = params[0] // 默认选择第一个参数作为基础
              return `时间：${param.value[0]}<br/>浓度：${param.value[1]}`
            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xAxisData,
            axisLine: {
              lineStyle: {
                color: '#fff'
              }
            },
          },
          yAxis: {
            type: 'value',
            axisLine: {
              lineStyle: {
                color: '#fff'
              }
            }
          },
          series: [{
            smooth: 0.6,
            type: 'line',
            data: part1.x.map((_: any, index: any) => ({ value: [part1.x[index], part1.y[index]] })),
            lineStyle: {
              color: '#2856ee'
            },
            symbol: 'none' // 隐藏数据点符号
          }, {
            smooth: 0.6,
            type: 'scatter',
            data: part2.x.map((_: any, index: any) => ({ value: [part2.x[index], part2.y[index]] })),
            symbol: 'circle', // 在数据点处显示圆形
            symbolSize: 10, // 圆形大小
            itemStyle: {
              color: '#CE1A0D' // 数据点颜色，这里设置为红色
            }
          }, {
            smooth: 0.6,
            type: 'line',
            data: part3.x.map((_: any, index: any) => ({ value: [part3.x[index], part3.y[index]] })),
            lineStyle: {
              color: '#2856ee'
            },
            symbol: 'none' // 隐藏数据点符号
          }],
        })
      } else {
        console.log(222222);
        monitorResultOption.value = Object.assign({}, {
          grid: {
            left: '12%',
            right: '12%',
            bottom: '12%',
            top: '10%'
          },
          tooltip: {
            trigger: 'axis', // 关键配置
            formatter: function (params: any) {
              // 确保我们只显示当前序列的提示信息
              let param = params[0] // 默认选择第一个参数作为基础
              return `时间：${param.value[0]}<br/>浓度：${param.value[1]}`
            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xAxisData,
            axisLine: {
              lineStyle: {
                color: '#fff'
              }
            },
          },
          yAxis: {
            type: 'value',
            axisLine: {
              lineStyle: {
                color: '#fff'
              }
            }
          },
          series: [{
            smooth: 0.6,
            type: 'line',
            data: part1.x.map((_: any, index: any) => ({ value: [part1.x[index], part1.y[index]] })),
            lineStyle: {
              color: '#2856ee'
            },
            symbol: 'none' // 隐藏数据点符号
          }, {
            smooth: 0.6,
            type: 'line',
            data: part2.x.map((_: any, index: any) => ({ value: [part2.x[index], part2.y[index]] })),
            lineStyle: {
              color: '#CE1A0D'
            },
            symbol: 'none' // 隐藏数据点符号
          }, {
            smooth: 0.6,
            type: 'line',
            data: part3.x.map((_: any, index: any) => ({ value: [part3.x[index], part3.y[index]] })),
            lineStyle: {
              color: '#2856ee'
            },
            symbol: 'none' // 隐藏数据点符号
          }]
        })
      }
    },200)
  }
}
const monitorResultClose = () => {
  monitorResulShow.value = false
  monitorResultOption.value = {}
}

const densityAnalysisShow = ref(false)
const densityAnalysisOption = ref<any>({})
const densityAnalysisData = ref<any>({})
const currentDensityAnalyse = ref<any>({})
const queryDensityAnalyse = async (item: any) => {
  currentDensityAnalyse.value = { ...item }
  try {
    const result = await workspaceApi.getPointDetetionAnalyseData({
      jobId: item.jobId,
      triggerTime: item.triggerTime
    })
    densityAnalysisData.value = { ...result.data }
    const xAxisData: any[] = []
    const yAxisData: any[] = []
    xAxisData.push(densityAnalysisData.value.startTime.split(' ')[1])
    yAxisData.push(0)
    result.data.jobBaseDataVOList.forEach((item: any) => {
      xAxisData.push(item.triggerTime.split(' ')[1])
      yAxisData.push(item.maxValue)
    })
    xAxisData.push(densityAnalysisData.value.endTime.split(' ')[1])
    yAxisData.push(0)
    densityAnalysisOpen(xAxisData, yAxisData)
  } catch (e) {
  } finally {
  }
}
const densityAnalysisOpen = (xAxisData: any, yAxisData: any) => {
  densityAnalysisShow.value = true
  setTimeout(() => {
    densityAnalysisOption.value = Object.assign( {}, {
      grid: {
        left: '12%',
        right: '12%',
        bottom: '12%',
        top: '10%'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xAxisData,
        axisLine: {
          lineStyle: {
            color: '#fff'
          }
        },
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#fff'
          }
        }
      },
      series: [{
        data: yAxisData,
        smooth: 0.6,
        type: 'line',
        itemStyle: {
          color: '#2856ee'
        }
      }]
    })
  }, 200);
}
const densityAnalysisClose = () => {
  densityAnalysisShow.value = false
}

const testReportShow = ref(false)
const testReport = ref<any>({})
const testReportOpen = async () => {
  try {
    if (selectedRecord.value) {
      const result = await workspaceApi.getWorkTestReport({
        jobId: selectedRecord.value.id
      })
      testReport.value = Object.assign({}, result.data ? result.data : {})
      testReportShow.value = true
    }
  } catch (e) {
    testReport.value = {}
  } finally {
  }
}
const testReportClose = () => {
  testReportShow.value = false
}

const initSelectedRecord = () => { 
  const selectedJobStr = localStorage.getItem('selectedJob')
  if (selectedJobStr) {
    try {
      selectedJob.value = JSON.parse(selectedJobStr)
      // 如果需要可选字段 selectedJobId.value，也应做类似判断
      workDate.value = dayjs(selectedJob.value.triggerTime.split(' ')[0])
      vehicleId.value = selectedJob.value.vehicleId
      airlineId.value = selectedJob.value.airlineId
      queryRecordDetail(selectedJob.value.jobId)
    } catch (e) {
      console.error('解析 selectedJob 失败', e)
      selectedJob.value = null
    }
  } else {
    workDate.value = dayjs()
  }
}

onMounted(() => {
  queryVehicleList()
  queryAirlineList()
  initSelectedRecord()
})
</script>
<style lang="scss">
.workRecordContainer{
  height: 100%;
  background: none;
  padding: 10px 6px;

  h5 {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    margin-bottom: 15px;
  }

  .recordListWrap {
    .filter{
      margin-bottom: 10px;
      .ant-picker{
        background: none;
        margin-bottom: 10px;
        border-radius: 0;
        border: 1px solid rgba(255, 255, 255, 0.4);

        .ant-picker-input{
          height: 20px;

          input {
            color: #fff;
          }

          .ant-picker-suffix, .ant-picker-clear {
            color: #fff;
            background: none;
          }
        }
      }

      .ant-select{
        height: 30px;
        background: none;
        border: 1px solid rgba(255, 255, 255, 0.4);
        margin-bottom: 10px;

        .ant-select-selector{
          background: none;
          height: 30px;
          border: none;
          color: #fff;
        }

        .ant-select-selection-placeholder {
          color: #fff !important;
        }

        .ant-select-arrow{
          color: #fff;
        }

        .ant-select-suffix, .ant-select-clear {
          color: #fff;
          background: none;
        }
      }

      .ant-input{
        background: none;
        color: #fff;
      }

    }
    .recordList {
      .recordItem {
        padding: 15px 10px;
        margin-bottom: 10px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
        cursor: pointer;
        .recordTimeRange {
          display: flex;
          align-items: center;
          justify-content: space-between;
          color: #fff;
          margin-bottom: 16px;
          .startTime, .endTime {
            position: relative;
            padding-left: 6px;
            &::before {
              content: '';
              position: absolute;
              top: 50%;
              left: -2px;
              transform: translateY(-50%);
              width: 4px;
              height: 4px;
              background: #03be22;
              border-radius: 50%;
            }
          }

          .endTime{
            padding-left: 10px;
            &::before {
              left: 2px;
              background: red;
            }
          }
        }
        .recordInfo {
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 12px;
          padding: 0 10px;
          .recordInfoItem{
            display: flex;
            align-items: center;
            .anticon {
              margin-right: 6px;
              line-height: 0;
            }
          }
        }

        &.recordItemActive {
          background: rgba(0, 0, 0, 0.3);
        }

        &:hover {
          background: rgba(255, 255, 255, 0.4);
        }
      }
    }
  }

  .recordInfosWrap{
    position: fixed;
    top: 50px;
    left: 400px;
    right: 0;
    height: 100px;
    background: rgba(224, 231, 252, 0.95);
    display: flex;
    align-items: center;
    color: #333;
    padding: 0 20px;
    .devInfo {
      width: 300px;
      display: flex;
      align-items: center;
      gap: 20px;
      .devImg {
        width: 120px;
        height: 80px;
        background: #fff;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .devName {
        .name {
          font-size: 18px;
          margin-bottom: 10px;
        }
        .handleBtns {
          display: flex;
          align-items: center;
          gap: 10px;
          .btnItem {
            font-size: 12px;
            padding: 6px;
            background: rgba(173, 181, 252, 0.4);
            color: #2684ff;
            cursor: pointer;
            text-align: center;
          }
        }
      }
    }

    .recordDataInfos{
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-left: 40px;
      gap: 10px;
      flex: 1;
      .dataInfoItem{
        text-align: center;
        .dataInfoItemTitle {
          margin-bottom: 15px;
        }

        .dataInfoItemContent{
          font-size: 18px;
          font-weight: bold;
        }
      }

      .densityItem{
        .dataInfoItemContent {
          color: #2856ee;
          cursor: pointer;
        }
      }
    }
  }

  .progressWrap{
    position: fixed;
    bottom: 30px;
    left: 400px;
    right: 0;
    height: 65px;
    background: rgba(215, 225, 253, 0.95);
  }

  .videoWrap{
    position: fixed;
    right: 5px;
    bottom: 100px;
    background: #333;
    .videoTitle{
      height: 30px;
      padding: 0 10px;
      display: flex;
      align-items: center;
      color: #fff;
      justify-content: space-between;
      background: #000;
      h6 {
        color: #fff;
        opacity: 0;
      }
      .videoBtns {
        display: flex;
        gap: 10px;
      }
    }

    .videoContent{
      width: 400px;
      height: 220px;
      video {
        width: 100%;
        height: 100%;
      }
    }

    &.videoFullScreenWrap {
      left: 400px;
      top: 150px;
      right: 0;
      bottom: 95px;

      .videoContent{
        width: calc(100vw - 400px);
        height: calc(100vh - 290px);
      }
    }
  }

  .monitorResult{
    position: fixed;
    right: 0;
    bottom: 30px;
    top: 50px;
    width: 600px;
    padding: 10px 20px;
    background: #2e2d2d;
    color: #fff;
    .monitorResultTitle{
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 30px;
      padding-bottom: 10px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      h5 {
        color: #fff;
        margin: 0;
      }
    }

    .hasNoData{
      margin-top: 50px;
      text-align: center;
      font-size: 16px;
    }

    .monitorResoultContent{
      height: calc(100% - 40px);
    }
    .monitorInfo{
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-top: 10px;
      .monitorInfoItem {
        width: 50%;
        margin-bottom: 6px;
        line-height: 20px;
        em {
          vertical-align: middle;
          display: inline-block;
          width: 80px;
        }
        span {
          vertical-align: middle;
          display: inline-block;
          max-width: calc(100% - 80px);
        }
      }
      .addressItem {
        width: 100%;
      }
    }

    .monitorData{
      margin-top: 20px;
      h6 {
        color: #fff;
        font-weight: bold;
        margin-bottom: 10px;
      }
      .chart {
        border: 1px solid rgba(255, 255, 255, 0.2);
        height: 250px;
      }
    }

    .monitorImgs{
      margin-top: 20px;
      h6 {
        color: #fff;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .imgs {
        display: flex;
        gap: 10px;
        .imgItem{
          width: calc(100% / 6);
          cursor: pointer;
        }
      }
    }
  }

  .densityAnalysis {
    position: fixed;
    right: 0;
    bottom: 30px;
    top: 50px;
    width: 600px;
    padding: 10px 20px;
    background: #2e2d2d;
    color: #fff;
    .densityAnalysistTitle{
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 30px;
      padding-bottom: 10px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      h5 {
        color: #fff;
        margin: 0;
      }
    }

    .monitorResoultContent{
      height: calc(100% - 40px);
    }
    .monitorInfo{
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-top: 10px;
      .monitorInfoItem {
        width: 50%;
        margin-bottom: 6px;
        line-height: 20px;
        em {
          vertical-align: middle;
          display: inline-block;
          width: 80px;
        }
        span {
          vertical-align: middle;
          display: inline-block;
          max-width: calc(100% - 80px);
        }
      }
      .addressItem {
        width: 100%;
      }
    }

    .monitorData{
      margin-top: 20px;
      h6 {
        color: #fff;
        font-weight: bold;
        margin-bottom: 10px;
      }
      .chart {
        border: 1px solid rgba(255, 255, 255, 0.2);
        height: 250px;
      }
    }

    .monitorImgs{
      margin-top: 20px;
      h6 {
        color: #fff;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .imgs {
        display: flex;
        gap: 10px;
        .imgItem{
          width: calc(100% / 6);
          cursor: pointer;
        }
      }
    }
  }

  .testReport{
    position: fixed;
    right: 0;
    bottom: 30px;
    top: 50px;
    left: 400px;
    background: #fff;
  }

  .pageLoadingWrap{
    position: fixed;
    top: 50px;
    bottom: 30px;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    .ant-spin-spinning{
      max-height: 100%;
    }
    .pageLoadingBox{
      width: 100px;
      height: calc(100vh - 80px);
      margin: 0 auto;
    }
  }
}
</style>
