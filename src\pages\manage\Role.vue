<template>
  <div class="role roleContainer">
    <div class="handleWrap clearfix">
      <div class="left fl">
        <a-form layout="inline" :model="searchFormState">
          <a-form-item>
            <a-input v-model:value="searchFormState.roleName" placeholder="请输入角色名称" size="small" allowClear @pressEnter="onSearch"></a-input>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="onSearch">搜索</a-button>
          </a-form-item>
        </a-form>
      </div>
      <div class="right fr">
        <a-button type="primary" @click="addHandle" v-privilege="'system:role:add'">
          <template #icon>
            <PlusOutlined />
          </template>
          新增
        </a-button>
      </div>
    </div>
    <div class="contentWrap custom-scrollbar" ref="tableWrapperRef">
      <a-table
        ref="tableRef"
        :loading="tableLoading"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="false"
        rowKey="index"
        bordered
        :scroll="{ x: 1200,  y: tableHeight }"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'operation'">
            <span>
              <a-button type="link" size="small" @click="editHandle(record)" v-privilege="'system:role:update'">编辑</a-button>
              <a-button type="link" size="small" danger @click="delHandle(record)" v-privilege="'system:role:delete'">删除</a-button>
            </span>
          </template>
        </template>
      </a-table>
    </div>
    <div class="pageWrap">
      <a-pagination
        size="small"
        v-model:current="pagination.pageNum"
        :page-size="pagination.pageSize"
        :total="total"
        show-size-changer
        show-quick-jumper
        :show-total="(total: number) => `共 ${total} 条`"
        :page-size-options="PAGE_SIZE_OPTIONS"
        @change="(current: number, size: number) => onPageChange('current', current, size)"
        @showSizeChange="(current: number, size: number) => onPageChange('size', current, size)"
      />
    </div>
    <a-modal
      v-model:open="addEditModalState.visible"
      :title="addEditModalState.type === 'add' ? '新增角色' : '编辑角色'"
      width="600px"
      cancelText="取消"
      okText="提交"
      :closable="false"
      :keyboard="false"
      :maskClosable="false"
      @cancel="handleCancle"
      @ok="handleOk"
    >
      <a-form class="roleHandleForm" ref="roleFormRef" :model="roleFormState" :rules="roleRules" :labelCol="{ span: 5 }">
        <a-form-item ref="roleName" label="角色名称" name="roleName">
          <a-input v-model:value="roleFormState.roleName" :maxlength="20" />
        </a-form-item>
        <a-form-item ref="remark" label="角色描述" name="remark">
          <a-textarea v-model:value="roleFormState.remark" placeholder="角色描述" :auto-size="{ minRows: 2, maxRows: 3 }" :maxlength="1000" />
        </a-form-item>
        <a-form-item ref="menuIdList" label="菜单权限" name="menuIdList">
          <div style="border: 1px solid #d9d9d9; height: 300px; overflow-y: auto;">
            <a-tree
              checkable
              :checkStrictly="false"
              :tree-data="menuTreeData"
              v-model:checkedKeys="menuCheckedKeys"
              :fieldNames="{ title: 'menuName', key: 'menuId', children: 'children' }"
              :defaultExpandAll="true"
              v-if="menuTreeData.length"
              @check="menuCheckedKeysChange"
            />
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import {
  PlusOutlined,
  SlackOutlined,
  MoreOutlined,
  FieldTimeOutlined,
  UserOutlined,
  ExclamationCircleOutlined,
  EnvironmentOutlined,
  DeleteOutlined,
  PlusCircleOutlined
} from '@ant-design/icons-vue'
import { Modal, TreeSelect, message } from 'ant-design-vue'
import { ValidateErrorEntity } from 'ant-design-vue/es/form/interface'
import { reactive, ref, UnwrapRef, createVNode, onMounted } from 'vue'
import { roleApi } from '@/api/manageTool/roleApi'
import { menuApi } from '@/api/manageTool/menuApi'
import { PageParamModel } from '@/types/paging'
import { PAGE_SIZE_OPTIONS } from '@/utils/constants'
import { getTableHeight } from '@/hooks/getTableHeight'
import { buildMenuTableTree, filterMenuByQueryForm } from '@/utils/menuDataHandler'
import _, { get } from 'lodash'
// 注册组件
defineOptions({
  components: {
    PlusOutlined,
    SlackOutlined,
    MoreOutlined,
    FieldTimeOutlined,
    UserOutlined,
    EnvironmentOutlined,
    DeleteOutlined,
    PlusCircleOutlined
  }
})

/* ====================== 表格 ====================== */
const tableWrapperRef = ref()
const tableRef = ref()
const tableHeight = getTableHeight(tableWrapperRef, tableRef)

const tableLoading = ref(false)
const columns = reactive([
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    customRender: ({ index }: any) => (pagination.value.pageNum - 1) * pagination.value.pageSize + index + 1,
    fixed: 'left',
    width: 80,
    align: 'center'
  },
  {
    title: '角色名称',
    dataIndex: 'roleName',
    key: 'roleName',
    fixed: 'left',
    align: 'center'
  },
  {
    title: '角色描述',
    dataIndex: 'remark',
    key: 'remark',
    align: 'center'
  },
  {
    title: '创建人',
    dataIndex: 'actualName',
    key: 'actualName',
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 200,
    align: 'center'
  },
  {
    title: '操作',
    key: 'operation',
    dataIndex: 'operation',
    // slots: { customRender: 'operation' },
    fixed: 'right',
    width: 130,
    align: 'center'
  }
])

const dataSource = ref([])

/* ====================== 分页 ====================== */
const pagination = ref<PageParamModel>({
  pageNum: 1, // 当前页码
  pageSize: 10, // 每页数量
  sortItemList: [
    {
      isAsc: false,
      column: 'create_time'
    }
  ]
})
const total = ref<number>(0)
const onPageChange = (type: string, pageNum: number, pageSize: number) => {
  console.log(pageNum, pageSize)
  if (type === 'current') {
    pagination.value.pageNum = pageNum
  } else {
    pagination.value.pageNum = 1
    pagination.value.pageSize = pageSize
  }
  queryRolePage()
}

/* ====================== 搜索 ====================== */
interface SearchFormState {
  // 角色名称
  roleName?: string
  // 角色id
  roleId?: string
}
const searchFormState: UnwrapRef<SearchFormState> = reactive({
  roleName: ''
})
const queryRolePage = async () => {
  try {
    tableLoading.value = true
    const result = await roleApi.queryRolePage({
      ...pagination.value,
      ...searchFormState
    })
    console.log(result)
    dataSource.value = result.data.list
    total.value = result.data.total
  } catch (e) {
    console.log(e)
    dataSource.value = []
    total.value = 0
  } finally {
    tableLoading.value = false
  }
}
const onSearch = () => {
  console.log(searchFormState.roleName)
  pagination.value.pageNum = 1
  total.value = 0
  dataSource.value = []
  queryRolePage()
}

/* ====================== 表单（新增|编辑） ====================== */
const menuData = ref([])
const menuTreeData = ref([])
const menuCheckedKeys = ref({})
const menuCheckedKeysChange = (checkedKeys: any, e: any) => {
  console.log(checkedKeys)
  console.log(e)
  // const getChildMenu = (childArr: Array<T>, data: object) => {
  //   childArr.push(data.menuId)
  //   if (data.children && data.children.length > 0) {
  //     data.children.forEach((item: any) => {
  //       getChildMenu(childArr, item)
  //     })
  //   }
  //   return childArr
  // }
  // const childArr = getChildMenu([], e.node.dataRef)
  // let selectedKeys = []
  // if (e.checked) {
  //   selectedKeys = [...checkedKeys.checked, ...childArr]
  //   const haveParent = selectedKeys.some(item => item == e.node.dataRef.parentId)
  //   if (!haveParent) selectedKeys.push(e.node.dataRef.parentId)
  // } else {
  //   selectedKeys = menuCheckedKeys.value.checked.filter(item => {
  //     const index = childArr.findIndex(child => child == item)
  //     return index === -1
  //   })
  // }
  // roleFormState.value.menuIdList = [...new Set(selectedKeys)]
  // menuCheckedKeys.value.checked = [...new Set(selectedKeys)]
  // 查找父节点
  const getParent = (currentMenuId, menuList, checkedKeys, parentIds) => {
    const currentNode = menuList.find(menuItem => menuItem.menuId == currentMenuId)
    if (currentNode) {
      if (currentNode.parentId != 0 && parentIds.indexOf(currentNode.parentId) === -1 && checkedKeys.indexOf(currentNode.parentId) === -1) {
        parentIds.push(currentNode.parentId)
        getParent(currentNode.parentId, menuList, checkedKeys, parentIds)
      }
    }
    return parentIds
  }

  if (checkedKeys.length > 0) {
    const parentIds = []
    const menuList = _.cloneDeep(menuData.value)
    checkedKeys.forEach(item => {
      getParent(item, menuList, checkedKeys, parentIds)
    })
    roleFormState.value.menuIdList = [...checkedKeys, ...new Set(parentIds)]
  } else {
    roleFormState.value.menuIdList = [...checkedKeys]
  }
}

interface RoleFormState {
  roleId: number | null
  // 角色名称
  roleName: string
  // 角色描述
  remark?: string
  // 菜单ID集合
  menuIdList: Array<string>
}
const roleFormStateModel = {
  roleId: null,
  roleName: '',
  remark: '',
  menuIdList: []
}
const roleFormState = ref<RoleFormState>({ ...roleFormStateModel })

const roleFormRef = ref()

const roleRules = reactive({
  roleName: [{ required: true, message: '请输入角色名称' }],
  menuIdList: [{ required: true, message: '请选择菜单权限' }]
})
interface AddEditModalModel {
  visible: boolean
  type: '' | 'add' | 'edit'
  rowData?: any
}
const addEditModalState = ref<AddEditModalModel>({
  visible: false,
  type: '',
  rowData: {}
})
// 获取角色关联菜单权限
const queryMenuTree = async () => {
  // try {
  //   const result = await menuApi.queryMenuTree(false)
  //   menuTreeData.value = result.data
  // } catch (e) {
  //   menuTreeData.value = []
  // }
  try {
    let result = await menuApi.queryMenu()
    menuData.value = _.cloneDeep(result.data.filter(item => !item.disabledFlag))
    // 过滤搜索条件
    const filtedMenuList = filterMenuByQueryForm(result.data, { disabledFlag: false })
    // 递归构造树形结构，并付给 TableTree组件
    menuTreeData.value = buildMenuTableTree(filtedMenuList)
  } catch (e) {
    menuData.value = []
    menuTreeData.value = []
  }
}
const handleCancle = () => {
  menuCheckedKeys.value = {}
  addEditModalState.value.visible = false
  roleFormState.value = _.cloneDeep(roleFormStateModel)
  roleFormRef.value.resetFields()
}
const handleOk = () => {
  console.log('roleFormState.value', roleFormState.value)
  roleFormRef.value
    .validate()
    .then(async () => {
      if (addEditModalState.value.type === 'add') {
        await roleApi.addRole(roleFormState.value)
        message.success('新增成功')
        searchFormState.roleName = ''
        onSearch()
      } else {
        await roleApi.updateRole(roleFormState.value)
        message.success('编辑成功')
        queryRolePage()
      }
      handleCancle()
    })
    .catch((error: ValidateErrorEntity<RoleFormState>) => {
      console.log('error', error)
      // addEditModalState.value.visible = false
    })
}

/* ====================== 操作按钮 ====================== */
const addHandle = () => {
  queryMenuTree()
  addEditModalState.value.type = 'add'
  addEditModalState.value.visible = true
}
const editHandle = async (item: any) => {
  await queryMenuTree()
  addEditModalState.value.type = 'edit'
  addEditModalState.value.visible = true
  roleFormState.value = {
    roleId: item.roleId,
    roleName: item.roleName,
    remark: item.remark,
    menuIdList: []
  }
  try {
    const result = await roleApi.getRoleSelectedMenu(item.roleId)
    roleFormState.value.menuIdList = result.data.selectedMenuId
    // 菜单树回显
    const selectedMenuIds = _.cloneDeep(result.data.selectedMenuId)
    const menuCheckedIds = []
    const menuList = _.cloneDeep(menuData.value)
    selectedMenuIds.forEach(selectedItem => {
      const children = menuList.filter(menuItem => menuItem.parentId == selectedItem)
      if (children.length) {
        const haveAllChild = children.every((child, index) => selectedMenuIds.indexOf(child.menuId) !== -1)
        console.log(haveAllChild, 'haveAllChild')
        if (haveAllChild) {
          menuCheckedIds.push(selectedItem)
        }
      } else {
        menuCheckedIds.push(selectedItem)
      }
    })
    menuCheckedKeys.value = {
      checked: menuCheckedIds
    }
  } catch (e) {
    console.log(e)
    roleFormState.value.menuIdList = []
  }
}
const delHandle = (item: any) => {
  console.log(item)
  Modal.confirm({
    title: () => '删除提醒',
    icon: () => createVNode(ExclamationCircleOutlined),
    content: () => '确定是否删除该角色？',
    okText: () => '确认',
    okType: 'danger',
    cancelText: () => '取消',
    onOk: async () => {
      try {
        await roleApi.deleteRole(item.roleId)
        message.success('删除成功')
        if (pagination.value.pageNum > 1 && dataSource.value.length === 1) {
          pagination.value.pageNum = pagination.value.pageNum - 1
        }
        queryRolePage()
      } catch (e) {
        console.log(e)
      }
    },
    onCancel() {
      console.log('Cancel')
    }
  })
}

onMounted(() => {
  queryRolePage()
})
</script>
<style lang="scss">
.roleContainer {
  height: 100%;

  .handleWrap {
    height: 54px;
    padding: 10px;
    box-sizing: border-box;

    .left {
      width: 80%;

      .ant-input {
        box-sizing: border-box;
        width: 240px;
        height: 34px;
      }

      .ant-btn {
        height: 34px;
      }
    }

    .right {
      width: 20%;
      text-align: right;

      .ant-btn {
        height: 34px;
      }
    }
  }

  .contentWrap {
    height: calc(100% - 110px);
    padding: 10px;
    overflow: hidden;

    .ant-card {
      height: 100%;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      border-radius: 6px;
      cursor: default;

      &:hover {
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
      }

      .ant-card-body {
        padding: 15px;
      }

      .cardHead {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #000;

        .headIcon {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          background: #d1cdfc;
          text-align: center;
          min-width: 50px;

          .anticon-slack {
            font-size: 30px;
            color: #200de7;
            width: 30px;
            height: 30px;
            margin-top: 10px;
          }
        }

        .cardTitle {
          font-size: 14px;
          font-weight: 600;
          margin: 0 10px;
        }

        .ant-dropdown-link {
          color: #000;

          &:hover {
            color: #1890ff;
          }
        }
      }

      .cardInfo {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #999;
        margin: 10px 0;

        .anticon {
          margin-right: 4px;
        }
      }

      .cardRemarks {
        border-top: 1px solid #eee;
        font-size: 12px;
        color: #666;
        padding-top: 10px;
      }
    }
  }

  .pageWrap {
    text-align: right;
    margin-top: 20px;
    height: 38px;
    padding-right: 20px;
  }
}

.ant-modal-body {
  padding: 20px 30px 0 20px;
}

.roleHandleForm {
  .ant-input {
    vertical-align: middle;
  }

  .ant-select {
    vertical-align: middle;
  }

  .anticon-environment {
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    color: #666;
    vertical-align: middle;
  }

  .anticon-delete {
    color: red;
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    vertical-align: middle;
  }

  .anticon-plus-circle {
    color: blue;
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    vertical-align: middle;
  }

  .targetItem {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.resetPwdTips {
  text-align: center;
  margin: 20px 0 30px;
  font-size: 20px;
}
</style>
