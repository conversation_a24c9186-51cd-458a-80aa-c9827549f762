<template>
  <a-layout>
    <a-layout-sider v-model:collapsed="collapsed" :trigger="null" collapsible>
      <div class="menuWrap custom-scrollbar">
        <a-menu theme="dark" mode="inline" :selectedKeys="[currentMenuKey]">
          <template v-for="menuItem in menuData" :key="menuItem.menuId">
            <template v-if="menuItem.children && menuItem.children.length">
              <subMenu :data="menuItem" @menuItemClick="menuItemClick"></subMenu>
            </template>
            <template v-else>
              <a-menu-item :key="menuItem.menuId" @click="menuItemClick(menuItem)">
                <!-- <user-outlined /> -->
                <template #icon v-if="menuItem.icon">
                  <component :is="menuItem.icon" />
                </template>
                <span>{{ menuItem.menuName }}</span>
              </a-menu-item>
            </template>
          </template>
        </a-menu>
      </div>
    </a-layout-sider>
    <a-layout>
      <a-layout-header class="header">
        <div class="content-wrapper">
          <menu-unfold-outlined v-if="collapsed" class="trigger" @click="() => (collapsed = !collapsed)" />
          <menu-fold-outlined v-else class="trigger" @click="() => (collapsed = !collapsed)" />
          <h4 class="secondTitle">{{ currentMenuName }}</h4>
        </div>
      </a-layout-header>
      <a-layout-content class="content">
        <router-view />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script lang="ts" setup>
import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  HddOutlined,
  DeploymentUnitOutlined,
  CameraOutlined,
  AlertOutlined,
  TeamOutlined,
  TrademarkOutlined,
  ApartmentOutlined,
  ProfileOutlined,
  MenuOutlined,
  AppstoreOutlined
} from '@ant-design/icons-vue'
import { ERouterName } from '@/types/index'
import { useRouter } from 'vue-router'
import { onMounted, ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import subMenu from '@/components/layout/subMenu.vue'
import { useStore } from 'vuex'

// 注册组件
defineOptions({
  components: {
    MenuUnfoldOutlined,
    MenuFoldOutlined,
    HddOutlined,
    DeploymentUnitOutlined,
    CameraOutlined,
    AlertOutlined,
    TeamOutlined,
    TrademarkOutlined,
    ApartmentOutlined,
    ProfileOutlined,
    MenuOutlined,
    AppstoreOutlined
  }
})
const route = useRoute()
const router = useRouter()

const store = useStore()
const menuData = computed(() => {
  const menu = store.getters['user/getMenuByName']('管理工具')
  if (menu && menu.children && menu.children.length) {
    return menu.children
  } else {
    return []
  }
})
// 响应式状态
const collapsed = ref<boolean>(false)

//当前选中菜单
const currentMenuName = ref<string | unknown>(route.meta.title)
const currentMenuKey = ref<string | unknown>(route.name)
const menuItemClick = (value: any) => {
  currentMenuName.value = value.menuName
  currentMenuKey.value = value.menuId
  router.push({ name: value.menuId.toString() })
}


onMounted(() => {})
</script>

<style lang="scss" scoped>
.menuWrap {
  height: 100%;
  overflow: auto;
}

.header {
  display: flex;
  align-items: center; /* 垂直居中 */
  height: 60px;
  line-height: 60px;
  background: #fff;
  padding: 0 10px;
  .content-wrapper {
    display: flex;
    align-items: center; /* 垂直居中 */
    gap: 20px; /* 图标与标题之间的间距 */
    flex-grow: 1; /* 允许内容包裹器扩展以占据剩余空间 */
    justify-content: flex-start; /* 子元素从左开始排列 */
    .trigger {
      font-size: 18px;
      cursor: pointer;
      transition: color 0.3s;
    }

    .trigger:hover {
      color: #1890ff; /* 鼠标悬停时改变颜色 */
    }

    .secondTitle {
      margin: 0; /* 移除默认的标题边距 */
      white-space: nowrap; /* 确保标题不换行 */
      font-size: 22px;
      font-weight: bold;
    }
  }
}

.content {
  height: calc(100vh - 60px);
  background: #fff;
  margin: 15px;
}
</style>
