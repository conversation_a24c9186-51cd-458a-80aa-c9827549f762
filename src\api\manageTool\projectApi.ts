import { getRequest, postRequest } from '../http/request'

export const projectApi = {
  // 分页查询
  queryProjectPage: (param: object) => {
    return postRequest('/gProject/queryPage', param)
  },
  // 添加
  addProject: (param: object) => {
    return postRequest('/gProject/add', param)
  },
  // 更新
  updateProject: (param: object) => {
    return postRequest('/gProject/update', param)
  },
  // 删除
  deleteProject: (id: string | number) => {
    return getRequest(`/gProject/delete/${id}`, {})
  }
}
