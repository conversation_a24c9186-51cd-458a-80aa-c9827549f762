export const vInputNumberChange = {
  mounted(el) {
    console.log(el)
    const inputElement = el.querySelector('input')
    if (inputElement) {
      // 保存函数引用以便后续移除
      el._inputHandler_ = e => {
        if ([1, 2, 3, 4, 5, 6, 7, 8, 9, 0].includes(e.data)) {
          inputElement.blur()
          inputElement.focus()
          inputElement.setSelectionRange(inputElement.value.length, inputElement.value.length)
        } else {
          inputElement.blur()
          inputElement.focus()
        }
      }
      inputElement.addEventListener('input', el._inputHandler_)
    }
  },
  unmounted(el) {
    const inputElement = el.querySelector('input')
    if (inputElement && el._inputHandler_) {
      inputElement.removeEventListener('input', el._inputHandler_)
      delete el._inputHandler_
    }
  }
}
