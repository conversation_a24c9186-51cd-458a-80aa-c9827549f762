<template>
  <div class="layout">
    <a-layout>
      <a-layout-header v-if="route.name !== 'workRecordApp'">
        <div class="left">
          <h1>{{ titleName }}</h1>
          <em v-if="route.name !== 'home'">|</em>
          <HomeOutlined v-if="route.name !== 'home'" @click="$router.push('/' + ERouterName.HOME)" />
        </div>
        <div class="userInfo">
          <UserOutlined />
          <a-dropdown>
            <a class="ant-dropdown-link" @click.prevent>
              {{ store.getters['user/userInfo'].loginName }}
              <DownOutlined />
            </a>
            <template #overlay>
              <a-menu>
                <a-menu-item key="userCenter" @click="$router.push('/' + ERouterName.USERCENTER)">个人中心</a-menu-item>
                <a-menu-item key="testRoute" @click="$router.push('/' + ERouterName.TESTROUTE)">测试航线</a-menu-item>
                <a-menu-item key="managementTools" @click="$router.push('/' + ERouterName.MANAGE)">管理工具</a-menu-item>
                <a-menu-item key="logout" @click="logout">退出登录</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>
      <a-layout-content>
        <router-view />
      </a-layout-content>
      <a-layout-footer v-if="route.name !== 'workRecordApp'">
        <p>Copyright ©{{ new Date().getFullYear() }} 汉威祥云（上海）数据服务有限公司 沪ICP备18020687号</p>
      </a-layout-footer>
    </a-layout>
  </div>
</template>

<script lang="ts" setup>
import { UserOutlined, DownOutlined, HomeOutlined } from '@ant-design/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import { ERouterName } from '@/types'
import { loginApi } from '@/api/loginApi'
import { useStore } from 'vuex'
import { ref, watch, onMounted } from 'vue'
import { workspaceApi } from '@/api/workspace/workspaceApi'

// 注册组件
defineOptions({
  components: {
    UserOutlined,
    DownOutlined,
    HomeOutlined
  }
})
const store = useStore()
const route = useRoute()
const router = useRouter()

const titleName = ref('')
const logout = async () => {
  try {
    await loginApi.logout()
  } finally {
    store.dispatch('user/logout')
    store.dispatch('user/setLoginInfo', {})
    router.replace('/' + ERouterName.LOGIN)
  }
}

watch(
  () => route,
  (newValue, oldValue) => {
    if (newValue.path.indexOf('/workspace') > -1) {
      queryProject()
    } else {
      titleName.value = '无人载具与作业管理平台'
    }
  }
)

const queryProject = async () => {
  // 获取 localStorage 中的项目信息
  const projectSelectedId = route.query.id;
  try {
    const result = await workspaceApi.getProjectDetails(projectSelectedId)
    titleName.value = result.data.name
  } catch (e) {
  } finally {
  }
}

onMounted(() => {
  console.log(route);
  if(route.path.indexOf('/workspace') > -1) {
    queryProject()
  } else {
    titleName.value = '无人载具与作业管理平台'
  }
})

</script>
<style lang="scss" scoped>
.layout {
  width: 100%;
  height: 100vh;

  .ant-layout {
    height: 100%;

    .ant-layout-header {
      height: 50px;
      padding: 0;

      .left {
        float: left;
        display: flex;
        align-items: center;

        h1 {
          font-size: 24px;
          color: #fff;
          font-weight: bold;
          line-height: 50px;
          margin: 0 0 0 20px;
          letter-spacing: 1px;
        }

        em {
          color: #fff;
          font-style: normal;
          margin: 0 15px;
          line-height: 50px;
        }

        span {
          font-size: 20px;
          color: #fff;
          cursor: pointer;
        }
      }

      .userInfo {
        float: right;
        color: #fff;
        line-height: 50px;
        margin-right: 20px;

        .ant-dropdown-link {
          margin-left: 4px;
          color: #fff;
          font-size: 16px;
          cursor: pointer;

          &:hover {
            color: #1890ff;
          }
        }
      }
    }

    .ant-layout-content {
      height: calc(100% - 80px);
      padding: 0;
    }

    .ant-layout-footer {
      height: 30px;
      background: #001529;
      color: #fff;
      padding: 0;

      p {
        text-align: center;
        line-height: 30px;
      }
    }
  }
}
</style>