<template>
  <div class="route-library">
    <div class="header">
      <h2>航线库</h2>
      <a-button type="primary" @click="showAddRouteModal">添加航线</a-button>
    </div>

    <a-input-search
      v-model:value="searchText"
      placeholder="搜索航线名称"
      style="margin-bottom: 10px"
      enter-button
      @search="handleSearch"
    />

    <div class="route-list">
      <a-spin :spinning="loading">
        <a-empty v-if="filteredRoutes.length === 0" description="暂无航线数据" />
        <div v-else>
          <div v-for="route in filteredRoutes" :key="route.id" class="route-item">
            <div class="route-info" @click="selectRoute(route)">
              <div class="route-name">{{ route.name }}</div>
              <div class="route-details">
                <span>{{ route.pointsCount }}个点位</span>
                <span>{{ route.distance }}米</span>
                <span>{{ route.createTime }}</span>
              </div>
            </div>
            <div class="route-actions">
              <a-button type="text" @click="handleEditRoute(route)">
                <template #icon><EditOutlined /></template>
              </a-button>
              <a-popconfirm
                title="确定要删除此航线吗?"
                @confirm="handleDeleteRoute(route)"
                ok-text="是"
                cancel-text="否"
              >
                <a-button type="text" danger>
                  <template #icon><DeleteOutlined /></template>
                </a-button>
              </a-popconfirm>
            </div>
          </div>
        </div>
      </a-spin>
    </div>

    <!-- 使用公共的航线表单模态框组件 -->
    <RouteFormModal
      v-model:visible="modalVisible"
      :isEditing="isEditing"
      :initialFormData="routeForm"
      :confirmLoading="modalLoading"
      @submit="handleModalSubmit"
      @cancel="handleModalCancel"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, reactive, onMounted } from 'vue'
import { EditOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import RouteFormModal from './RouteFormModal.vue'

// 注册组件
defineOptions({
  components: {
    EditOutlined,
    DeleteOutlined,
    RouteFormModal
  }
})

const emit = defineEmits(['routeSelected'])

// 状态
const loading = ref(false)
const routes = ref<any[]>([])
const searchText = ref('')
const modalVisible = ref(false)
const modalLoading = ref(false)
const isEditing = ref(false)
const currentRouteId = ref<string | null>(null)

// 表单数据
const routeForm = reactive({
  name: '',
  description: '',
  type: 'inspection'
})

// 计算属性：筛选后的航线列表
const filteredRoutes = computed(() => {
  if (!searchText.value) {
    return routes.value
  }
  return routes.value.filter(route => route.name.toLowerCase().includes(searchText.value.toLowerCase()))
})

// 获取航线列表
const fetchRoutes = async () => {
  try {
    loading.value = true
    // 这里应该调用后端API获取航线列表数据
    // 模拟数据
    setTimeout(() => {
      routes.value = [
        {
          id: '1',
          name: '市区巡检航线A',
          description: '市区主要道路巡检',
          type: 'inspection',
          pointsCount: 5,
          distance: 2500,
          createTime: '2023-09-15',
          points: [
            // 北京市内的一些点位坐标
            {longitude: 116.3975, latitude: 39.9085, height: 100}, // 天安门附近
            {longitude: 116.4105, latitude: 39.9120, height: 150}, // 王府井
            {longitude: 116.4030, latitude: 39.9040, height: 120}, // 前门
            {longitude: 116.3845, latitude: 39.9060, height: 130}, // 西单
            {longitude: 116.3975, latitude: 39.9000, height: 110}  // 天安门南
          ]
        },
        {
          id: '2',
          name: '郊区测绘航线B',
          description: '郊区地形测绘',
          type: 'survey',
          pointsCount: 23,
          distance: 5600,
          createTime: '2023-10-20',
          points: []
        },
        {
          id: '3',
          name: '物资运输航线C',
          description: '城郊物资运输路线',
          type: 'transport',
          pointsCount: 8,
          distance: 3200,
          createTime: '2023-11-05',
          points: []
        }
      ]
      loading.value = false
    }, 500)
  } catch (error) {
    console.error('获取航线列表失败:', error)
    message.error('获取航线列表失败')
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  // 已通过计算属性自动过滤
}

// 选择航线
const selectRoute = (route: any) => {
  console.log(route)
  
  emit('routeSelected', route)
  message.success(`已选择航线: ${route.name}`)
}

// 显示添加航线模态框
const showAddRouteModal = () => {
  isEditing.value = false
  currentRouteId.value = null
  Object.assign(routeForm, {
    name: '',
    description: '',
    type: 'inspection'
  })
  modalVisible.value = true
}

// 编辑航线
const handleEditRoute = (route: any) => {
  isEditing.value = true
  currentRouteId.value = route.id

  // 填充表单数据
  Object.assign(routeForm, {
    name: route.name,
    description: route.description || '',
    type: route.type
  })
  
  modalVisible.value = true
}

// 删除航线
const handleDeleteRoute = async (route: any) => {
  try {
    // 这里应该调用后端API删除航线
    // 模拟删除操作
    const index = routes.value.findIndex(item => item.id === route.id)
    if (index !== -1) {
      routes.value.splice(index, 1)
      message.success('删除航线成功')
    }
  } catch (error) {
    console.error('删除航线失败:', error)
    message.error('删除航线失败')
  }
}

// 处理模态框提交
const handleModalSubmit = async (formData: any) => {
  try {
    modalLoading.value = true
    
    if (isEditing.value) {
      // 编辑现有航线
      const index = routes.value.findIndex(route => route.id === currentRouteId.value)
      if (index !== -1) {
        routes.value[index] = {
          ...routes.value[index],
          name: formData.name,
          description: formData.description,
          type: formData.type
        }
        message.success('更新航线成功')
      }
    } else {
      // 添加新航线
      const newRoute = {
        id: Date.now().toString(), // 使用时间戳作为临时ID
        name: formData.name,
        description: formData.description,
        type: formData.type,
        pointsCount: 0,
        distance: 0,
        createTime: new Date().toLocaleDateString(),
        points: []
      }

      routes.value.unshift(newRoute)
      message.success('添加航线成功')
    }
    
    modalVisible.value = false
    modalLoading.value = false
  } catch (error) {
    console.error('提交失败:', error)
    modalLoading.value = false
  }
}

// 模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
}

onMounted(() => {
  fetchRoutes()
})
</script>

<style lang="scss" scoped>
.route-library {
  height: 100%;
  padding: 15px;
  display: flex;
  flex-direction: column;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    h2 {
      margin: 0;
      color: #fff;
      font-size: 18px;
    }
  }

  .route-list {
    flex: 1;
    overflow-y: auto;

    .route-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      margin-bottom: 10px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      .route-info {
        flex: 1;
      }

      .route-name {
        font-weight: bold;
        margin-bottom: 5px;
      }

      .route-details {
        font-size: 12px;
        color: #ccc;

        span {
          margin-right: 10px;
        }
      }

      .route-actions {
        display: flex;
        gap: 5px;
      }
    }
  }
}

// 深色主题适配
:deep(.ant-empty-description) {
  color: #ccc;
}

:deep(.ant-select-dropdown) {
  background-color: #1f1f1f;
}

:deep(.ant-input),
:deep(.ant-select-selector) {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: #fff !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
}

:deep(.ant-form-item-label > label) {
  color: #fff;
}
</style>

<style lang="scss">
/* 全局样式，确保弹窗中的文字颜色清晰可见 */
.ant-modal {
  .ant-modal-content {
    background-color: #fff;

    .ant-modal-header {
      background-color: #fff;

      .ant-modal-title {
        color: rgba(0, 0, 0, 0.85);
        font-weight: bold;
      }
    }

    .ant-modal-body {
      .ant-form-item-label > label {
        color: rgba(0, 0, 0, 0.85);
      }

      .ant-input,
      .ant-select-selector,
      .ant-input-number {
        background-color: #fff !important;
        color: rgba(0, 0, 0, 0.85) !important;
        border-color: #d9d9d9 !important;
      }

      .ant-select-selection-placeholder,
      .ant-input::placeholder {
        color: rgba(0, 0, 0, 0.45) !important;
      }
    }

    .ant-modal-footer {
      .ant-btn {
        color: rgba(0, 0, 0, 0.85);

        &.ant-btn-primary {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #fff;
        }
      }
    }
  }
}
</style>
