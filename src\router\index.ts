import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import { ELocalStorageKey, ERouterName, EUserType } from '@/types/enums'
import { MENU_TYPE_ENUM } from '@/utils/constants'
import store from '@/store'
import { message } from 'ant-design-vue'
import NotFound from '@/pages/40X/404.vue'
import NoPrivilege from '@/pages/40X/403.vue'
const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/' + ERouterName.LOGIN
  },
  // 登录页
  {
    path: '/' + ERouterName.LOGIN,
    name: ERouterName.LOGIN,
    component: () => import('@/pages/Login.vue')
  },
  { path: '/:pathMatch(.*)*', name: '404', component: NotFound },
  { path: '/403', name: '403', component: NoPrivilege }
]

export const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})
router.beforeEach((to, from, next) => {
  // const { query } = to
  // if (query.token) {
  //   localStorage.setItem(ELocalStorageKey.Token, query.token)
  // }
  // 验证登录
  const token = localStorage.getItem(ELocalStorageKey.Token)
  if (!token) {
    store.dispatch('user/logout')
    store.dispatch('user/setLoginInfo', {})
    if (to.path === '/login') {
      next()
    } else {
      next({ path: '/login' })
    }
    return
  }

  if (to.path === '/manage') {
    const manageMenu = store.getters['user/getMenuByName']('管理工具')
    if (!manageMenu) {
      next({ path: '/403' })
    } else {
      const redirectPath = getRedirectPath(manageMenu)
      if (redirectPath) {
        next({
          path: redirectPath,
          replace: true
        })
      } else {
        next({ path: '/404' })
      }
    }
  } else {
    next()
  }
})
// ----------------------- 构建router对象 -----------------------
const routerMap = new Map()

export function buildRoutes(menuRouterList) {
  let menuList = menuRouterList ? menuRouterList : useUserStore().getMenuRouterList || []
  /**
   * 1、构建整个路由信息
   * 2、添加到路由里
   */
  const routerList = []
  // 获取所有vue组件引用地址 用于构建路由
  const modules = import.meta.glob('../pages/**/**.vue')
  // 获取所有vue组件 用于注入name属性 name属性用于keep-alive

  //1、构建整个路由信息
  for (const e of menuList) {
    if (!e.menuId) {
      continue
    }
    if (!e.path) {
      continue
    }
    if (e.deletedFlag && e.deletedFlag === true) {
      continue
    }
    let route = {
      path: e.path.startsWith('/') ? e.path : `/${e.path}`,
      // 使用【menuId】作为name唯一标识
      name: e.menuId.toString(),
      meta: {
        // 数据库菜单(页面)id
        id: e.menuId.toString(),
        // 组件名称
        componentName: e.menuId.toString(),
        // 菜单展示
        title: e.menuName,
        // 菜单图标展示
        icon: e.icon,
        // 是否在菜单隐藏
        hideInMenu: !e.visibleFlag,
        // 页面是否keep-alive缓存
        keepAlive: e.cacheFlag,
        // 是否为外链
        frameFlag: e.frameFlag,
        // 外链地址
        frameUrl: e.frameUrl,
        // 是否 rename了组件的名字
        renameComponentFlag: false
      }
    }

    if (e.frameFlag) {
      route.component = () => import('../components/framework/iframe/iframe-index.vue')
    } else {
      let componentPath = e.component && e.component.startsWith('/') ? e.component : '/' + e.component
      let relativePath = `../pages${componentPath}`
      // // eslint-disable-next-line no-prototype-builtins
      route.component = modules[relativePath]
    }
    routerList.push(route)
    routerMap.set(e.menuId.toString(), route)
  }
  //2、添加到路由里
  router.addRoute({
    path: "/",
    name: ERouterName.LAYOUT,
    component: () => import("@/pages/Layout.vue"),
    children: [
      // 首页
      {
        path: ERouterName.HOME,
        name: ERouterName.HOME,
        component: () => import("@/pages/Home.vue"),
        children: [],
      },
      // 载具工作页面
      {
        path: ERouterName.WORKSPACE,
        name: ERouterName.WORKSPACE,
        component: () => import("@/pages/workspace/Index.vue"),
        redirect: "/" + ERouterName.WORKSPACE + "/" + ERouterName.PROJECTINFO,
        children: [
          {
            path: ERouterName.PROJECTINFO,
            name: ERouterName.PROJECTINFO,
            component: () => import("@/pages/workspace/ProjectInfo.vue"),
          },
          {
            path: ERouterName.WORKRECORD,
            name: ERouterName.WORKRECORD,
            component: () => import("@/pages/workspace/WorkRecord.vue"),
          },
          {
            path: ERouterName.WORKRECORDAPP,
            name: ERouterName.WORKRECORDAPP,
            component: () => import("@/pages/workspace/WorkRecordApp.vue"),
          },
        ],
      },
      // 测试航线页面
      {
        path: ERouterName.TESTROUTE,
        name: ERouterName.TESTROUTE,
        component: () => import("@/pages/TestRoute.vue"),
      },
      // 个人中心页面
      {
        path: ERouterName.USERCENTER,
        name: ERouterName.USERCENTER,
        component: () => import("@/pages/UserCenter.vue"),
      },
      // 管理页面
      {
        path: ERouterName.MANAGE,
        name: ERouterName.MANAGE,
        component: () => import("@/pages/manage/Index.vue"),
        // redirect: '/' + ERouterName.MANAGE + '/' + ERouterName.PROJECT,
        children: [...routerList],
      },
    ],
  });
  return routerList
}

// 获取管理工具路由的重定向
const getRedirectPath = menu => {
  // 不存在数据
  if (!menu) return '/home'
  // 数据类型是菜单
  if (menu.menuType == MENU_TYPE_ENUM.MENU.value) return `${menu.path}`
  let path = ''
  if (menu.children && menu.children.length) {
    menu.children.forEach(item => {
      if (!path) {
        // 菜单
        if (item.menuType == MENU_TYPE_ENUM.MENU.value) {
          path = `${item.path}`
        } else {
          path = getRedirectPath(item)
        }
      }
    })
  }
  return path
}
