<template>
  <div class="project projectContainer">
    <div class="handleWrap clearfix">
      <div class="left fl">
        <a-form layout="inline" :model="searchFormState">
          <a-form-item>
            <a-input v-model:value="searchFormState.name" placeholder="请输入项目名称" size="small" allowClear @pressEnter="onSearch"></a-input>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="onSearch">搜索</a-button>
          </a-form-item>
        </a-form>
      </div>
      <div class="right fr">
        <a-button type="primary" @click="addHandle">
          <template #icon>
            <PlusOutlined />
          </template>
          新增
        </a-button>
      </div>
    </div>
    <div class="contentWrap custom-scrollbar" v-if="dataSource.length">
      <div style="display: flex; flex-wrap: wrap;">
        <div
          style="width: 20%;min-width: 235px; padding-right: 20px; padding-bottom:20px;box-sizing: border-box;"
          v-for="item in dataSource"
          :key="item"
        >
          <a-card :bordered="false">
            <div class="cardHead">
              <div class="headIcon">
                <SlackOutlined />
              </div>
              <span :title="item.name" class="cardTitle single-line">{{ item.name }}</span>
              <a-dropdown v-if="store.getters['user/administratorFlag'] || item.chargeUserId == store.getters['user/userInfo'].userId">
                <a class="ant-dropdown-link" @click.prevent>
                  <MoreOutlined />
                </a>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="editHandle(item)">
                      <div>编辑</div>
                    </a-menu-item>
                    <a-menu-item @click="delHandle(item)">
                      <div>删除</div>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
            <div class="cardInfo">
              <div class="createTime" :title="item.createTime">
                <FieldTimeOutlined />
                <span>{{ item.createTime }}</span>
              </div>
              <div class="user" :title="item.chargeUserName">
                <UserOutlined />
                <span>{{ item.chargeUserName }}</span>
              </div>
            </div>
            <div class="cardRemarks multi-line" :title="item.des">{{ item.des }}</div>
          </a-card>
        </div>
      </div>
    </div>
    <div class="contentWrap" style="display: flex; align-items: center; justify-content: center;" v-else>
      <a-empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
    </div>
    <div class="pageWrap">
      <a-pagination
        size="small"
        v-model:current="pagination.pageNum"
        :page-size="pagination.pageSize"
        :total="total"
        @change="(current: number, size: number) => onPageChange('current', current, size)"
      />
    </div>
    <a-modal
      v-model:open="addEditModalState.visible"
      :title="addEditModalState.type === 'add' ? '新增项目' : '编辑项目'"
      width="600px"
      cancelText="取消"
      okText="提交"
      :closable="false"
      :keyboard="false"
      :maskClosable="false"
      @cancel="handleCancle"
      @ok="handleOk"
    >
      <a-form class="projectHandleForm" ref="projectFormRef" :model="projectFormState" :rules="projectRules" :labelCol="{ span: 6 }">
        <a-form-item ref="name" label="项目名称" name="name">
          <a-input v-model:value="projectFormState.name" placeholder="请输入项目名称" :maxlength="30" />
        </a-form-item>
        <a-form-item ref="des" label="项目简介" name="des">
          <a-textarea v-model:value="projectFormState.des" placeholder="请输入项目简介" :auto-size="{ minRows: 2, maxRows: 3 }" :maxlength="1000" />
        </a-form-item>
        <a-form-item ref="address" label="项目作业中心点" name="address">
          <a-input :title="centerPoint" v-model:value="centerPoint" style="width: 90%;" placeholder="请选择项目作业中心点" :disabled="true" />
          <EnvironmentOutlined title="中心点选择" @click="centerPointHandle" />
        </a-form-item>
        <a-form-item ref="chargeUserId" label="负责人" name="chargeUserId">
          <a-select
            v-model:value="projectFormState.chargeUserId"
            :fieldNames="{label: 'actualName', value: 'employeeId'}"
            show-search
            :filter-option="filterOption"
            placeholder
            :options="chargeList"
            @change="userDisabledChange"
            style="width: 100%"
          ></a-select>
        </a-form-item>
        <a-form-item ref="memberUserIds" label="项目成员" name="memberUserIds">
          <div class="personItem" v-for="(item, index) in projectFormState.memberUserIds" :key="index">
            <a-form-item
              :name="['memberUserIds', index]"
              :rules="[{
                required: true,
                message: '请选择项目成员'
              }]"
            >
              <div style="display: flex; align-items: center;">
                <a-select
                  v-model:value="projectFormState.memberUserIds[index]"
                  show-search
                  :filter-option="filterOption"
                  :fieldNames="{label: 'actualName', value: 'employeeId'}"
                  placeholder
                  :options="memberList"
                  style="width: 85%;"
                  @change="userDisabledChange"
                ></a-select>
                <DeleteOutlined v-if="projectFormState.memberUserIds.length > 1" title="删除成员" @click="delPerson(index)" />
                <PlusCircleOutlined v-if="index == projectFormState.memberUserIds.length - 1" title="添加成员" @click="addPerson" />
              </div>
            </a-form-item>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
    <!-- 作业点在地图上显示，地图弹框 -->
    <a-modal
      v-model:open="mapVisible"
      title="作业点选取"
      cancelText="取消"
      okText="确定"
      :closable="false"
      :keyboard="false"
      :maskClosable="false"
      @ok="handleMapOk"
      @cancel="handleMapCancel"
      width="1200px"
    >
      <div class="mapContainer">
        <MapPointSelect v-if="mapVisible" @custom-event="handleChildData" :pointDefault="addressInfo" />
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import {
  PlusOutlined,
  SlackOutlined,
  MoreOutlined,
  FieldTimeOutlined,
  UserOutlined,
  ExclamationCircleOutlined,
  EnvironmentOutlined,
  DeleteOutlined,
  PlusCircleOutlined
} from '@ant-design/icons-vue'
import { Modal, message, Empty } from 'ant-design-vue'
import { ValidateErrorEntity } from 'ant-design-vue/es/form/interface'
import { reactive, ref, UnwrapRef, createVNode, onMounted } from 'vue'
import MapPointSelect from '@/components/MapPointSelect.vue'
import { projectApi } from '@/api/manageTool/projectApi'
import { userApi } from '@/api/manageTool/userApi'
import { PageParamModel } from '@/types/paging'
import { PAGE_SIZE_OPTIONS } from '@/utils/constants'
import _ from 'lodash'
import { changeBaseMap } from '@/components/cesiumMap/MapWorks'
import { useStore } from 'vuex'
const store = useStore()
// 注册组件
defineOptions({
  components: {
    PlusOutlined,
    SlackOutlined,
    MoreOutlined,
    FieldTimeOutlined,
    UserOutlined,
    EnvironmentOutlined,
    DeleteOutlined,
    PlusCircleOutlined,
    MapPointSelect
  }
}) /* ====================== 分页 ====================== */
const pagination = ref<PageParamModel>({
  pageNum: 1, // 当前页码
  pageSize: 15, // 每页数量
  sortItemList: [
    {
      isAsc: false,
      column: 'create_time'
    }
  ]
})
const total = ref<number>(0)
const onPageChange = (type: string, pageNum: number, pageSize: number) => {
  console.log(pageNum, pageSize)
  if (type === 'current') {
    pagination.value.pageNum = pageNum
  } else {
    pagination.value.pageNum = 1
    pagination.value.pageSize = pageSize
  }
  queryProjectPage()
}
/* ====================== 搜索 ====================== */
const dataSource = ref([])
interface SearchFormState {
  name: string
}
const searchFormState: UnwrapRef<SearchFormState> = reactive({
  name: ''
})
const tableLoading = ref(false)
const queryProjectPage = async () => {
  try {
    tableLoading.value = true
    const result = await projectApi.queryProjectPage({
      ...pagination.value,
      ...searchFormState
    })
    console.log(result)
    dataSource.value = result.data.list.map((item: any) => {
      const chargeUserId = item.projectMemberVOList.find(chargeUser => chargeUser.type === 0).userId
      return {
        ...item,
        chargeUserId
      }
    })
    total.value = result.data.total
  } catch (e) {
    console.log(e)
    dataSource.value = []
    total.value = 0
  } finally {
    tableLoading.value = false
  }
}
const onSearch = () => {
  console.log(searchFormState.name)
  pagination.value.pageNum = 1
  total.value = 0
  dataSource.value = []
  queryProjectPage()
}
/* ====================== 表单（新增|编辑） ====================== */
const centerPoint = ref('')
// 新增/编辑
interface ProjectFormState {
  id: number | null
  name: string
  des: string
  lon: number | string
  lat: number | string
  address: string
  chargeUserId: number | null
  memberUserIds: Array<number | string>
}
const projectFormStateModel = {
  id: null,
  name: '',
  des: '',
  lon: '',
  lat: '',
  address: '',
  chargeUserId: null,
  memberUserIds: ['']
}
const projectFormState = ref<ProjectFormState>(_.cloneDeep(projectFormStateModel))
const projectFormRef = ref()
const projectRules = reactive({
  name: [{ required: true, message: '请输入项目名称' }],
  // des: [{ required: true, message: '请输入项目简介' }],
  address: [{ required: true, message: '请选择作业中心点' }],
  chargeUserId: [{ required: true, message: '请选择负责人' }],
  memberUserIds: [{ required: true, message: '' }]
})
interface AddEditModalModel {
  visible: boolean
  type: '' | 'add' | 'edit'
}
const addEditModalState = ref<AddEditModalModel>({
  visible: false,
  type: ''
})
const userList = ref([])
const disabledUserList = ref([])
const chargeList = ref([])
const memberList = ref([])
const queryEmployeeAll = async () => {
  try {
    const result = await userApi.queryEmployeeAll({})
    userList.value = result.data.filter(item => !item.disabledFlag)
    disabledUserList.value = result.data.filter(item => item.disabledFlag)
    // 负责人列表
    chargeList.value = _.cloneDeep(userList.value)
    // 成员列表
    memberList.value = _.cloneDeep(userList.value)
  } catch (e) {
    userList.value = []
    disabledUserList.value = []
    chargeList.value = []
    memberList.value = []
    console.log(e)
  }
}
const userDisabledChange = () => {
  const userListData = _.cloneDeep(userList.value)
  // 负责人列表
  chargeList.value = userListData.filter(item => {
    const isMember = projectFormState.value.memberUserIds.findIndex(member => member === item.employeeId)
    return (isMember === -1 && item.disabledType !== 'member') || item.disabledType === 'charge'
  })
  // 成员列表
  memberList.value = userListData.filter(item => {
    const isMember = projectFormState.value.memberUserIds.findIndex(member => member === item.employeeId)
    if (isMember !== -1) {
      item.disabled = true
    }
    return (projectFormState.value.chargeUserId !== item.employeeId && item.disabledType !== 'charge') || item.disabledType === 'member'
  })
}

const filterOption = (input: string, option: any) => {
  console.log(input, option)
  return option.actualName.indexOf(input) >= 0
}
const handleCancle = () => {
  addEditModalState.value.visible = false
  centerPoint.value = ''
  projectFormState.value = _.cloneDeep(projectFormStateModel)
  projectFormRef.value.resetFields()
}
const handleOk = () => {
  console.log('values', projectFormState.value)
  projectFormRef.value
    .validate()
    .then(async () => {
      if (addEditModalState.value.type === 'add') {
        await projectApi.addProject(projectFormState.value)
        message.success('新增成功')
        searchFormState.name = ''
        onSearch()
      } else {
        await projectApi.updateProject(projectFormState.value)
        message.success('编辑成功')
        queryProjectPage()
      }
      // console.log(dictConfigFormState.value)
      addEditModalState.value.visible = false
      centerPoint.value = ''
      projectFormState.value = _.cloneDeep(projectFormStateModel)
      projectFormRef.value.resetFields()
    })
    .catch((error: ValidateErrorEntity<ProjectFormState>) => {
      console.log('error', error)
      // addEditModalState.value.visible = false
    })
}

// 地图弹框
const mapVisible = ref(false)
const centerPointHandle = () => {
  addressInfo.value = {
    address: projectFormState.value.address,
    longitude: projectFormState.value.lon,
    latitude: projectFormState.value.lat
  }
  mapVisible.value = true
}
const addressInfo = ref({
  address: '',
  longitude: '',
  latitude: ''
})
const handleChildData = (data: string) => {
  console.log(data)
  addressInfo.value = data
}

const handleMapOk = () => {
  console.log(addressInfo.value)
  projectFormState.value.address = addressInfo.value.address
  centerPoint.value = `经纬度：${addressInfo.value.longitude}${addressInfo.value.longitude >= 0 ? 'E' : 'w'} ${addressInfo.value.latitude}${
    addressInfo.value.latitude >= 0 ? 'N' : 'S'
  }`
  projectFormState.value.lon = addressInfo.value.longitude
  projectFormState.value.lat = addressInfo.value.latitude
  mapVisible.value = false
}
const handleMapCancel = () => {
  mapVisible.value = false
}

const addHandle = () => {
  queryEmployeeAll()
  addEditModalState.value.type = 'add'
  addEditModalState.value.visible = true
}
const editHandle = async (item: any) => {
  await queryEmployeeAll()
  const chargeUserId = item.projectMemberVOList.find(userItem => userItem.type === 0).userId || ''
  // 负责人禁用用户
  const disabledChargeUser = _.cloneDeep(disabledUserList.value).find(userItem => userItem.employeeId == chargeUserId)
  if (disabledChargeUser) userList.value.push({ ...disabledChargeUser, disabledType: 'charge', disabled: disabledChargeUser.disabledFlag })
  const memberUserIds = item.projectMemberVOList.filter(item => item.type === 1).map(item => item.userId) || []
  // 项目成员中禁用用户
  const diasbledUserData = disabledUserList.value.filter(userItem => {
    const i = memberUserIds.findIndex(member => member == userItem.employeeId)
    return i !== -1
  })
  const disabledMemberUser = _.cloneDeep(diasbledUserData).map(userItem => ({
    ...userItem,
    disabledType: 'member',
    disabled: userItem.disabledFlag
  }))
  if (disabledMemberUser && disabledMemberUser.length) userList.value.push(...disabledMemberUser)
  addEditModalState.value.type = 'edit'
  addEditModalState.value.visible = true
  projectFormState.value = {
    id: item.id,
    name: item.name,
    des: item.des,
    lon: item.lon,
    lat: item.lat,
    address: item.address,
    chargeUserId: item.projectMemberVOList.find(item => item.type === 0).userId || '',
    memberUserIds: item.projectMemberVOList.filter(item => item.type === 1).map(item => item.userId) || []
  }
  centerPoint.value = `经纬度：${item.lon}${item.lon >= 0 ? 'E' : 'w'} ${item.lat}${item.lat >= 0 ? 'N' : 'S'}`
  userDisabledChange()
}
const delHandle = (item: any) => {
  console.log(item)
  Modal.confirm({
    title: () => '删除提醒',
    icon: () => createVNode(ExclamationCircleOutlined),
    content: () => '确定是否删除该项目？',
    okText: () => '确认',
    okType: 'danger',
    cancelText: () => '取消',
    onOk: async () => {
      try {
        await projectApi.deleteProject(item.id)
        message.success('删除成功')
        if (pagination.value.pageNum > 1 && dataSource.value.length === 1) {
          pagination.value.pageNum = pagination.value.pageNum - 1
        }
        queryProjectPage()
      } catch (e) {
        console.log(e)
      }
    },
    onCancel() {
      console.log('Cancel')
    }
  })
}
const delPerson = (index: number) => {
  projectFormState.value.memberUserIds.splice(index, 1)
  userDisabledChange()
}
const addPerson = () => {
  if (projectFormState.value.memberUserIds.length >= 50) {
    return message.error('最多可添加50个成员')
  }
  projectFormState.value.memberUserIds.push('')
  userDisabledChange()
}
onMounted(() => {
  queryProjectPage()
})
</script>
<style lang="scss">
.projectContainer {
  height: 100%;
  .handleWrap {
    height: 54px;
    padding: 10px;
    box-sizing: border-box;
    .left {
      width: 80%;
      .ant-input {
        box-sizing: border-box;
        width: 100%;
        height: 34px;
      }

      .ant-btn {
        height: 34px;
      }
    }

    .right {
      width: 20%;
      text-align: right;
      .ant-btn {
        height: 34px;
      }
    }
  }

  .contentWrap {
    height: calc(100% - 110px);
    padding: 10px;
    overflow: auto;
    .ant-card {
      height: 100%;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      border-radius: 6px;
      cursor: default;
      &:hover {
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
      }
      .ant-card-body {
        padding: 15px;
      }
      .cardHead {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #000;
        .headIcon {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          background: #d1cdfc;
          text-align: center;
          min-width: 50px;
          .anticon-slack {
            font-size: 30px;
            color: #200de7;
            width: 30px;
            height: 30px;
            margin-top: 10px;
          }
        }

        .cardTitle {
          flex: 1;
          font-size: 16px;
          font-weight: 600;
          margin: 0 10px;
        }
        .ant-dropdown-link {
          color: #000;
          &:hover {
            color: #1890ff;
          }
        }
      }

      .cardInfo {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #999;
        margin: 15px 0;
        .anticon {
          margin-right: 4px;
        }
        .createTime,
        .user {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .createTime {
          min-width: 130px;
        }
      }

      .cardRemarks {
        border-top: 1px solid #eee;
        font-size: 14px;
        line-height: 1.5;
        color: #666;
        padding-top: 10px;
        height: 52px;
        word-break: break-all;
        color: #aaa;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }

  .pageWrap {
    text-align: right;
    margin-top: 20px;
    height: 38px;
    padding-right: 20px;
  }
}
.ant-modal-body {
  padding: 20px 30px 0 20px;
}
.projectHandleForm {
  .ant-input {
    vertical-align: middle;
  }
  .ant-select {
    vertical-align: middle;
  }
  .anticon-environment {
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    color: #666;
    vertical-align: middle;
  }
  .anticon-delete {
    color: red;
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    vertical-align: middle;
  }
  .anticon-plus-circle {
    color: blue;
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    vertical-align: middle;
  }
  .personItem {
    margin-bottom: 20px;
  }
}

.mapContainer {
  height: 500px;
}
</style>
