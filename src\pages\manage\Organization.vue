<!-- 管理工具-组织机构 -->
<template>
  <div class="org orgContainer">
    <div class="handleWrap clearfix">
      <div class="left fl">
        <a-form layout="inline" :model="searchFormState">
          <a-form-item>
            <a-input v-model:value="searchFormState.key" placeholder="请输入组织机构名称" size="small" allowClear @pressEnter="onSearch"></a-input>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="onSearch">搜索</a-button>
          </a-form-item>
        </a-form>
      </div>
    </div>
    <div class="contentWrap custom-scrollbar" ref="tableWrapperRef">
      <a-table
        ref="tableRef"
        :columns="columns"
        :dataSource="departmentTreeData"
        :pagination="false"
        bordered
        rowKey="departmentId"
        :scroll="{ x: 1200,  y: tableHeight }"
        :expandedRowKeys="expandedRowList"
        @expand="onExpand"
        :loading="tableLoading"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'operation'">
            <span>
              <a-button type="link" size="small" @click="addHandle(record)" v-privilege="'system:department:add'">新增下级</a-button>
              <a-button type="link" size="small" @click="editHandle(record)" v-privilege="'system:department:update'">编辑</a-button>
              <a-button
                type="link"
                size="small"
                danger
                @click="delHandle(record)"
                :disabled="record.parentId == 0"
                v-privilege="'system:department:delete'"
              >删除</a-button>
            </span>
          </template>
        </template>
      </a-table>
    </div>
    <a-modal
      v-model:open="addEditModalState.visible"
      :title="addEditModalState.type === 'add' ? '新增组织机构' : '编辑组织机构'"
      width="600px"
      cancelText="取消"
      okText="提交"
      :closable="false"
      :keyboard="false"
      :maskClosable="false"
      @cancel="handleCancle"
      @ok="handleOk"
    >
      <a-form class="orgHandleForm" ref="orgFormRef" :model="orgFormState" :rules="orgRules" :labelCol="{ span: 5 }">
        <a-form-item ref="name" label="组织机构名称" name="name">
          <a-input v-model:value="orgFormState.name" :maxlength="30" />
        </a-form-item>
        <a-form-item ref="parentName" label="上级" name="parentName" v-if="orgFormState.parentId != 0">
          <a-tree-select
            v-if="addEditModalState.type === 'add'"
            v-model:value="orgFormState.parentId"
            style="width: 100%"
            :tree-data="departmentTreeData"
            allow-clear
            search-placeholder="Please select"
            :fieldNames="{ label: 'name', value: 'departmentId', children: 'children' }"
            :getPopupContainer="triggerNode => triggerNode.parentNode"
            :disabled="true"
          />
          <a-tree-select
            v-else
            v-model:value="orgFormState.parentId"
            style="width: 100%"
            :tree-data="lastLevelTreeData"
            allow-clear
            search-placeholder="Please select"
            :fieldNames="{ label: 'name', value: 'departmentId', children: 'children' }"
            :getPopupContainer="triggerNode => triggerNode.parentNode"
          />
        </a-form-item>
        <a-form-item ref="sort" label="排序" name="sort">
          <a-input-number
            ref="sortRef"
            id="inputNumber"
            v-model:value="orgFormState.sort"
            :min="1"
            :max="1000000"
            style="width: 30%;"
            :precision="0"
            v-input-number-change
          />
        </a-form-item>
        <a-form-item ref="remark" label="描述" name="remark">
          <a-textarea v-model:value="orgFormState.remark" placeholder="组织机构描述" :auto-size="{ minRows: 2, maxRows: 3 }" :maxlength="1000" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import {
  PlusOutlined,
  SlackOutlined,
  MoreOutlined,
  FieldTimeOutlined,
  UserOutlined,
  ExclamationCircleOutlined,
  EnvironmentOutlined,
  DeleteOutlined,
  PlusCircleOutlined
} from '@ant-design/icons-vue'
import { Modal, message } from 'ant-design-vue'
import { ValidateErrorEntity } from 'ant-design-vue/es/form/interface'
import { reactive, ref, UnwrapRef, createVNode, onMounted } from 'vue'
import { getTableHeight } from '@/hooks/getTableHeight'
import { organizationApi } from '@/api/manageTool/organizationApi'
import _ from 'lodash'
// 注册组件
defineOptions({
  components: {
    PlusOutlined,
    SlackOutlined,
    MoreOutlined,
    FieldTimeOutlined,
    UserOutlined,
    EnvironmentOutlined,
    DeleteOutlined,
    PlusCircleOutlined
  }
})

/* ====================== 表格 ====================== */
const tableWrapperRef = ref()
const tableRef = ref()
const tableHeight = getTableHeight(tableWrapperRef, tableRef)

const columns = reactive([
  {
    title: '组织机构',
    dataIndex: 'name',
    key: 'name',
    fixed: 'left'
  },
  {
    title: '排序',
    dataIndex: 'sort',
    key: 'sort',
    align: 'center'
  },
  // {
  //   title: '描述',
  //   dataIndex: 'remarks',
  //   key: 'remarks'
  // },
  {
    title: '操作',
    key: 'operation',
    dataIndex: 'operation',
    // slots: { customRender: 'operation' },
    fixed: 'right',
    width: 200,
    align: 'center'
  }
])

const onExpand = (expanded: boolean, record: object) => {
  console.log('onExpand', expanded)
  console.log('onExpand', record)
  if (expanded) {
    // 展开
    expandedRowList.value.push(record.departmentId)
  } else {
    // 收起
    expandedRowList.value = expandedRowList.value.filter(item => item !== record.departmentId)
  }
}

/* ====================== 组织机构树 ====================== */
const DEPARTMENT_PARENT_ID = 0
const DEPARTMENT_PARENT_Name = ''
const tableLoading = ref(false)
// 所有部门列表
const departmentList = ref([])
// 部门树形数据
const departmentTreeData = ref([])
// 存放部门id和部门，用于查找
const idInfoMap = ref(new Map())
// 默认展开的行
const expandedRowList = ref([])
// 查询部门列表并构建部门树
async function queryDepartmentTree() {
  try {
    tableLoading.value = true
    let result = await organizationApi.queryAllDepartment()
    console.log(result)
    let data = result.data
    data.forEach(e => {
      idInfoMap.value.set(e.departmentId, e)
    })
    departmentList.value = _.cloneDeep(data)
    departmentTreeData.value = buildDepartmentTree(_.cloneDeep(data), DEPARTMENT_PARENT_ID, DEPARTMENT_PARENT_Name, 1)
    console.log(departmentTreeData.value)
    let topDepartmentId
    // 默认显示 最顶级ID为列表中返回的第一条数据的ID
    if (!_.isEmpty(departmentTreeData.value) && departmentTreeData.value.length > 0) {
      topDepartmentId = departmentTreeData.value[0].departmentId
    }
    console.log(topDepartmentId)
    if (expandedRowList.value.length == 0) {
      // 默认展开最顶层
      expandedRowList.value = [topDepartmentId]
    }
  } catch (e) {
    // smartSentry.captureError(e);
  } finally {
    tableLoading.value = false
  }
}
// 构建部门树
function buildDepartmentTree(data, parentId, parentName, level) {
  let children = data.filter(e => e.parentId === parentId) || []
  if (!_.isEmpty(children)) {
    children.forEach(e => {
      e.parentName = parentName
      e.level = level
      e.children = buildDepartmentTree(data, e.departmentId, e.name, level + 1)
      // 排序
      if (!_.isEmpty(e.children) && e.children.length > 0) e.children.sort((a, b) => a.sort - b.sort)
    })
    return children
  }
  return null
}
// 根据ID递归筛选部门
function recursionFilterDepartment(resList, id, unshift) {
  let info = idInfoMap.value.get(id)
  if (!info || resList.some(e => e.departmentId === id)) {
    return
  }
  if (unshift) {
    resList.unshift(info)
  } else {
    resList.push(info)
  }
  if (info.parentId && info.parentId !== 0) {
    recursionFilterDepartment(resList, info.parentId, unshift)
  }
}

/* ====================== 搜索 ====================== */
const searchFormState = reactive({
  key: ''
})

function onSearch() {
  if (!searchFormState.key) {
    departmentTreeData.value = buildDepartmentTree(_.cloneDeep(departmentList.value), DEPARTMENT_PARENT_ID, DEPARTMENT_PARENT_Name, 1)
    return
  }
  let originData = departmentList.value.concat()
  if (!originData) {
    return
  }
  // 筛选出名称符合的部门
  let filterDepartment = originData.filter(e => e.name.indexOf(searchFormState.key) > -1)
  let filterDepartmentList = []
  // 循环筛选出的部门 构建部门树
  filterDepartment.forEach(e => {
    recursionFilterDepartment(filterDepartmentList, e.departmentId, false)
  })
  departmentTreeData.value = buildDepartmentTree(filterDepartmentList, DEPARTMENT_PARENT_ID, DEPARTMENT_PARENT_Name, 1)
  let topDepartmentId
  // 默认显示 最顶级ID为列表中返回的第一条数据的ID
  if (!_.isEmpty(departmentTreeData.value) && departmentTreeData.value.length > 0) {
    topDepartmentId = departmentTreeData.value[0].departmentId
  }
  console.log(topDepartmentId)
  // 默认展开最顶层
  expandedRowList.value = [topDepartmentId]
}

/* ====================== 表单(新增下级|编辑弹窗) ====================== */
interface orgFormState {
  // 部门名称
  name: string
  // 排序
  sort: number | null
  // 部门负责人ID
  managerId?: number | null
  // 上级部门ID
  parentId?: number | null
  // 上级部门名称
  parentName?: string | null
  // 部门ID(新增时为空，编辑时必传)
  departmentId?: number | null
  remark?: string | null
}
const orgFormModel = {
  name: '',
  sort: 1,
  parentId: null,
  parentName: null,
  managerId: null,
  remark: null,
  departmentId: null
}
const orgFormState = ref<orgFormState>(_.cloneDeep(orgFormModel))
const orgRules = reactive({
  name: [{ required: true, message: '请输入组织机构名称' }],
  sort: [{ required: true, message: '请输入排序' }]
})
const orgFormRef = ref()
const sortRef = ref()
const lastLevelTreeData = ref([])
// 过滤掉当前机构及下级机构
const filterDepartmentListData = (data: any[], departmentId: number | null) => {
  // 判断一个部门是否是另一个部门的子部门(向上一级一级查找，如果找到的上级包含当前层级，返回true)
  const isChildDepartment = (department: any, departmentId: number | null, data: any[]): boolean => {
    if (department.parentId === departmentId) {
      return true
    }
    if (department.parentId === null || department.parentId === undefined) {
      return false
    }
    const parentDepartment = _.cloneDeep(data).find(d => d.departmentId === department.parentId)
    if (!parentDepartment) {
      return false
    }
    return isChildDepartment(parentDepartment, departmentId, _.cloneDeep(data))
  }
  return data.filter(
    department => department.departmentId !== departmentId && !isChildDepartment(department, departmentId, _.cloneDeep(data))
  )
}

interface AddEditModalModel {
  visible: boolean
  type: '' | 'add' | 'edit'
}
const addEditModalState = ref<AddEditModalModel>({
  visible: false,
  type: ''
})
const handleCancle = () => {
  addEditModalState.value.visible = false
  orgFormState.value = _.cloneDeep(orgFormModel)
  orgFormRef.value.resetFields()
}
const handleOk = () => {
  orgFormRef.value
    .validate()
    .then(async () => {
      try {
        if (addEditModalState.value.type === 'add') {
          await organizationApi.addDepartment(orgFormState.value)
          message.success('新增成功')
          searchFormState.key = ''
          queryDepartmentTree()
        } else {
          await organizationApi.updateDepartment(orgFormState.value)
          message.success('编辑成功')
          queryDepartmentTree()
        }
        // console.log(dictConfigFormState.value)
        addEditModalState.value.visible = false
        orgFormState.value = _.cloneDeep(orgFormModel)
        orgFormRef.value.resetFields()
      } catch (e) {
        console.log(e)
      }
    })
    .catch((error: ValidateErrorEntity) => {
      console.log('error', error)
    })
}

/* ====================== 操作按钮 ====================== */
const addHandle = (parent: any) => {
  if (parent.level === 10) {
    message.error('最多只能添加10级组织机构')
    return
  }
  addEditModalState.value.type = 'add'
  addEditModalState.value.visible = true
  orgFormState.value.parentId = parent.departmentId
  orgFormState.value.parentName = parent.name
}
const editHandle = (item: any) => {
  const filterDepartmentData = filterDepartmentListData(_.cloneDeep(departmentList.value), item.departmentId)
  lastLevelTreeData.value = buildDepartmentTree(filterDepartmentData, DEPARTMENT_PARENT_ID, DEPARTMENT_PARENT_Name, 1)
  addEditModalState.value.type = 'edit'
  addEditModalState.value.visible = true
  orgFormState.value.parentId = item.parentId
  orgFormState.value.parentName = item.parentName
  orgFormState.value.name = item.name
  orgFormState.value.departmentId = item.departmentId
  orgFormState.value.remark = item.remark
  orgFormState.value.sort = item.sort
}
const delHandle = (item: any) => {
  console.log(item)
  Modal.confirm({
    title: () => '删除提醒',
    icon: () => createVNode(ExclamationCircleOutlined),
    content: () => '确定是否删除该组织机构？',
    okText: () => '确认',
    okType: 'danger',
    cancelText: () => '取消',
    onOk: async () => {
      try {
        await organizationApi.deleteDepartment(item.departmentId)
        message.success('删除成功')
        queryDepartmentTree()
      } catch (e) {
        console.log(e)
      }
    },
    onCancel() {
      console.log('Cancel')
    }
  })
}
onMounted(() => {
  queryDepartmentTree()
})
</script>
<style lang="scss">
.orgContainer {
  height: 100%;

  .handleWrap {
    height: 54px;
    padding: 10px;
    box-sizing: border-box;

    .left {
      width: 80%;

      .ant-input {
        box-sizing: border-box;
        width: 240px;
        height: 34px;
      }

      .ant-btn {
        height: 34px;
      }
    }

    .right {
      width: 20%;
      text-align: right;

      .ant-btn {
        height: 34px;
      }
    }
  }

  .contentWrap {
    height: calc(100% - 72px);
    padding: 10px;
    overflow: hidden;

    .ant-card {
      height: 100%;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      border-radius: 6px;
      cursor: default;

      &:hover {
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
      }

      .ant-card-body {
        padding: 15px;
      }

      .cardHead {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #000;

        .headIcon {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          background: #d1cdfc;
          text-align: center;
          min-width: 50px;

          .anticon-slack {
            font-size: 30px;
            color: #200de7;
            width: 30px;
            height: 30px;
            margin-top: 10px;
          }
        }

        .cardTitle {
          font-size: 14px;
          font-weight: 600;
          margin: 0 10px;
        }

        .ant-dropdown-link {
          color: #000;

          &:hover {
            color: #1890ff;
          }
        }
      }

      .cardInfo {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #999;
        margin: 10px 0;

        .anticon {
          margin-right: 4px;
        }
      }

      .cardRemarks {
        border-top: 1px solid #eee;
        font-size: 12px;
        color: #666;
        padding-top: 10px;
      }
    }
  }
}

.ant-modal-body {
  padding: 20px 30px 0 20px;
}

.orgHandleForm {
  .ant-input {
    vertical-align: middle;
  }

  .ant-select {
    vertical-align: middle;
  }

  .anticon-environment {
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    color: #666;
    vertical-align: middle;
  }

  .anticon-delete {
    color: red;
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    vertical-align: middle;
  }

  .anticon-plus-circle {
    color: blue;
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    vertical-align: middle;
  }

  .targetItem {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.resetPwdTips {
  text-align: center;
  margin: 20px 0 30px;
  font-size: 20px;
}
</style>
