import { getRequest, postRequest } from '../http/request'

export const organizationApi = {
  // 查询部门列表
  queryAllDepartment: () => {
    return getRequest('/department/listAll', {})
  },
  // 添加部门
  addDepartment: (param: object) => {
    return postRequest('/department/add', param)
  },
  // 更新部门
  updateDepartment: (param: object) => {
    return postRequest('/department/update', param)
  },
  // 删除部门
  deleteDepartment: (id: string | number) => {
    return getRequest(`/department/delete/${id}`, {})
  },
  // 查询部门树形列表
  getDepartmentTreeList: () => {
    return getRequest(`/department/treeList`, {})
  },
}
