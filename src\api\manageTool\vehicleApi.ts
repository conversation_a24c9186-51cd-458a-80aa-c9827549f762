import { getRequest, postRequest } from '../http/request'

export const vehicleApi = {
  // 分页查询
  queryVehiclePage: (param: object) => {
    return postRequest('/vehicle/queryPage', param)
  },
  // 添加
  addVehicle: (param: object) => {
    return postRequest('/vehicle/add', param)
  },
  // 更新
  updateVehicle: (param: object) => {
    return postRequest('/vehicle/update', param)
  },
  // 删除
  deleteVehicle: (id: string | number) => {
    return getRequest(`/vehicle/delete/${id}`, {})
  },
}
