<template>
  <div class="home homeContainer">
    <div class="mapWrap">
      <HomeCesiumMap :pointsArr="pointArray" />
    </div>
    <div class="leftSide">
      <h4>项目列表</h4>
      <div class="search">
        <a-input-search v-model:value="projectName" placeholder="请输入项目名称" @search="onSearch" />
      </div>
      <div class="noProject" v-if="dataSource.length === 0">
        <p>暂无可用项目</p>
        <p>请联系管理员或项目负责人分配项目</p>
      </div>
      <ul class="projectList custom-scrollbar" v-else>
        <li :class="['projectItem']" v-for="(item, index) in dataSource" :key="index" @click="projectSelect(item)">
          <div class="name single-line" :title="item.name">{{ item.name }}</div>
          <div class="types">
            <span v-for="(vehicleItem, vehicleIndex) in item.vehicleTypes" :key="vehicleIndex">{{
              computedVehicleType(vehicleItem) }}</span>
          </div>
          <div class="des multi-line">{{ item.des }}</div>
          <div class="createdTime">创建时间：{{ item.createTime }}</div>
          <div class="enterBtn" @click="toWorkspace(item)">
            <DeliveredProcedureOutlined />
          </div>
        </li>
      </ul>
    </div>
    <div class="rightSide custom-scrollbar">
      <div class="top">
        <h4>项目资源</h4>
        <ul class="countList">
          <li>
            <div class="countTitle">
              <RocketOutlined /><span>载具数量（个）</span>
            </div>
            <p>{{ resources.onlineVehicleCount }}/ {{ resources.vehicleSum }}</p>
          </li>
          <li>
            <div class="countTitle">
              <InstagramOutlined /><span>检测设备负载数量（个）</span>
            </div>
            <p>{{ resources.onlineDevicePayloadCount }}/{{ resources.devicePayloadSum }}</p>
          </li>
          <li>
            <div class="countTitle">
              <VideoCameraOutlined /><span>摄像机负载数量（个）</span>
            </div>
            <p>{{ resources.onlineCameraPayloadCount }}/{{ resources.cameraPayloadSum }}</p>
          </li>
          <li>
            <div class="countTitle">
              <HddOutlined /><span>媒体存储（G）</span>
            </div>
            <p>{{ resources.mediaStorage }}</p>
          </li>
        </ul>
      </div>
      <div class="bottom">
        <h4>今日巡检概况</h4>
        <ul class="countList">
          <li>
            <div class="countTitle">
              <RadiusSettingOutlined /><span>巡检次数</span>
            </div>
            <p>{{ resources.inspectionCount }}</p>
          </li>
          <li class="riskItem">
            <div class="countTitle">
              <WarningOutlined /><span>风险处数量</span>
            </div>
            <p @click="riskListShow">{{ resources.riskCount }}</p>
          </li>
        </ul>
      </div>
    </div>
    <a-drawer title="风险明细" placement="right" width="800px" v-model:open="riskVisible">
      <a-table ref="tableRef" :columns="columns" :dataSource="riskDataSource" :pagination="false" bordered :rowKey="(record: any) => record.id">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'maxValue'">
            {{ `${record.maxValue} (${record.detectTargetUnit})` }}
          </template>
        </template>
        <template #operation="{ record }">
          <span>
            <a @click="toWorkRecord(record)">作业记录</a>
          </span>
        </template>
      </a-table>
      <div class="homeRiskPager">
        <a-pagination size="small" v-model:current="pagination.pageNum" :page-size="pagination.pageSize"
          :total="riskTotal" show-size-changer show-quick-jumper :show-total="(total: number) => `共 ${total} 条`"
          :page-size-options="PAGE_SIZE_OPTIONS"
          @change="(current: number, size: number) => onPageChange('current', current, size)"
          @showSizeChange="(current: number, size: number) => onPageChange('size', current, size)" />
      </div>
    </a-drawer>
  </div>
</template>

<script lang="ts" setup>
import HomeCesiumMap from '@/components/home/<USER>';
import CesiumGaodeMap from '@/components/cesiumMap/CesiumGaodeMap.vue';

import {
  DeliveredProcedureOutlined,
  RocketOutlined,
  InstagramOutlined,
  VideoCameraOutlined,
  HddOutlined,
  RadiusSettingOutlined,
  WarningOutlined
} from '@ant-design/icons-vue';
import { ref, onMounted, reactive } from "vue";
import { workspaceApi } from '@/api/workspace/workspaceApi'
import { dictApi } from '@/api/manageTool/dictApi'
import { getRoot } from '@/root'
import { ERouterName } from '@/types'
import { useRouter } from "vue-router";
import { PageParamModel } from '@/types/paging'
import { PAGE_SIZE_OPTIONS } from '@/utils/constants'
import rootStore from "@/store/index";

// 注册组件
defineOptions({
  components: {
    HomeCesiumMap,
    DeliveredProcedureOutlined,
    RocketOutlined,
    InstagramOutlined,
    VideoCameraOutlined,
    HddOutlined,
    RadiusSettingOutlined,
    WarningOutlined,
    CesiumGaodeMap
  }
})

const root = getRoot()
const router = useRouter()

const projectName = ref('');

const onSearch = () => {
  queryProjectList()
}

const projectLoading = ref(false)
let dataSource = ref<any>([])
let pointArray = ref<any>([])
const queryProjectList = async () => {
  try {
    projectLoading.value = true
    const result = await workspaceApi.getProjectList({
      name: projectName.value
    })
    dataSource.value = [...result.data]
    pointArray.value = [...result.data]
  } catch (e) {
    dataSource.value = []
  } finally {
    projectLoading.value = false
  }
}

const selectProfect = ref<any>()
const projectSelect = (item: any) => {
  selectProfect.value = item
}

const resourcesLoading = ref(false)
let resources = reactive<any>({
  cameraPayloadSum: 0,
  devicePayloadSum: 0,
  inspectionCount: 0,
  mediaStorage: 0,
  onlineCameraPayloadCount: 0,
  onlineDevicePayloadCount: 0,
  onlineVehicleCount: 0,
  riskCount: 0,
  vehicleSum: 0
})
const queryResources = async () => {
  try {
    resourcesLoading.value = true
    const result = await workspaceApi.getResources()
    resources = Object.assign(resources, result.data)
  } catch (e) {
    resources = {}
  } finally {
    resourcesLoading.value = false
  }
}

const toWorkspace = (item: any) => {
  localStorage.setItem('projectSelectedId', item.id)
  // 使用 Vue Router 的 resolve 方法获取完整 URL
  const routeData = router.resolve('/' + ERouterName.WORKSPACE + '/' + ERouterName.PROJECTINFO + '?id=' + item.id);
  window.open(routeData.href, '_blank'); // 打开新窗口
}

let vehicleTypes = ref<any>([])
const queryDict = async () => {
  try {
    const result = await dictApi.queryDictValue({
      dictKeyId: 5,
      pageNum: 1, // 当前页码
      pageSize: 100 // 每页数量
    })
    vehicleTypes.value = result.data.list
  } catch (e) {
    vehicleTypes.value = []
  }
}

const computedVehicleType = (vehicleType: string) => {
  const result = vehicleTypes.value.find((item: any) => item.valueCode === vehicleType)
  return result ? result.valueName : ''
}

const riskVisible = ref(false)
const riskListShow = () => {
  riskVisible.value = true
  queryRiskList()
}

const columns = reactive([
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    customRender: ({ index }: any) => (pagination.value.pageNum - 1) * pagination.value.pageSize + index + 1,
    width: 70
  },
  {
    title: '检测时间',
    dataIndex: 'triggerTime',
    key: 'triggerTime',
    width: 160
  },
  {
    title: '检测浓度(单位)',
    dataIndex: 'maxValue',
    key: 'maxValue',
    width: 130
  },
  {
    title: '检测目标',
    dataIndex: 'detectTargetName',
    key: 'detectTargetName',
    width: 100
  },
  {
    title: '项目',
    dataIndex: 'projectName',
    key: 'projectName'
  },
  {
    title: '载具',
    dataIndex: 'vehicleName',
    key: 'vehicleName',
    width: 100
  },
  {
    title: '操作',
    key: 'operation',
    slots: { customRender: 'operation' },
    width: 100
  }
])

let riskDataSource = ref<any[]>([])

const pagination = ref<PageParamModel>({
  pageNum: 1, // 当前页码
  pageSize: 10 // 每页数量
})
const riskTotal = ref<number>(0)
const onPageChange = (type: string, pageNum: number, pageSize: number) => {
  if (type === 'current') {
    pagination.value.pageNum = pageNum
  } else {
    pagination.value.pageNum = 1
    pagination.value.pageSize = pageSize
  }
  queryRiskList()
}

const queryRiskList = async () => {
  try {
    const result = await workspaceApi.queryRiskList({
      ...pagination.value,
      searchCount: true,
      sortItemList: [
        {
          isAsc: true,
          column: "trigger_time" 
        }
      ]
    })
    riskDataSource.value = result.data.list
    riskTotal.value = result.data.total
  } catch (e) {
    riskDataSource.value = []
    riskTotal.value = 0
  }
}

const toWorkRecord = (item: any) => {
  localStorage.setItem('projectSelectedId', item.projectId)
  // 使用 Vue Router 的 resolve 方法获取完整 URL
  const routeData = router.resolve('/' + ERouterName.WORKSPACE + '/workRecord?id=' + item.projectId);
  rootStore.dispatch("workspace/workrecordSelected", item.jobId);
  localStorage.setItem('selectedJob', JSON.stringify(item))
  window.open(routeData.href, '_blank'); // 打开新窗口
}

onMounted(() => {
  queryDict()
  queryProjectList()
  queryResources()
  document.title = '无人载具与作业管理平台'
})

</script>
<style lang="scss">
.homeContainer {
  width: 100%;
  height: 100%;
  position: relative;
  background: rgb(210, 210, 214);

  .leftSide {
    position: absolute;
    width: 300px;
    top: 0;
    left: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    color: #fff;

    h4 {
      color: #fff;
      font-size: 18px;
      font-weight: bold;
      height: 60px;
      padding: 20px 10px;
    }

    .search {
      padding: 0 4px;
      height: 30px;

      .ant-input-search {
        height: 28px;
        padding: 0 10px;
        background: transparent;
        color: #fff !important;

        // border: 1px solid rgba(255, 255, 255, 0.4);
        .ant-input {
          height: 28px;
          background: transparent;
          color: #fff !important;
          border-radius: 0;
          border-color: rgba(255, 255, 255, 0.4);
        }

        .ant-input::placeholder {
          color: #fff !important;
        }

        .ant-input-group-addon {
          .ant-input-search-button {
            color: rgba(255, 255, 255, 0.4) !important;
            background: transparent;
            border-radius: 0;
            height: 28px;
            border-color: rgba(255, 255, 255, 0.4);
          }
        }
      }
    }

    .noProject {
      color: #bbb7b7;
      padding: 15px;
      line-height: 26px;
      text-align: center;
    }

    .projectList {
      margin-top: 15px;
      padding-bottom: 15px;
      overflow-y: auto;
      height: calc(100% - 105px);

      .projectItem {
        padding: 10px 45px 10px 10px;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;

        &:last-child {
          border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        &.active {
          background: rgba(0, 0, 0, 0.6);
        }

        .name {
          height: 30px;
          font-size: 18px;
          font-weight: bold;
          vertical-align: middle;
          line-height: 30px;
          margin-right: 10px;
        }

        .types {
          margin: 6px 0 10px;

          span {
            display: inline-block;
            height: 18px;
            line-height: 16px;
            font-size: 10px;
            border: 1px solid rgb(4, 197, 4);
            color: rgb(4, 197, 4);
            vertical-align: middle;
            padding: 0 4px;
            margin-right: 4px;
          }
        }

        .des {
          line-height: 16px;
          margin-bottom: 10px;
        }

        .createdTime {
          font-size: 12px;
          color: #b9b7b7;
        }

        .enterBtn {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 40px;
          background: rgba(0, 0, 0, 0.3);
          font-size: 18px;
          cursor: pointer;

          .anticon-delivered-procedure {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }

        &:hover {
          background: rgba(0, 0, 0, 0.6);

          .enterBtn {
            background: rgba(0, 0, 0, 0.9);
          }
        }
      }
    }
  }

  .mapWrap {
    width: 100%;
    height: 100%;
  }

  .rightSide {
    position: absolute;
    width: 250px;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    color: #fff;

    h4 {
      color: #fff;
      font-size: 18px;
      font-weight: bold;
      height: 60px;
      padding: 20px 10px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .countList {
      margin-bottom: 10px;

      li {
        padding: 10px 30px;

        .countTitle {
          font-size: 14px;
          color: #fff;

          .anticon {
            font-size: 20px;
            vertical-align: middle;
            margin-right: 10px;
          }

          span {
            vertical-align: middle;
          }
        }

        p {
          font-size: 24px;
          margin-top: 15px;
          padding-left: 30px;
        }
      }

      .riskItem {
        p {
          cursor: pointer;

          &:hover {
            color: red;
          }
        }
      }
    }
  }
}

.homeRiskPager {
  text-align: right;
  margin-top: 20px;
}</style>
