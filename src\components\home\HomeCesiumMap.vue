<template>
  <div class="cesiumMapContainer">
    <iconPop v-if="popFlag && popIsShow" ref="mapPopup" :popupData="popData"></iconPop>
    <div id="cesiumContainer" class="cesiumContainer" ref="cesiumContainer"></div>
  </div>
</template>

      
<script lang="ts" setup >
import {
  watch,
  onBeforeUnmount,
  ref
} from 'vue'
import { useRoute } from 'vue-router';
import { initCesium, viewer } from '@/components/cesiumMap/initCesium';
import { useCesium } from '@/components/cesiumMap/useCesium';
import iconPop from '@/components/cesiumMap/IconPop.vue';
import marker from "@/assets/images/marker.png";

initCesium()
const { addpointNoLabel, setPopEvent, flyToEntitiesCenter, popFlag, popData, mapPopup, closeMapPopup } = useCesium();

// 定义props
const props = defineProps({
  // 地图缩放级别
  pointsArr: {
    type: Array,
    default: [],
  }
});

const popIsShow = ref(false);
watch(() => props.pointsArr, (newValue, oldValue) => {
  if (newValue.length > 0) {
    popIsShow.value = true
    setPoint(newValue, popIsShow.value)
  }
});

const setPoint = (points: any, issetPopEvent: boolean = true) => {
  points.forEach((point: any) => {
    addpointNoLabel(point, marker);
  });
  setPopEvent();
  flyToEntitiesCenter(points);
};

onBeforeUnmount(() => {
  if(viewer) {
    viewer.destroy();
  }
});


</script>
    
<style scoped>
.cesiumMapContainer{
  width: 100%;
  height: 100%;
}
.cesiumContainer {
  width: 100%;
  height: 100%;
}
</style>
