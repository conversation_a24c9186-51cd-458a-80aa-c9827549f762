{"name": "unmannedvehicle", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "serve": "vite --host", "build:test": "vite build --mode stag", "build": "vite build && node copy-assets.js", "preview": "vite preview", "lint": "eslint --fix"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.2.6", "axios": "^1.9.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "event-source-polyfill": "^1.0.31", "flv.js": "^1.6.2", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lodash": "^4.17.21", "moment": "^2.30.1", "sm-crypto": "^0.3.13", "vue": "^3.2.26", "vue-router": "^4.5.1", "vuex": "^4.1.0"}, "devDependencies": {"@turf/turf": "^7.2.0", "@types/crypto-js": "^4.2.2", "@types/lodash": "^4.17.16", "@types/node": "^22.15.17", "@types/sm-crypto": "^0.3.4", "@vitejs/plugin-vue": "^2.3.4", "@vue/tsconfig": "^0.7.0", "bezier-js": "^6.1.4", "cesium": "1.127.0", "fs-extra": "^11.3.0", "rollup-plugin-external-globals": "^0.6.1", "sass": "^1.88.0", "typescript": "~4.5.4", "vite": "^2.4.0", "vite-plugin-cesium": "^1.2.23", "vue-tsc": "^1.8.27"}}