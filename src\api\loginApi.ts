import { getRequest, postRequest } from './http/request'
export interface LoginBody {
  captchaCode: string
  captchaUuid: string
  loginName: string
  password: string
  loginDevice: number
  emailCode: string
}

export const loginApi = {
  // 登录
  login: (param: LoginBody) => {
    return postRequest('/login', param)
  },
  // 退出登录
  logout: () => {
    return getRequest('/login/logout', {})
  },
  // 获取登录信息
  getLoginInfo: () => {
    return getRequest('/login/getLoginInfo', {})
  }
}
