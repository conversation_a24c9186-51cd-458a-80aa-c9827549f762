<template>
  <div class="cesiumMapContainer">
    <iconPop v-if="popFlag && popIsShow" ref="mapPopup" :popupData="popData" @closePop="closeMapPopup"></iconPop>
    <div id="cesiumContainer" class="cesiumContainer" ref="cesiumContainer"></div>
  </div>
</template>

      
<script lang="ts" setup >
import {
  watch,
  onBeforeUnmount,
  ref
} from 'vue'
import { useRoute } from 'vue-router';
import { initCesium, viewer } from './initCesium';
import { useCesium } from './useCesium';
import iconPop from './IconPop.vue';

initCesium()
const { setPoint, popFlag, popData, mapPopup, closeMapPopup, addTrack, addSampleTrack, clearMap } = useCesium();

// 定义props
const props = defineProps({
  // 地图缩放级别
  pointsArr: {
    type: Array,
    default: [],
  },
  trackData: {
    type: Object,
    default: () => {}
  },
  sampleTrackData: {
    type: Object,
    default: () => {}
  }
});

const route = useRoute();

watch(() => route.path, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    clearMap()
  }
})

const popIsShow = ref(false);
watch(() => props.pointsArr, (newValue, oldValue) => {
  if (newValue.length > 0) {
    if(route.name === 'home') {
      popIsShow.value = true
    } else {
      popIsShow.value = false
    }
    setPoint(newValue, popIsShow.value)
    // setTimeout(() => {
    // }, 3000)
  }
});

watch(() => props.trackData, (newValue, oldValue) => {
  if (newValue.length > 0) {
    addTrack(newValue)
  }
});

watch(() => props.sampleTrackData, (newValue, oldValue) => {
  if (newValue.length > 0) {
    addSampleTrack(newValue)
  }
});

onBeforeUnmount(() => {
  if(viewer) {
    viewer.destroy();
  }
});


</script>
    
<style scoped>
.cesiumMapContainer{
  width: 100%;
  height: 100%;
}
.cesiumContainer {
  width: 100%;
  height: 100%;
}
</style>
