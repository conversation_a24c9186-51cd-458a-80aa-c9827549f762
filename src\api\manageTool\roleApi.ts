import { getRequest, postRequest } from '../http/request'

export const roleApi = {
  // 分页查询角色列表
  queryRolePage: (param: object) => {
    return postRequest('/role/queryPage', param)
  },
  // 添加
  addRole: (param: object) => {
    return postRequest('/role/add', param)
  },
  // 更新
  updateRole: (param: object) => {
    return postRequest('/role/update', param)
  },
  // 删除
  deleteRole: (roleId: string | number) => {
    return getRequest(`/role/delete/${roleId}`, {})
  },
  // 获取角色关联菜单权限
  getRoleSelectedMenu: (roleId: string | number) => {
    return getRequest(`/role/menu/getRoleSelectedMenu/${roleId}`, {})
  },
  // 获取所有角色
  getRoleAll: () => {
    return getRequest(`/role/getAll`, {})
  },
}
