import { InjectionKey } from 'vue'
import { ActionTree, createStore, GetterTree, MutationTree, Store, StoreOptions, useStore } from 'vuex'
import { EDeviceTypeName } from '../types'
import user from './mudules/user';
import workspace from "./mudules/workspace";

const getters: any ={
}
const mutations: any = {
}

const actions: any = {
}

const storeOptions: any = {
  state: {},
  getters,
  mutations,
  actions,
  modules: {
    user,
    workspace,
  },
};

const rootStore = createStore(storeOptions)

export default rootStore

