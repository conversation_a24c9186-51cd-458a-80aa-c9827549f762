<template>
  <a-config-provider :locale="locale">
    <div class="app">
      <router-view />
    </div>
  </a-config-provider>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import zhCN from 'ant-design-vue/es/locale/zh_CN' // 引入 Ant Design Vue 的中文语言包

const locale = ref(zhCN)
</script>
<style lang="scss">
// @import "@/styles/reset.scss";
// @import "@/styles/common.scss";
@use '@/styles/reset.scss' as *;
@use '@/styles/common.scss' as *;
.app {
  width: 100%;
  height: 100vh;
}

.ant-modal-wrap {
  overflow: hidden !important;
  .ant-modal {
    height: 100%;
    overflow: hidden;
    & > div:nth-child(1) {
      height: 100%;
      overflow: hidden;
      .ant-modal-content {
        max-height: calc(100% - 130px);
        overflow: hidden;
        display: flex;
        flex-direction: column;
        .ant-modal-body {
          flex: 1;
          overflow: auto;
        }
      }
    }
  }
}

</style>
