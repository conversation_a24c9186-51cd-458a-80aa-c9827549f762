<template>
  <div style="height: 100%; display: flex; flex-direction: column;">
    <a-form class="smart-query-form" style="padding: 10px;">
      <a-row class="smart-query-form-row">
        <a-form-item label="关键字" class="smart-query-form-item">
          <a-input style="width: 300px" v-model:value="queryForm.keywords" placeholder="菜单名称/路由地址/组件路径/权限字符串" />
        </a-form-item>

        <a-form-item label="类型" class="smart-query-form-item">
          <!-- <SmartEnumSelect width="120px" v-model:value="queryForm.menuType" placeholder="请选择类型" enum-name="MENU_TYPE_ENUM" /> -->
          <a-select ref="select" v-model:value="queryForm.menuType" style="width: 120px">
            <a-select-option :value="1">目录</a-select-option>
            <a-select-option :value="2">菜单</a-select-option>
            <a-select-option :value="3">按钮</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="禁用" class="smart-query-form-item">
          <!-- <SmartEnumSelect width="120px" enum-name="FLAG_NUMBER_ENUM" v-model:value="queryForm.disabledFlag" /> -->
          <a-select ref="select" v-model:value="queryForm.disabledFlag" style="width: 120px">
            <a-select-option :value="1">是</a-select-option>
            <a-select-option :value="0">否</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item class="smart-query-form-item smart-margin-left10">
          <a-button-group>
            <a-button type="primary" @click="queryMenu">搜索</a-button>

            <a-button @click="resetQuery">重置</a-button>
          </a-button-group>
          <a-button class="smart-margin-left20" @click="moreQueryConditionFlag = !moreQueryConditionFlag">
            <template #icon>
              <MoreOutlined />
            </template>
            {{ moreQueryConditionFlag ? '收起' : '展开' }}
          </a-button>
        </a-form-item>
      </a-row>

      <a-row class="smart-query-form-row" v-show="moreQueryConditionFlag">
        <a-form-item label="外链" class="smart-query-form-item">
          <!-- <SmartEnumSelect width="120px" enum-name="FLAG_NUMBER_ENUM" v-model:value="queryForm.frameFlag" /> -->
          <a-select ref="select" v-model:value="queryForm.frameFlag" style="width: 120px">
            <a-select-option :value="1">是</a-select-option>
            <a-select-option :value="0">否</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="缓存" class="smart-query-form-item">
          <!-- <SmartEnumSelect width="120px" enum-name="FLAG_NUMBER_ENUM" v-model:value="queryForm.cacheFlag" /> -->
          <a-select ref="select" v-model:value="queryForm.cacheFlag" style="width: 120px">
            <a-select-option :value="1">是</a-select-option>
            <a-select-option :value="0">否</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="显示" class="smart-query-form-item">
          <!-- <SmartEnumSelect width="120px" enum-name="FLAG_NUMBER_ENUM" v-model:value="queryForm.visibleFlag" /> -->
          <a-select ref="select" v-model:value="queryForm.visibleFlag" style="width: 120px">
            <a-select-option :value="1">是</a-select-option>
            <a-select-option :value="0">否</a-select-option>
          </a-select>
        </a-form-item>
      </a-row>
    </a-form>
    <a-row class="smart-table-btn-block" style="padding: 0 10px;">
      <div class="smart-table-operate-block">
        <a-button type="primary" @click="showDrawer" v-privilege="'system:menu:add'">
          <template #icon>
            <PlusOutlined />
          </template>
          添加菜单
        </a-button>

        <a-button type="primary" danger @click="batchDelete" :disabled="!hasSelected" v-privilege="'system:menu:batchDelete'">
          <template #icon>
            <DeleteOutlined />
          </template>
          批量删除
        </a-button>
      </div>
    </a-row>
    <div style="flex: 1;min-height: 0; display: flex; flex-direction: column;">
      <div style="flex: 1;min-height: 0; padding: 10px;" ref="tableWrapperRef">
        <a-table
          ref="tableRef"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          size="small"
          :scroll="{ x: 1200, y: tableHeight }"
          :defaultExpandAllRows="true"
          :dataSource="tableData"
          bordered
          :columns="columns"
          :loading="tableLoading"
          rowKey="menuId"
          :pagination="false"
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.dataIndex === 'operation'">
              <div class="smart-table-operate">
                <a-button type="link" size="small" @click="showDrawer(record)" v-privilege="'system:menu:update'">编辑</a-button>
                <a-button danger type="link" size="small" @click="singleDelete(record)" v-privilege="'system:menu:batchDelete'">删除</a-button>
              </div>
            </template>
            <template v-if="column.dataIndex === 'icon'">
              <component :is="$antIcons[record.icon]" />
            </template>
            <template v-if="column.dataIndex === 'disabledFlag'">
              <a-switch
                :checked="record.disabledFlag"
                :checkedValue="false"
                :unCheckedValue="true"
                checked-children="启用"
                un-checked-children="禁用"
                @change="(checked) => disabledFlagChange(checked, record)"
              />
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <MenuOperateModal ref="menuOperateModal" @reloadList="queryMenu" />
  </div>
</template>
<script setup lang="ts">
import _ from 'lodash'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { computed, createVNode, onBeforeUpdate, onMounted, reactive, ref } from 'vue'
import { buildMenuTableTree, filterMenuByQueryForm } from '@/utils/menuDataHandler'
import MenuOperateModal from '@/components/Menu/menu-operate-modal.vue'
import { menuApi } from '@/api/manageTool/menuApi'
import { getTableHeight } from '@/hooks/getTableHeight'

// 注册组件
defineOptions({
  components: {
    MenuOperateModal
  }
})

/* ====================== 搜索 ====================== */
const queryFormState = {
  keywords: '',
  menuType: undefined,
  frameFlag: undefined,
  cacheFlag: undefined,
  visibleFlag: undefined,
  disabledFlag: undefined
}
const queryForm = reactive({ ...queryFormState })
// 展开更多查询参数
const moreQueryConditionFlag = ref(true)
function resetQuery() {
  Object.assign(queryForm, queryFormState)
  queryMenu()
}
/* ====================== 表格 ====================== */
const tableWrapperRef = ref()
const tableRef = ref()
const tableHeight = getTableHeight(tableWrapperRef, tableRef)
const columns = ref([
  {
    title: '名称',
    dataIndex: 'menuName',
    key: 'ID',
    width: 200,
    fixed: 'left'
  },
  {
    title: '类型',
    dataIndex: 'menuType',
    width: 80,
    customRender: ({ index, record }) => {
      return record.menuType === 1 ? '目录' : record.menuType === 2 ? '菜单' : '按钮'
    },
    align: 'center'
  },
  {
    title: '图标',
    dataIndex: 'icon',
    width: 50,
    // slots: { customRender: 'icon' },
    align: 'center'
  },
  {
    title: '路径',
    dataIndex: 'path',
    ellipsis: true
  },
  {
    title: '组件',
    dataIndex: 'component',
    ellipsis: true
  },
  {
    title: '后端权限',
    dataIndex: 'apiPerms',
    ellipsis: true
  },
  {
    title: '前端权限',
    dataIndex: 'webPerms',
    ellipsis: true
  },
  {
    title: '顺序',
    dataIndex: 'sort',
    width: 100,
    align: 'center'
  },
  {
    title: '状态',
    dataIndex: 'disabledFlag',
    // slots: { customRender: 'disabledFlag' },
    ellipsis: true,
    fixed: 'right',
    align: 'center',
    width: 100
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 100,
    fixed: 'right',
    // slots: { customRender: 'operation' },
    align: 'center'
  }
])
const tableLoading = ref(false)
const tableData = ref([])

async function queryMenu() {
  try {
    tableLoading.value = true
    let result = await menuApi.queryMenu()
    // 过滤搜索条件
    const filtedMenuList = filterMenuByQueryForm(result.data, queryForm)
    // 递归构造树形结构，并付给 TableTree组件
    tableData.value = buildMenuTableTree(filtedMenuList)
  } catch (e) {
    // smartSentry.captureError(e);
  } finally {
    tableLoading.value = false
  }
}

// 多选
const selectedRowKeys = ref([])
let selectedRows = []
const hasSelected = computed(() => selectedRowKeys.value.length > 0)
function onSelectChange(keyArray, selectRows) {
  selectedRowKeys.value = keyArray
  selectedRows = selectRows
}

/* ====================== 操作按钮 ====================== */
const disabledFlagChange = (checked, record) => {
  const params = { menuId: record.menuId, disabledFlag: checked, menuName: record.menuName, parentId: record.parentId }
  menuApi.updateMenu(params).then(res => {
    if (res.code === 0) {
      record.disabledFlag = checked
      message.success(`${record.disabledFlag ? '禁用' : '启用'}成功`)
    }
  })
}
const menuOperateModal = ref()
function showDrawer(rowData) {
  menuOperateModal.value.showDrawer(rowData)
}
function batchDelete() {
  confirmBatchDelete(selectedRows)
}
function singleDelete(record) {
  confirmBatchDelete([record])
}

function confirmBatchDelete(menuArray) {
  const menuNameArray = menuArray.map(e => e.menuName)
  Modal.confirm({
    title: '确定要删除如下菜单吗?',
    icon: createVNode(ExclamationCircleOutlined),
    content: _.join(menuNameArray, '、'),
    okText: '删除',
    okType: 'danger',
    onOk() {
      console.log('OK')
      const menuIdList = menuArray.map(e => e.menuId)
      requestBatchDelete(menuIdList)
      selectedRows = []
    },
    cancelText: '取消',
    onCancel() {}
  })

  async function requestBatchDelete(menuIdList) {
    try {
      await menuApi.batchDeleteMenu(menuIdList)
      message.success('删除成功!')
      queryMenu()
    } catch (e) {
      console.log(e)
    } finally {
    }
  }
}
onBeforeUpdate(() => {
  console.log('onBeforeUpdate')
})
onMounted(queryMenu)
</script>
<style lang="scss">
.smart-query-form {
  background-color: #ffffff;
  padding: 5px 10px;
}
.smart-query-form-row:not(:first-child) {
  margin-top: 8px;
}

.smart-query-form-row .smart-query-form-item {
  margin-right: 8px;
  margin-top: 5px;
  margin-bottom: 5px;
}

.smart-margin-left10 {
  margin-left: 10px;
}
.smart-margin-left20 {
  margin-left: 20px;
}
.smart-table-btn-block {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  .smart-table-operate-block {
    .ant-btn {
      margin-right: 12px;
    }
  }
  .smart-table-setting-block {
    float: right;
  }
}
.smart-table-operate {
  .ant-btn {
    padding: 0 3px !important;
    height: auto;
  }
}
</style>