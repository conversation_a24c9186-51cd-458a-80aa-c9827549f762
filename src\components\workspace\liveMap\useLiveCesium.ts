import { liveViewer, handler } from "./initLiveCesium";
import * as Cesium from "cesium";
import { ref, nextTick } from "vue";
import rootStore from "@/store/index";

import startBg from '@/assets/images/startBg.png';
import endBg from '@/assets/images/endBg.png';
import vehicle from "@/assets/images/vehicle.png";

export function useLiveCesium() {
  const popFlag = ref(false); // 弹框显示
  const popData = ref(); // 点击数据
  const mapPopup = ref(); // 弹框dom
  const pointEntity = ref();
  const trackEntity = ref();
  const addPointsEntity = ref<Cesium.Entity[]>([]);
  const addPointsNoLabelEntity = ref<Cesium.Entity[]>([]);
  const trackPositions = ref<Cesium.Cartesian3[]>([]); // 使用 ref 来保存坐标点

  const flyToEntitiesCenter = (points: any) => {
    // 获取所有点的边界框
    const pointPositions = points.map((point: any) =>
      Cesium.Cartesian3.fromDegrees(point.lon, point.lat)
    );

    const boundingBox = Cesium.BoundingSphere.fromPoints(pointPositions);
    // 设置视角包含所有内容
    liveViewer.zoomTo(
      liveViewer.entities,
      new Cesium.HeadingPitchRange(
        0,
        -Cesium.Math.PI_OVER_TWO,
        boundingBox.radius * 2
      )
    );
    // // 设置相机位置和方向
    // liveViewer.camera.flyTo({
    //   destination: Cesium.Cartesian3.fromDegrees(
    //     Cesium.Math.toDegrees(
    //       Cesium.Cartographic.fromCartesian(boundingBox.center).longitude
    //     ),
    //     Cesium.Math.toDegrees(
    //       Cesium.Cartographic.fromCartesian(boundingBox.center).latitude
    //     ),
    //     boundingBox.radius * 2.5
    //   ),
    //   orientation: {
    //     heading: Cesium.Math.toRadians(0), // 方向为正东
    //     pitch: Cesium.Math.toRadians(-90), // 垂直向下看
    //     roll: 0.0, // 无翻滚
    //   },
    //   duration: 2, // 飞行时间，单位为秒
    // });
  };

  // 添加点
  const addpoint = (point: any, image: any, labelColor: any) => {
    let addPointEntity = null;
    if (image) {
      addPointEntity = liveViewer.entities.add({
        id: point.id,
        data: point,
        position: Cesium.Cartesian3.fromDegrees(point.lon, point.lat),
        billboard: {
          image: image, // 使用相对路径引入图片资源
          width: 40,
          height: 40,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 确保图片底部中心指向坐标点
        },
        label: {
          text: point.name,
          font: "14px sans-serif",
          fillColor: Cesium.Color.fromCssColorString(
            labelColor ? labelColor : "#D20000"
          ),
          outlineColor: Cesium.Color.fromCssColorString("#AAA"),
          outlineWidth: 0,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 确保标签位于点的上方
          pixelOffset: new Cesium.Cartesian2(6, -44), // 调整标签位置，确保标签位于点的上方
        },
      });
    } else {
      addPointEntity = liveViewer.entities.add({
        id: point.id,
        data: point,
        position: Cesium.Cartesian3.fromDegrees(point.lon, point.lat),
        billboard: {
          image: image, // 使用相对路径引入图片资源
          width: 50,
          height: 50,
        },
        point: {
          pixelSize: 20, // 点的大小，以像素为单位
          color: Cesium.Color.RED, // 点的颜色
          outlineColor: Cesium.Color.WHITE, // 点轮廓颜色
          outlineWidth: 2, // 点轮廓宽度
        },
      });
    }

    addPointsEntity.value.push(addPointEntity);
  };

  const clearAddPoints = () => {
    if (!liveViewer) return;
    // 删除之前的点位
    addPointsEntity.value.forEach((entity) =>
      liveViewer?.entities.remove(entity)
    );
    addPointsEntity.value = [];
  };

  // 添加点没有上方名称
  const addpointNoLabel = (point: any, image: any) => {
    let addPointNoLabelEntity = null;
    if (image) {
      addPointNoLabelEntity = liveViewer.entities.add({
        id: point.id,
        data: point,
        position: Cesium.Cartesian3.fromDegrees(point.lon, point.lat),
        billboard: {
          image: image, // 使用相对路径引入图片资源
          width: 30,
          height: 30,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 确保图片底部中心指向坐标点
        },
      });
    } else {
      addPointNoLabelEntity = liveViewer.entities.add({
        id: point.id,
        data: point,
        position: Cesium.Cartesian3.fromDegrees(point.lon, point.lat),
        billboard: {
          image: image, // 使用相对路径引入图片资源
          width: 50,
          height: 50,
        },
        point: {
          pixelSize: 20, // 点的大小，以像素为单位
          color: Cesium.Color.RED, // 点的颜色
          outlineColor: Cesium.Color.WHITE, // 点轮廓颜色
          outlineWidth: 2, // 点轮廓宽度
        },
      });
    }
    addPointsNoLabelEntity.value.push(addPointNoLabelEntity);
  };

  const clearAddPointsNoLabel = () => {
    if (!liveViewer) return;
    console.log(addPointsNoLabelEntity);
    
    // 删除之前的点位
    addPointsNoLabelEntity.value.forEach((entity) =>
      liveViewer?.entities.remove(entity)
    );
    addPointsNoLabelEntity.value = [];
  };

  // 添加绑定事件
  const setEvent = () => {
    // 左键点击事件
    let leftclick = Cesium.ScreenSpaceEventType.LEFT_CLICK;
    liveViewer.screenSpaceEventHandler.removeInputAction(leftclick);
    handler.setInputAction((pickClick: any) => {
      console.log(pickClick);

      // 返回笛卡尔2坐标系 - 为点击点位置
      // 获取点击的实体
      const pick = liveViewer.scene.pick(pickClick.position);
      console.log(pick);
      console.log(pick.id.id);

      if (!pick || !pick.id) {
        return false;
      }
      const pick_obj = Cesium.defaultValue(pick.id, pick.primitive.id);
      console.log(pick_obj);
      // 判断是否是Cesium实体
      if (pick_obj instanceof Cesium.Entity) {
        const data = (pick_obj as any).data; // 使用类型断言绕过检查
        console.log(data);
        if (!data || !data.lat || !data.lon) {
          return;
        }
        console.log(data);
        // 经纬度转笛卡尔3
        const cartesian3 = Cesium.Cartesian3.fromDegrees(
          Number(data.lon),
          Number(data.lat),
          0.1
        );
        // 获取实体笛卡尔2坐标系
        const screenposition = Cesium.SceneTransforms.worldToWindowCoordinates(
          liveViewer.scene,
          cartesian3
        );
        rootStore.dispatch("workspace/mapPointClick", data);
      }
    }, leftclick);
  };

  // 添加轨迹
  const addTrack = (trackPointsArr: any) => {
    console.log("addTrack");
    console.log(trackPointsArr);

    // 假设这是你的坐标点列表（经度、纬度）
    // const trackPoints = [...trackPointsArr];
    // // 将坐标转换为 Cartesian3 格式
    // const positions = trackPoints.map((point) =>
    //   Cesium.Cartesian3.fromDegrees(point.lon, point.lat)
    // );

    // // 创建一条折线来连接这些点
    // liveViewer.entities.add({
    //   polyline: {
    //     positions: new Cesium.CallbackProperty(() => positions, false),
    //     width: 4,
    //     material: new Cesium.ColorMaterialProperty(Cesium.Color.BLUE),
    //   },
    // });

    // // 给第一个点添加绿色水滴背景和"S"标记
    // if (trackPoints.length > 0) {
    //   const firstPointPosition = Cesium.Cartesian3.fromDegrees(
    //     trackPoints[0].lon,
    //     trackPoints[0].lat
    //   );
    //   liveViewer.entities.add({
    //     position: firstPointPosition,
    //     billboard: {
    //       image: startBg, // 替换为实际路径
    //       width: 54,
    //       height: 70,
    //       scale: 0.5,
    //       verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 确保图片底部中心指向坐标点
    //     }
    //   });
    // }

    // // // 给最后一个点添加红色水滴背景和"E"标记
    // if (trackPoints.length > 1) {
    //   const lastPointPosition = Cesium.Cartesian3.fromDegrees(
    //     trackPoints[trackPoints.length - 1].lon,
    //     trackPoints[trackPoints.length - 1].lat
    //   );
    //   liveViewer.entities.add({
    //     position: lastPointPosition,
    //     billboard: {
    //       image: endBg, // 替换为实际路径
    //       width: 54,
    //       height: 70,
    //       scale: 0.5,
    //       verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 确保图片底部中心指向坐标点
    //     },
    //   });
    // }

    // flyToEntitiesCenter(trackPointsArr);
  };

  // 添加样本点
  const addSamplePoint = (pointData: any) => {
    if (!liveViewer) return;
    removeEntityById("movePoint1");
    console.log(pointData);
    // 校验经纬度是否为有效数字
    const lon = parseFloat(pointData.lon);
    const lat = parseFloat(pointData.lat);
    console.log(lon, lat);

    if (!pointData || !lon || !lat) {
      console.error("点位数据错误:", pointData);
      return;
    }
    if (isNaN(lon) || isNaN(lat)) {
      console.error("Invalid longitude or latitude:", pointData);
      return;
    }
    const position = Cesium.Cartesian3.fromDegrees(lon, lat);
    liveViewer.entities.add({
      id: "movePoint1",
      position: position,
      billboard: {
        image: vehicle, // 替换为你的背景图片路径
        width: 60,
        height: 60,
        scale: 0.5,
      },
    });

    // 移动到最新的点位
    // liveViewer.zoomTo(liveViewer.entities);
    liveViewer.camera.setView(liveViewer.entities, {
      offset: {
        heading: Cesium.Math.toRadians(0), // 正北方向
        pitch: Cesium.Math.toRadians(-90), // 垂直向下
        roll: 0,
      },
    });
  };

  // 更新样本位置
  const updatePosition = (latestData: any) => {
    if (!liveViewer) return;
    // 校验经纬度是否为有效数字
    const lon = parseFloat(latestData.lon);
    const lat = parseFloat(latestData.lat);
    if (!latestData || !lon || !lat) {
      console.error("点位数据错误:", latestData);
      return;
    }
    if (isNaN(lon) || isNaN(lat)) {
      console.error("Invalid longitude or latitude:", latestData);
      return;
    }
    const position = Cesium.Cartesian3.fromDegrees(lon, lat);
    console.log(pointEntity.value);
    
    if (pointEntity.value) {
      pointEntity.value.position = position;
    } else {
      pointEntity.value = liveViewer.entities.add({
        id: "movePoint1",
        position: position,
        billboard: {
          image: vehicle, // 替换为你的背景图片路径
          width: 60,
          height: 60,
          scale: 0.5,
        },
      });
    }
    // 移动相机以跟随实体
    // liveViewer.camera.flyTo({
    //   destination: Cesium.Cartesian3.fromDegrees(lon, lat, 1000),
    //   duration: 1,
    // });
  };

  // 更新轨迹
  const updateTrack = (trackPoints: any, isAdd: boolean) => {
    if (!liveViewer) return;

    // 给第一个点添加绿色水滴背景和"S"标记
    if (trackPoints.length > 0 && isAdd) {
      const firstPointPosition = Cesium.Cartesian3.fromDegrees(
        trackPoints[0].lon,
        trackPoints[0].lat
      );
      liveViewer.entities.add({
        position: firstPointPosition,
        billboard: {
          image: startBg, // 替换为实际路径
          width: 54,
          height: 70,
          scale: 0.5,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 确保图片底部中心指向坐标点
        },
      });
    }

    // 不做平滑处理的线,将坐标转换为 Cartesian3 格式
    let trackPointsArr = trackPoints.map((point: any) =>
      Cesium.Cartesian3.fromDegrees(point.lon, point.lat)
    );
    
    // 去除重复的点
    trackPositions.value = trackPointsArr.filter(
      (point: any, index: number, self: any) =>
        index === 0 || !Cesium.Cartesian3.equals(point, self[index - 1])
    );
    console.log(trackEntity.value);

    if (!trackEntity.value) {
      trackEntity.value = liveViewer.entities.add({
        polyline: {
          positions: new Cesium.CallbackProperty(
            () => trackPositions.value,
            false
          ),
          width: 4,
          material: new Cesium.ColorMaterialProperty(Cesium.Color.BLUE),
        },
      });
    }
    // else {
    //   trackEntity.value.positions = new Cesium.CallbackProperty(
    //     () => positions,
    //     false
    //   );
    // }
  };

  const removeEntityById = (id: any) => {
    const entity = liveViewer.entities.getById(id);
    if (entity) {
      liveViewer.entities.remove(entity);
      console.log(`已移除ID为 ${id} 的实体`);
    } else {
      console.warn(`未找到ID为 ${id} 的实体`);
    }
  };

  // 清除地图上的所有内容
  const clearMap = () => {
    console.log("clearMap");
    console.log(trackEntity.value);
    if (trackEntity.value) {
      trackEntity.value = undefined;
    }
    console.log(trackEntity.value);
    trackPositions.value = [];
    if (pointEntity.value) {
      pointEntity.value = undefined;
    }
    if (liveViewer.entities) {
      liveViewer.entities.removeAll(); // 移除所有实体
    }
  };

  return {
    addpoint,
    addpointNoLabel,
    clearAddPoints,
    clearAddPointsNoLabel,
    updatePosition,
    updateTrack,
    setEvent,
    flyToEntitiesCenter,
    popFlag,
    popData,
    mapPopup,
    addTrack,
    clearMap,
    addSamplePoint,
  };
}
