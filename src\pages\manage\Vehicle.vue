<template>
  <div class="vehicle vehicleContainer">
    <div class="handleWrap clearfix">
      <div class="left fl">
        <a-form layout="inline" :model="searchFormState">
          <a-form-item>
            <a-select v-model:value="searchType" style="width: 130px;" @change="searchTypeChangeHandle">
              <a-select-option value="vehicleName">载具名称</a-select-option>
              <a-select-option value="vehicleCode">载具编码</a-select-option>
              <a-select-option value="vehicleType">载具类型</a-select-option>
              <a-select-option value="loadCount">支持负载个数</a-select-option>
              <a-select-option value="status">在离线状态</a-select-option>
              <a-select-option value="remindStatus">保险过期提醒</a-select-option>
              <a-select-option value="createTime">创建时间</a-select-option>
              <a-select-option value="lastReportTime">最后上报时间</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item v-if="searchType === 'vehicleName'" style="width: 280px;">
            <a-input v-model:value="searchFormState.vehicleName" placeholder="请输入载具名称" size="small" allowClear @pressEnter="onSearch"></a-input>
          </a-form-item>
          <a-form-item v-else-if="searchType === 'vehicleCode'" style="width: 280px;">
            <a-input v-model:value="searchFormState.vehicleCode" placeholder="请输入载具编码" size="small" allowClear @pressEnter="onSearch"></a-input>
          </a-form-item>
          <a-form-item v-else-if="searchType === 'vehicleType'" style="width: 280px;">
            <DictSelect dict-code="vehicle_type" v-model:value="searchFormState.vehicleType" width="100%" placeholder="请选择载具类型" />
          </a-form-item>
          <a-form-item v-else-if="searchType === 'loadCount'" style="width: 280px;">
            <a-input-number
              v-model:value="searchFormState.loadCount"
              :min="1"
              :max="1000000"
              style="width: 100%;"
              :precision="0"
              v-input-number-change
              placeholder="请输入支持负载个数"
            />
          </a-form-item>
          <a-form-item v-else-if="searchType === 'status'" style="width: 280px;">
            <a-select v-model:value="searchFormState.status" style="width: 100%;" placeholder="请选择在离线状态">
              <a-select-option value="0">在线</a-select-option>
              <a-select-option value="1">离线</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item v-else-if="searchType === 'remindStatus'" style="width: 280px;">
            <a-select v-model:value="searchFormState.remindStatus" style="width: 100%;" placeholder="请选择保险过期提醒">
              <a-select-option value="0">正常</a-select-option>
              <a-select-option value="1">临期</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item v-else-if="searchType === 'createTime'" style="width: 280px;">
            <a-date-picker
              format="YYYY-MM-DD"
              valueFormat="YYYY-MM-DD"
              placeholder="请选择日期时间"
              v-model:value="searchFormState.createTime"
              style="width: 100%;"
            />
          </a-form-item>
          <a-form-item v-else style="width: 280px;">
            <a-date-picker
              format="YYYY-MM-DD"
              valueFormat="YYYY-MM-DD"
              placeholder="请选择日期时间"
              v-model:value="searchFormState.lastReportTime"
              style="width: 100%;"
            />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="onSearch" v-privilege="'vehicle:query'">搜索</a-button>
          </a-form-item>
        </a-form>
      </div>
      <div class="right fr">
        <a-button type="primary" @click="addHandle" v-privilege="'vehicle:add'">
          <template #icon>
            <PlusOutlined />
          </template>
          新增
        </a-button>
      </div>
    </div>
    <div class="contentWrap custom-scrollbar" ref="tableWrapperRef">
      <a-table
        ref="tableRef"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="false"
        rowKey="id"
        bordered
        :scroll="{ x: 1200 ,y: tableHeight }"
        :loading="tableLoading"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'operation'">
            <span>
              <a-button type="link" size="small" @click="editHandle(record)" v-privilege="'vehicle:update'">编辑</a-button>
              <a-button type="link" size="small" danger @click="delHandle(record)" v-privilege="'vehicle:delete'">删除</a-button>
            </span>
          </template>
          <template v-if="column.dataIndex === 'status'">
            <span :style="{color: record.status === 1 ? '' : '#52c41a'}">{{ record.status===1 ? '离线' : '在线'}}</span>
          </template>
          <template v-if="column.dataIndex === 'remindStatus'">
            <span :style="{ color: record.remindStatus === 1 ? '#fa8c16' : '' }">{{ record.remindStatus === 1 ? '临期' : '' }}</span>
          </template>
        </template>
      </a-table>
    </div>
    <div class="pageWrap">
      <a-pagination
        size="small"
        v-model:current="pagination.pageNum"
        :page-size="pagination.pageSize"
        :total="total"
        show-size-changer
        show-quick-jumper
        :show-total="(total: number) => `共 ${total} 条`"
        :page-size-options="PAGE_SIZE_OPTIONS"
        @change="(current: number, size: number) => onPageChange('current', current, size)"
        @showSizeChange="(current: number, size: number) => onPageChange('size', current, size)"
      />
    </div>
    <a-modal
      v-model:open="addEditModalState.visible"
      :title="addEditModalState.type === 'add' ? '新增载具' : '编辑载具'"
      width="600px"
      cancelText="取消"
      okText="提交"
      :closable="false"
      :keyboard="false"
      :maskClosable="false"
      @cancel="handleCancle"
      @ok="handleOk"
    >
      <a-form class="vehicleHandleForm" ref="vehicleFormRef" :model="vehicleFormState" :rules="vehicleRules" :labelCol="{ span: 5 }">
        <a-form-item ref="vehicleCode" label="载具编码" name="vehicleCode">
          <a-input v-model:value="vehicleFormState.vehicleCode" :maxlength="30" :disabled="addEditModalState.type === 'edit'" />
        </a-form-item>
        <a-form-item ref="vehicleName" label="载具名称" name="vehicleName">
          <a-input v-model:value="vehicleFormState.vehicleName" :maxlength="30" />
        </a-form-item>
        <a-form-item ref="vehicleType" label="载具类型" name="vehicleType">
          <DictSelect dict-code="vehicle_type" v-model:value="vehicleFormState.vehicleType" width="100%" />
        </a-form-item>
        <a-form-item ref="model" label="型号" name="model">
          <a-input v-model:value="vehicleFormState.model" :maxlength="30" />
        </a-form-item>
        <a-form-item ref="loadCount" label="支持负载个数" name="loadCount">
          <!-- <a-input v-model:value="vehicleFormState.loadCount" :maxlength="30" /> -->
          <a-input-number
            ref="loadCountRef"
            v-model:value="vehicleFormState.loadCount"
            :min="1"
            :max="1000000"
            style="width: 100%;"
            :precision="0"
            v-input-number-change
          />
        </a-form-item>
        <a-form-item ref="insurance" label="在保时间" name="insurance">
          <a-range-picker
            v-model:value="vehicleFormState.insurance"
            style="width: 100%;"
            format="YYYY-MM-DD"
            valueFormat="YYYY-MM-DD"
            @change="insuranceChangeHandle"
          />
        </a-form-item>
        <a-form-item ref="remind" label="保险期提醒天数" name="remind">
          <a-input v-model:value="vehicleFormState.remind" :maxlength="30" />
        </a-form-item>
        <a-form-item ref="vendor" label="供应商" name="vendor">
          <a-input v-model:value="vehicleFormState.vendor" :maxlength="30" />
        </a-form-item>
        <a-form-item ref="vendorContact" label="供应商联系人" name="vendorContact">
          <a-input v-model:value="vehicleFormState.vendorContact" :maxlength="30" />
        </a-form-item>
        <a-form-item ref="vendorPhone" label="供应商电话" name="vendorPhone">
          <a-input v-model:value="vehicleFormState.vendorPhone" :maxlength="30" />
        </a-form-item>
        <a-form-item ref="des" label="描述" name="des">
          <a-textarea v-model:value="vehicleFormState.des" :auto-size="{ minRows: 2, maxRows: 3 }" :maxlength="1000" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import {
  PlusOutlined,
  SlackOutlined,
  MoreOutlined,
  FieldTimeOutlined,
  UserOutlined,
  ExclamationCircleOutlined,
  EnvironmentOutlined,
  DeleteOutlined,
  PlusCircleOutlined
} from '@ant-design/icons-vue'
import DictSelect from '@/components/common/dict-select/index.vue'
import 'moment/dist/locale/zh-cn'
import { Modal, message } from 'ant-design-vue'
import { ValidateErrorEntity } from 'ant-design-vue/es/form/interface'
import { reactive, ref, UnwrapRef, createVNode, onMounted } from 'vue'
import { vehicleApi } from '@/api/manageTool/vehicleApi'
import { PageParamModel } from '@/types/paging'
import { PAGE_SIZE_OPTIONS } from '@/utils/constants'
import { getTableHeight } from '@/hooks/getTableHeight'
import _ from 'lodash'
import moment, { Moment } from 'moment'
import { regular } from '@/utils/regular'
// 注册组件
defineOptions({
  components: {
    PlusOutlined,
    SlackOutlined,
    MoreOutlined,
    FieldTimeOutlined,
    UserOutlined,
    EnvironmentOutlined,
    DeleteOutlined,
    PlusCircleOutlined,
    DictSelect
  }
})

/* ====================== 表格 ====================== */
const tableWrapperRef = ref()
const tableRef = ref()
const tableHeight = getTableHeight(tableWrapperRef, tableRef)

const tableLoading = ref(false)
const columns = reactive([
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    customRender: ({ index }: any) => (pagination.value.pageNum - 1) * pagination.value.pageSize + index + 1,
    fixed: 'left',
    width: 80,
    align: 'center'
  },
  {
    title: '载具名称',
    dataIndex: 'vehicleName',
    key: 'vehicleName',
    fixed: 'left',
    width: 160,
    align: 'center'
  },
  {
    title: '载具编码',
    dataIndex: 'vehicleCode',
    key: 'vehicleCode',
    width: 160,
    align: 'center'
  },
  {
    title: '载具类型',
    dataIndex: 'vehicleTypeName',
    key: 'vehicleTypeName',
    width: 160,
    align: 'center'
  },
  {
    title: '支持负载个数',
    dataIndex: 'loadCount',
    key: 'loadCount',
    width: 160,
    align: 'center'
  },
  {
    title: '在离线状态',
    dataIndex: 'status',
    key: 'status',
    // slots: { customRender: 'status' },
    width: 160,
    align: 'center'
  },
  {
    title: '保险过期提醒',
    dataIndex: 'remindStatus',
    key: 'remindStatus',
    // slots: { customRender: 'remindStatus' },
    width: 160,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 200,
    align: 'center'
  },
  {
    title: '最后上报时间',
    dataIndex: 'lastReportTime',
    key: 'lastReportTime',
    width: 200,
    align: 'center'
  },
  {
    title: '操作',
    key: 'operation',
    dataIndex: 'operation',
    // slots: { customRender: 'operation' },
    fixed: 'right',
    width: 130,
    align: 'center'
  }
])
const dataSource = ref([])

/* ====================== 分页 ====================== */
const pagination = ref<PageParamModel>({
  pageNum: 1, // 当前页码
  pageSize: 10, // 每页数量
  sortItemList: [
    {
      isAsc: false,
      column: 'create_time'
    }
  ]
})
const total = ref<number>(0)
const onPageChange = (type: string, pageNum: number, pageSize: number) => {
  console.log(pageNum, pageSize)
  if (type === 'current') {
    pagination.value.pageNum = pageNum
  } else {
    pagination.value.pageNum = 1
    pagination.value.pageSize = pageSize
  }
  queryVehiclePage()
}
/* ====================== 搜索 ====================== */
const searchType = ref('vehicleName')
interface SearchFormState {
  vehicleName: string
  vehicleCode: string
  vehicleType: string | null
  loadCount: string
  status: string | null
  remindStatus: string | null
  createTime: string
  lastReportTime: string
}
const searchFormStateModel = {
  vehicleName: '',
  vehicleCode: '',
  vehicleType: null,
  loadCount: '',
  status: null,
  remindStatus: null,
  createTime: '',
  lastReportTime: ''
}
const searchFormState: UnwrapRef<SearchFormState> = reactive({ ...searchFormStateModel })

const queryVehiclePage = async () => {
  try {
    tableLoading.value = true
    const result = await vehicleApi.queryVehiclePage({
      ...pagination.value,
      ...searchFormState
    })
    console.log(result)
    dataSource.value = result.data.list
    total.value = result.data.total
  } catch (e) {
    console.log(e)
    dataSource.value = []
    total.value = 0
  } finally {
    tableLoading.value = false
  }
}

const onSearch = () => {
  console.log(searchFormState.vehicleName)
  pagination.value.pageNum = 1
  total.value = 0
  dataSource.value = []
  queryVehiclePage()
}

const searchTypeChangeHandle = () => {
  for (const key in searchFormState) {
    if (key !== searchType.value) {
      searchFormState[key] = searchFormStateModel[key]
    }
  }
}
/* ====================== 表单（新增|编辑） ====================== */
interface VehicleFormState {
  id: number | null
  vehicleCode: string
  vehicleName: string
  vehicleType: string
  model: string
  loadCount: number
  insurance: Array<string>
  insStartTime?: string
  insEndTime?: string
  remind?: number
  vendor?: string
  vendorContact?: string
  vendorPhone?: string
  des?: string
}
const vehicleFormStateModel = {
  id: null,
  vehicleCode: '',
  vehicleName: '',
  vehicleType: '',
  model: '',
  loadCount: 0,
  insurance: [],
  insStartTime: '',
  insEndTime: '',
  remind: 0,
  vendor: '',
  vendorContact: '',
  vendorPhone: '',
  des: ''
}
const vehicleFormState = ref<VehicleFormState>(_.cloneDeep(vehicleFormStateModel))

const vehicleFormRef = ref()
const loadCountRef = ref()
const vehicleRules = reactive({
  vehicleCode: [{ required: true, message: '请输入载具编码' }],
  vehicleName: [{ required: true, message: '请输入载具名称' }],
  vehicleType: [{ required: true, message: '请选择载具类型' }],
  model: [{ required: true, message: '请输入型号' }],
  loadCount: [{ required: true, message: '请输入负载个数' }],
  // insurance: [{ required: true, message: '请选择在保时间' }],
  // insStartTime: [{ required: true, message: '请选择开始日期' }],
  // insEndTime: [{ required: true, message: '请选择结束日期' }],
  remind: [{ pattern: regular.isNumber1, message: '请输入数字' }]
  // vendor: [{ required: true, message: '请输入供应商' }],
  // vendorContact: [{ required: true, message: '请输入供应商联系人' }],
  // vendorPhone: [{ required: true, message: '请输入供应商电话' }],
  // des: [{ required: true, message: '请输入描述' }]
})
const insuranceChangeHandle = (dates: [Moment, Moment] | [string, string], dateStrings: [string, string]) => {
  console.log(vehicleFormState.value.insurance)
  vehicleFormState.value.insStartTime = dateStrings[0]
  vehicleFormState.value.insEndTime = dateStrings[1]
}
interface AddEditModalModel {
  visible: boolean
  type: '' | 'add' | 'edit'
}
const addEditModalState = ref<AddEditModalModel>({
  visible: false,
  type: ''
})
const handleCancle = () => {
  addEditModalState.value.visible = false
  vehicleFormState.value = _.cloneDeep(vehicleFormStateModel)
  vehicleFormRef.value.resetFields()
}
const handleOk = () => {
  console.log(vehicleFormState.value)
  // return
  vehicleFormRef.value
    .validate()
    .then(async () => {
      if (addEditModalState.value.type === 'add') {
        await vehicleApi.addVehicle(vehicleFormState.value)
        message.success('新增成功')
        searchFormState.vehicleName = ''
        onSearch()
      } else {
        await vehicleApi.updateVehicle(vehicleFormState.value)
        message.success('编辑成功')
        queryVehiclePage()
      }
      // console.log(dictConfigFormState.value)
      addEditModalState.value.visible = false
      vehicleFormState.value = _.cloneDeep(vehicleFormStateModel)
      vehicleFormRef.value.resetFields()
    })
    .catch((error: ValidateErrorEntity<VehicleFormState>) => {
      console.log('error', error)
      // addEditModalState.value.visible = false
    })
}

/* ====================== 操作按钮 ====================== */
const addHandle = () => {
  addEditModalState.value.type = 'add'
  addEditModalState.value.visible = true
}
const editHandle = (item: any) => {
  addEditModalState.value.type = 'edit'
  addEditModalState.value.visible = true
  vehicleFormState.value = {
    id: item.id,
    vehicleCode: item.vehicleCode,
    vehicleName: item.vehicleName,
    vehicleType: item.vehicleType,
    model: item.model,
    loadCount: item.loadCount,
    insurance: [item.insStartTime, item.insEndTime],
    insStartTime: item.insStartTime,
    insEndTime: item.insEndTime,
    remind: item.remind,
    vendor: item.vendor,
    vendorContact: item.vendorContact,
    vendorPhone: item.vendorPhone,
    des: item.des
  }
}
const delHandle = (item: any) => {
  console.log(item)
  Modal.confirm({
    title: () => '删除提醒',
    icon: () => createVNode(ExclamationCircleOutlined),
    content: () => '确定是否删除该载具？',
    okText: () => '确认',
    okType: 'danger',
    cancelText: () => '取消',
    onOk: async () => {
      try {
        await vehicleApi.deleteVehicle(item.id)
        message.success('删除成功')
        if (pagination.value.pageNum > 1 && dataSource.value.length === 1) {
          pagination.value.pageNum = pagination.value.pageNum - 1
        }
        queryVehiclePage()
      } catch (e) {
        console.log(e)
      }
    },
    onCancel() {
      console.log('Cancel')
    }
  })
}
onMounted(() => {
  queryVehiclePage()
})
</script>
<style lang="scss">
.vehicleContainer {
  height: 100%;

  .handleWrap {
    height: 54px;
    padding: 10px;
    box-sizing: border-box;

    .left {
      width: 80%;

      .ant-input {
        box-sizing: border-box;
        width: 240px;
        height: 34px;
      }

      .ant-btn {
        height: 34px;
      }
    }

    .right {
      width: 20%;
      text-align: right;

      .ant-btn {
        height: 34px;
      }
    }
  }

  .contentWrap {
    height: calc(100% - 110px);
    padding: 10px;
    overflow: hidden;

    .ant-card {
      height: 100%;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      border-radius: 6px;
      cursor: default;

      &:hover {
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
      }

      .ant-card-body {
        padding: 15px;
      }

      .cardHead {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #000;

        .headIcon {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          background: #d1cdfc;
          text-align: center;
          min-width: 50px;

          .anticon-slack {
            font-size: 30px;
            color: #200de7;
            width: 30px;
            height: 30px;
            margin-top: 10px;
          }
        }

        .cardTitle {
          font-size: 14px;
          font-weight: 600;
          margin: 0 10px;
        }

        .ant-dropdown-link {
          color: #000;

          &:hover {
            color: #1890ff;
          }
        }
      }

      .cardInfo {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #999;
        margin: 10px 0;

        .anticon {
          margin-right: 4px;
        }
      }

      .cardRemarks {
        border-top: 1px solid #eee;
        font-size: 12px;
        color: #666;
        padding-top: 10px;
      }
    }
  }

  .pageWrap {
    text-align: right;
    margin-top: 20px;
    height: 38px;
    padding-right: 20px;
  }
}

.ant-modal-body {
  padding: 20px 30px 0 20px;
}

.vehicleHandleForm {
  .ant-input {
    vertical-align: middle;
  }

  .ant-select {
    vertical-align: middle;
  }

  .anticon-environment {
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    color: #666;
    vertical-align: middle;
  }

  .anticon-delete {
    color: red;
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    vertical-align: middle;
  }

  .anticon-plus-circle {
    color: blue;
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    vertical-align: middle;
  }

  .targetItem {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.mapContainer {
  height: 500px;
}
</style>
