<template>
  <div>
    <a-form label-width="80px">
      <a-row gutter="16">
        <a-col :span="6">
          <a-form-item label="当前地点">
            <a-input size="small" id="suggestId" v-model:value="addressInfo.address" placeholder="请输入地点"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="位置经度">
            <a-input size="small" v-model:value="addressInfo.longitude" readonly />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="位置纬度">
            <a-input size="small" v-model:value="addressInfo.latitude" readonly />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div id="baidu-map" style="width: 100%; height: 450px;"></div>
  </div>
</template>

<script lang="ts" setup>
import { BMAPGL } from '@/utils/loadBmap'
import { ref, onMounted, reactive, watch, nextTick } from 'vue'

// 定义props
const props = defineProps({
  // 地图缩放级别
  pointDefault: {
    type: String,
    default: ''
  }
})

// 定义要触发的事件（推荐使用 defineEmits 声明）
const emit = defineEmits(['custom-event'])

let map: any = ref(null)
const mapZoom = ref(15)
let addressInfo = reactive({
  // 地址信息
  longitude: '', // 经度
  latitude: '', // 纬度
  province: '', // 省
  city: '', // 市
  district: '', // 区
  address: '' // 详细地址
})

const initMap = async () => {
  map.value = null
  BMAPGL('PrH5h2eChtIkm14cosq58yPG7aQWY3gW').then((BMapGL: any) => {
    if (!BMapGL || !BMapGL.Map) {
      console.error('百度地图 GL 版本加载失败，请检查 API 密钥和版本')
      return
    }
    // 打印加载的 BMapGL 对象以调试
    map.value = new BMapGL.Map('baidu-map')
    const ac = new BMapGL.Autocomplete({
      //建立一个自动完成的对象
      input: 'suggestId',
      location: map.value
    })
    const zoomCtrl = new BMapGL.ZoomControl() // 添加缩放控件
    map.value.addControl(zoomCtrl)
    const cityCtrl = new BMapGL.CityListControl() // 添加城市列表控件
    map.value.addControl(cityCtrl)
    const LocationControl = new BMapGL.LocationControl() // 添加定位控件，用于获取定位
    map.value.addControl(LocationControl)
    const scaleCtrl = new BMapGL.ScaleControl() // 添加比例尺控件
    map.value.addControl(scaleCtrl)
    map.value.setMapType() // 设置地图类型为标准地图模式；

    let point: any

    //初始化的时候如果有经纬度，需要先在地图上添加点标记
    if (addressInfo.longitude && addressInfo.latitude) {
      point = new BMapGL.Point(parseFloat(addressInfo.longitude), parseFloat(addressInfo.latitude))
      map.value.centerAndZoom(point, mapZoom.value)
      const marker2 = new BMapGL.Marker(point)
      //在地图上添加点标记
      map.value.addOverlay(marker2)
      const geoc = new BMapGL.Geocoder() // 创建地址解析器的实例
      geoc.getLocation(point, (rs: { addressComponents: any }) => {
        let adr = rs.addressComponents
        addressInfo.address = adr.province + adr.city + adr.district + adr.street + adr.streetNumber
      })
      emit('custom-event', addressInfo)
    } else {
      // 初始化地图中心点
      const localcity = new BMapGL.LocalCity()
      localcity.get((e: { name: any }) => {
        map.value.centerAndZoom(e.name, mapZoom.value)
      })
    }

    // 开启鼠标滚轮缩放
    map.value.enableScrollWheelZoom(true)

    map.value.addEventListener('click', function (e: { latlng: { lng: string; lat: string } }) {
      map.value.clearOverlays()
      const point1 = new BMapGL.Point(e.latlng.lng, e.latlng.lat)
      // 创建点标记
      const marker1 = new BMapGL.Marker(point1)
      // 在地图上添加点标记
      map.value.addOverlay(marker1)
      addressInfo.longitude = e.latlng.lng
      addressInfo.latitude = e.latlng.lat
      const geoc = new BMapGL.Geocoder() // 创建地址解析器的实例
      geoc.getLocation(point1, (rs: { addressComponents: any }) => {
        let adr = rs.addressComponents
        addressInfo.address = adr.province + adr.city + adr.district + adr.street + adr.streetNumber // 省市区街道门牌号
        addressInfo.province = adr.province
        addressInfo.city = adr.city
        addressInfo.district = adr.district
      })
      const centerPoint = addressInfo.longitude + ',' + addressInfo.latitude
      // emit('custom-event', centerPoint) // 触发事件并传递数据
      emit('custom-event', addressInfo) // 触发事件并传递数据
    })

    // 监听地址输入框
    ac.addEventListener('onconfirm', function (e: { item: { value: any } }) {
      //鼠标点击下拉列表后的事件
      var _value = e.item.value
      addressInfo.address = _value.province + _value.city + _value.district + _value.street + _value.business
      addressInfo.province = _value.province
      addressInfo.city = _value.city
      addressInfo.district = _value.district
      // 搜索
      map.value.clearOverlays() //清除地图上所有覆盖物
      //智能搜索
      var local = new BMapGL.LocalSearch(map.value, {
        onSearchComplete: () => {
          //获取第一个智能搜索的结果
          const pp = local.getResults().getPoi(0).point
          map.value.centerAndZoom(pp, mapZoom.value)
          map.value.addOverlay(new BMapGL.Marker(pp)) //添加标注
          addressInfo.longitude = pp.lng
          addressInfo.latitude = pp.lat
        }
      })
      local.search(addressInfo.address)
      emit('custom-event', addressInfo)
    })
  })
}

onMounted(() => {
  addressInfo.longitude = props.pointDefault.longitude
  addressInfo.latitude = props.pointDefault.latitude
  initMap()
})
</script>

<style lang="scss">
.tangram-suggestion {
  z-index: 99999;
}

.tangram-suggestion-main {
  z-index: 99999;
}

#baidu-map {
  width: 100%;
  height: 500px;
}
:deep(.BMap_cpyCtrl) {
  display: none !important;
}
:deep(.BMap_cpyCtrl) {
  display: none !important;
}
</style>