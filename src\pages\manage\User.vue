<template>
  <div class="user userContainer">
    <div class="handleWrap clearfix">
      <div class="left fl">
        <a-form layout="inline" :model="searchFormState">
          <a-form-item>
            <a-input v-model:value="searchFormState.keyword" placeholder="请输入用户账号" size="small" allowClear @pressEnter="onSearch"></a-input>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="onSearch">搜索</a-button>
          </a-form-item>
        </a-form>
      </div>
      <div class="right fr">
        <a-button type="primary" @click="addHandle" v-privilege="'system:employee:add'">
          <template #icon>
            <PlusOutlined />
          </template>
          新增
        </a-button>
      </div>
    </div>
    <div class="contentWrap custom-scrollbar" ref="tableWrapperRef">
      <a-table
        ref="tableRef"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="false"
        rowKey="employeeId"
        bordered
        :scroll="{ x: 1200,  y: tableHeight }"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'operation'">
            <span>
              <a-button
                type="link"
                size="small"
                @click="editHandle(record)"
                v-privilege="'system:employee:update'"
                :disabled="record.administratorFlag"
              >编辑</a-button>
              <a-button
                type="link"
                size="small"
                @click="resetPwdHandle(record)"
                :style="record.administratorFlag ? '' : {color: '#52c41a'}"
                v-privilege="'system:employee:password:reset'"
                :disabled="record.administratorFlag"
              >重置密码</a-button>
              <a-button
                type="link"
                size="small"
                danger
                @click="delHandle(record)"
                v-privilege="'system:employee:delete'"
                :disabled="record.administratorFlag"
              >删除</a-button>
            </span>
          </template>
          <template v-if="column.dataIndex === 'disabledFlag'">
            <a-switch :checked="!record.disabledFlag" :disabled="record.administratorFlag" @change="toggleStatus(record)" />
          </template>
        </template>
      </a-table>
    </div>
    <div class="pageWrap">
      <a-pagination
        size="small"
        v-model:current="pagination.pageNum"
        :page-size="pagination.pageSize"
        :total="total"
        show-size-changer
        show-quick-jumper
        :show-total="(total: number) => `共 ${total} 条`"
        :page-size-options="PAGE_SIZE_OPTIONS"
        @change="(current: number, size: number) => onPageChange('current', current, size)"
        @showSizeChange="(current: number, size: number) => onPageChange('size', current, size)"
      />
    </div>
    <a-modal
      v-model:open="addEditModalState.visible"
      :title="addEditModalState.type === 'add' ? '新增用户' : '编辑用户'"
      width="600px"
      cancelText="取消"
      okText="提交"
      :closable="false"
      :keyboard="false"
      :maskClosable="false"
      @cancel="handleCancle"
      @ok="handleOk"
      wrapClassName="custom-modal-style"
    >
      <a-form class="userHandleForm" ref="userFormRef" :model="userFormState" :rules="userRules" :labelCol="{ span: 5 }">
        <a-form-item label="账号" name="loginName">
          <a-input
            v-model:value="userFormState.loginName"
            :maxlength="30"
            :disabled="addEditModalState.type === 'edit'"
            @change="validatePasswordHandle"
          />
        </a-form-item>
        <a-form-item label="姓名" name="actualName">
          <a-input v-model:value="userFormState.actualName" :maxlength="30" />
        </a-form-item>
        <a-form-item label="手机号" name="phone">
          <a-input v-model:value="userFormState.phone" :maxlength="30" @change="validatePasswordHandle" />
        </a-form-item>
        <a-form-item label="邮箱" name="email">
          <a-input v-model:value="userFormState.email" :maxlength="30" />
        </a-form-item>
        <a-form-item label="初始密码" name="loginPwd" validateTrigger="change" v-if="addEditModalState.type === 'add'">
          <a-tooltip :trigger="['focus']" placement="bottomLeft" overlay-class-name="numeric-input">
            <template #title>
              <div style="background: #fff; color: #000;font-size: 12px;">
                <div style="display: flex; align-items: center;padding-bottom: 5px;">
                  <div style="white-space: nowrap; font-size: 14px;">安全等级：</div>
                  <div
                    v-for="(item, index) in 3"
                    :key="index"
                    :style="{width: '30px',height: '12px', background: index >= safeLevel ? '#ddd' : '#52c41a',marginRight: '10px'}"
                  ></div>
                </div>
                <div v-for="(item, index) in passwordOptions" :key="index" style="display: flex; align-items: center;padding-bottom: 5px;">
                  <CloseCircleFilled style="color: #ff4d4f; font-size: 16px; margin-right: 5px;" v-if="!item.passFlag" />
                  <CheckCircleFilled style="color: #52c41a; font-size: 16px; margin-right: 5px;" v-else />
                  <div class="numeric-input-title">{{ item.demand }}</div>
                </div>
              </div>
            </template>
            <a-input
              v-model:value="userFormState.loginPwd"
              :maxlength="32"
              @change="validatePasswordHandle"
              @focus="validatePasswordHandle"
            />
          </a-tooltip>
        </a-form-item>
        <a-form-item label="组织机构" name="departmentId">
          <a-tree-select
            v-model:value="userFormState.departmentId"
            style="width: 100%"
            :tree-data="departmentTreeData"
            allow-clear
            search-placeholder="Please select"
            :fieldNames="{ label: 'name', value: 'departmentId', children: 'children' }"
            :getPopupContainer="triggerNode => triggerNode.parentNode"
            tree-default-expand-all
          />
        </a-form-item>
        <a-form-item label="角色" name="roleIdList">
          <div style="border: 1px solid #d9d9d9; height: 300px; overflow-y: auto;">
            <a-checkbox-group v-model:value="userFormState.roleIdList">
              <a-row style="padding: 20px 30px;">
                <a-col :span="12" v-for="(item, index) in roleOptions" :key="index" style="margin-bottom: 20px;">
                  <a-checkbox :value="item.roleId" class="role-checkbox">
                    <span :title="item.roleName">{{item.roleName}}</span>
                  </a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal
      v-model:open="resetPwdVisible"
      title="重置密码"
      @ok="resetPwdVisible = false"
      :cancelButtonProps="{ style: { display: 'none' } }"
      :closable="false"
      :keyboard="false"
      :maskClosable="false"
    >
      <p class="resetPwdTips">密码已重置，新密码：{{ newPassword }}</p>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import {
  PlusOutlined,
  SlackOutlined,
  MoreOutlined,
  FieldTimeOutlined,
  UserOutlined,
  ExclamationCircleOutlined,
  EnvironmentOutlined,
  DeleteOutlined,
  PlusCircleOutlined
} from '@ant-design/icons-vue'
import { Modal, message } from 'ant-design-vue'
import { ValidateErrorEntity, RuleObject } from 'ant-design-vue/es/form/interface'
import { reactive, ref, UnwrapRef, createVNode, onMounted, computed } from 'vue'
import { userApi } from '@/api/manageTool/userApi'
import { organizationApi } from '@/api/manageTool/organizationApi'
import { roleApi } from '@/api/manageTool/roleApi'
import { PageParamModel } from '@/types/paging'
import { PAGE_SIZE_OPTIONS } from '@/utils/constants'
import { getTableHeight } from '@/hooks/getTableHeight'
import _ from 'lodash'
import { regular } from '@/utils/regular'

// 注册组件
defineOptions({
  components: {
    PlusOutlined,
    SlackOutlined,
    MoreOutlined,
    FieldTimeOutlined,
    UserOutlined,
    EnvironmentOutlined,
    DeleteOutlined,
    PlusCircleOutlined
  }
})
/* ====================== 表格 ====================== */
const tableWrapperRef = ref()
const tableRef = ref()
const tableHeight = getTableHeight(tableWrapperRef, tableRef)

const tableLoading = ref(false)
const columns = reactive([
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    customRender: ({ index }: any) => (pagination.value.pageNum - 1) * pagination.value.pageSize + index + 1,
    fixed: 'left',
    width: 80,
    align: 'center'
  },
  {
    title: '账号',
    dataIndex: 'loginName',
    key: 'loginName',
    fixed: 'left',
    width: 160,
    align: 'center'
  },
  {
    title: '姓名',
    dataIndex: 'actualName',
    key: 'actualName',
    fixed: 'left',
    width: 160,
    align: 'center'
  },
  {
    title: '组织机构',
    dataIndex: 'departmentName',
    key: 'departmentName',
    width: 160,
    align: 'center'
  },
  {
    title: '状态',
    dataIndex: 'disabledFlag',
    key: 'disabledFlag',
    width: 160,
    // slots: {
    //   customRender: 'disabledFlag'
    // },
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 200,
    align: 'center'
  },
  {
    title: '操作',
    key: 'operation',
    dataIndex: 'operation',
    // slots: { customRender: 'operation' },
    fixed: 'right',
    width: 180,
    align: 'center'
  }
])

const dataSource = ref([])

/* ====================== 分页 ====================== */
const pagination = ref<PageParamModel>({
  pageNum: 1, // 当前页码
  pageSize: 10, // 每页数量
  sortItemList: [
    {
      isAsc: false,
      column: 'create_time'
    }
  ]
})
const total = ref<number>(0)
const onPageChange = (type: string, pageNum: number, pageSize: number) => {
  console.log(pageNum, pageSize)
  if (type === 'current') {
    pagination.value.pageNum = pageNum
  } else {
    pagination.value.pageNum = 1
    pagination.value.pageSize = pageSize
  }
  queryEmployeePage()
}
/* ====================== 搜索 ====================== */
interface SearchFormState {
  keyword?: string
}
const searchFormState: UnwrapRef<SearchFormState> = reactive({
  keyword: ''
})
const queryEmployeePage = async () => {
  try {
    tableLoading.value = true
    const result = await userApi.queryEmployeePage({
      ...pagination.value,
      ...searchFormState
    })
    console.log(result)
    dataSource.value = result.data.list
    total.value = result.data.total
  } catch (e) {
    console.log(e)
    dataSource.value = []
    total.value = 0
  } finally {
    tableLoading.value = false
  }
}
const onSearch = () => {
  console.log(searchFormState.keyword)
  pagination.value.pageNum = 1
  total.value = 0
  dataSource.value = []
  queryEmployeePage()
}

const toggleStatus = async (record: any) => {
  const result = await userApi.updateEmployeeDisabled(record.employeeId)
  if (result.code === 0) {
    record.disabledFlag = !record.disabledFlag
  }
}
/* ====================== 表单（新增|编辑） ====================== */

interface UserFormState {
  employeeId: number | string
  loginName: string
  actualName: string
  phone: string
  email: string
  loginPwd: string
  departmentId: number | string
  roleIdList: Array<number>
}

const userFormStateModel = {
  employeeId: '',
  loginName: '',
  actualName: '',
  phone: '',
  email: '',
  loginPwd: '',
  departmentId: '',
  roleIdList: []
}
const userFormState = ref<UserFormState>(_.cloneDeep(userFormStateModel))

const userFormRef = ref()
// 验证密码
const passwordOptions = reactive([
  {
    demand: '8-32个字符，密码不能包含账号、手机号',
    passFlag: false
  },
  {
    demand: '只能包含大小写字母、数字、特殊符号，至少包含其中3种',
    passFlag: false
  },
  {
    demand: '不能包含超过4位连续或相同的字母或者数字组合',
    passFlag: false
  }
])
// const safeLevel = computed(() => {
//   let level = 0
//   passwordOptions.forEach(option => {
//     if (option.passFlag) {
//       level++
//     }
//   })
//   return level
// })
const safeLevel = ref(0)
let validatePassword = async (rule: RuleObject, value: string) => {
  // console.log('validatePassword')
  if (!value) {
    // console.log('请输入初始密码')
    passwordOptions[0].passFlag = false
    passwordOptions[1].passFlag = false
    passwordOptions[2].passFlag = false
    return Promise.reject('')
  }

  // 检查密码长度,是否包含账号和手机号
  if (
    value.length < 8 ||
    value.length > 32 ||
    (userFormState.value.loginName && value.includes(userFormState.value.loginName)) ||
    (userFormState.value.phone && value.includes(userFormState.value.phone))
  ) {
    passwordOptions[0].passFlag = false
    // console.log('密码长度应在8到32个字符之间,不包含账号和手机号')
  } else {
    passwordOptions[0].passFlag = true
  }

  // 检查密码是否只包含大小写字母、数字、特殊符号
  const validChars = /^[a-zA-Z0-9!@#$%^&*(),.?":{}|<>]*$/
  if (!validChars.test(value)) {
    passwordOptions[1].passFlag = false
    // console.log('密码只能包含大小写字母、数字、特殊符号')
  } else {
    passwordOptions[1].passFlag = true
  }

  // 检查密码是否至少包含3种字符类型
  const hasUpperCase = /[A-Z]/.test(value)
  const hasLowerCase = /[a-z]/.test(value)
  const hasDigit = /[0-9]/.test(value)
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value)
  const charTypesCount = [hasUpperCase, hasLowerCase, hasDigit, hasSpecialChar].filter(Boolean).length
  if (charTypesCount < 3) {
    passwordOptions[1].passFlag = false
    // console.log('密码至少包含大小写字母、数字、特殊符号中的3种')
  } else {
    passwordOptions[1].passFlag = true
  }
  // 检查字符串中是否有连续的字母或数字组合
  const isContinuous = (str: string, patt: RegExp, len: number) => {
    try {
      if (patt.test(str)) {
        const arr = str.match(patt)
        if (arr) {
          return arr.some(arrItem => {
            const asciiArr: any[] = []
            return arrItem.split('').some(strItem => {
              if (asciiArr.length === 0) {
                asciiArr.push(strItem.charCodeAt(0))
              } else {
                if (asciiArr[asciiArr.length - 1] + 1 === strItem.charCodeAt(0)) {
                  asciiArr.push(strItem.charCodeAt(0))
                } else {
                  asciiArr.splice(asciiArr.length - 1, 1)
                }
              }
              return asciiArr.length > len
            })
          })
        } else {
          return false
        }
      } else {
        return false
      }
    } catch (e) {
      return false
    }
  }

  // 检查密码是否包含超过4位连续或相同的字母或者数字组合
  const sameChars = /([0-9A-Za-z])\1{4,}/g
  const continuousNumbers = /[\d]{5,}/g
  const continuousChars = /[A-Za-z]{5,}/g
  const sameCharsFlag = sameChars.test(value)
  const continuousNumbersFlag = isContinuous(value, continuousNumbers, 4)
  const continuousChartsFlag = isContinuous(value, continuousChars, 4)
  if (sameCharsFlag || continuousNumbersFlag || continuousChartsFlag) {
    passwordOptions[2].passFlag = false
    // console.log('密码不能包含超过4位连续或相同的字母或者数字组合')
  } else {
    passwordOptions[2].passFlag = true
  }
  if (passwordOptions.every(option => option.passFlag)) {
    return Promise.resolve()
  } else {
    return Promise.reject('密码不符合要求')
  }
}
const validatePasswordHandle = async () => {
  if (userFormState.value.loginPwd) {
    userFormRef.value.validate(['loginPwd'])
    try {
      const params = {
        password: userFormState.value.loginPwd
      }
      const res = await userApi.getPasswordSecurityLevel(params)
      safeLevel.value = res.data
    } catch (error) {
      safeLevel.value = 0
    }
  }
}
const userRules = reactive({
  loginName: [
    { required: true, message: '请输入账号' },
    { min: 6, message: '账号长度不足，至少需要6个字符' }
  ],
  actualName: [{ required: true, message: '请输入姓名' }],
  phone: [{ pattern: regular.phone, message: '请输入正确的手机号码' }],
  email: [{ pattern: regular.email, message: '请输入正确的邮箱地址' }],
  loginPwd: [{ required: true, message: '请输入初始密码' }, { validator: validatePassword }],
  departmentId: [{ required: true, message: '请选择组织结构' }],
  roleIdList: [{ required: true, message: '请选择角色' }]
})
interface AddEditModalModel {
  visible: boolean
  type: '' | 'add' | 'edit'
}
const addEditModalState = ref<AddEditModalModel>({
  visible: false,
  type: ''
})
// 机构
const departmentTreeData = ref([])
const DEPARTMENT_PARENT_ID = 0
const DEPARTMENT_PARENT_Name = ''
const getDepartmentTreeList = async () => {
  try {
    let result = await organizationApi.queryAllDepartment()
    let data = result.data
    departmentTreeData.value = buildDepartmentTree(_.cloneDeep(data), DEPARTMENT_PARENT_ID, DEPARTMENT_PARENT_Name, 1)
  } catch (e) {
    departmentTreeData.value = []
  }
}
// 构建部门树
function buildDepartmentTree(data, parentId, parentName, level) {
  let children = data.filter(e => e.parentId === parentId) || []
  if (!_.isEmpty(children)) {
    children.forEach(e => {
      e.parentName = parentName
      e.level = level
      e.children = buildDepartmentTree(data, e.departmentId, e.name, level + 1)
      // 排序
      if (!_.isEmpty(e.children) && e.children.length > 0) e.children.sort((a, b) => a.sort - b.sort)
    })
    return children
  }
  return null
}
// 角色
const roleOptions = ref<Array<string>>([])
const getRoleAll = async () => {
  try {
    const result = await roleApi.queryRolePage({
      pageNum: 1, // 当前页码
      pageSize: -1, // 每页数量
      sortItemList: [
        {
          isAsc: false,
          column: 'create_time'
        }
      ]
    })
    roleOptions.value = result.data.list.map(item => ({ ...item, label: item.roleName, value: item.roleId }))
  } catch (e) {
    roleOptions.value = []
  }
}
const handleCancle = () => {
  addEditModalState.value.visible = false
  userFormState.value = _.cloneDeep(userFormStateModel)
  userFormRef.value.resetFields()
}
const handleOk = () => {
  console.log(userFormState.value)
  userFormRef.value
    .validate()
    .then(async () => {
      if (addEditModalState.value.type === 'add') {
        await userApi.addEmployee(userFormState.value)
        message.success('新增成功')
        searchFormState.keyword = ''
        onSearch()
      } else {
        await userApi.updateEmployee(userFormState.value)
        message.success('编辑成功')
        queryEmployeePage()
      }
      // console.log(dictConfigFormState.value)
      addEditModalState.value.visible = false
      userFormState.value = _.cloneDeep(userFormStateModel)
      userFormRef.value.resetFields()
    })
    .catch((error: ValidateErrorEntity<UserFormState>) => {
      console.log('error', error)
      // addEditModalState.value.visible = false
    })
}

const addHandle = () => {
  getDepartmentTreeList()
  getRoleAll()
  addEditModalState.value.type = 'add'
  addEditModalState.value.visible = true
}
const editHandle = (item: any) => {
  getDepartmentTreeList()
  getRoleAll()
  addEditModalState.value.type = 'edit'
  addEditModalState.value.visible = true
  userFormState.value = {
    employeeId: item.employeeId,
    loginName: item.loginName,
    actualName: item.actualName,
    phone: item.phone,
    email: item.email,
    loginPwd: '',
    departmentId: item.departmentId,
    roleIdList: item.roleIdList
  }
}
const resetPwdVisible = ref(false)
const newPassword = ref('')
const resetPwdHandle = async (item: any) => {
  Modal.confirm({
    title: () => '提醒',
    icon: () => createVNode(ExclamationCircleOutlined),
    content: () => '确定要重置密码吗？',
    okText: () => '确认',
    okType: 'danger',
    cancelText: () => '取消',
    onOk: async () => {
      try {
        const result = await userApi.resetEmployeePassword(item.employeeId)
        newPassword.value = result.data
        resetPwdVisible.value = true
      } catch (e) {
        console.log(e)
      }
    },
    onCancel() {
      console.log('Cancel')
    }
  })
}
const delHandle = (item: any) => {
  console.log(item)
  Modal.confirm({
    title: () => '删除提醒',
    icon: () => createVNode(ExclamationCircleOutlined),
    content: () => '确定是否删除该用户？',
    okText: () => '确认',
    okType: 'danger',
    cancelText: () => '取消',
    onOk: async () => {
      try {
        await userApi.batchDeleteEmployee([item.employeeId])
        message.success('删除成功')
        if (pagination.value.pageNum > 1 && dataSource.value.length === 1) {
          pagination.value.pageNum = pagination.value.pageNum - 1
        }
        queryEmployeePage()
      } catch (e) {
        console.log(e)
      }
    },
    onCancel() {
      console.log('Cancel')
    }
  })
}
onMounted(() => {
  queryEmployeePage()
})
</script>
<style lang="scss">
.userContainer {
  height: 100%;

  .handleWrap {
    height: 54px;
    padding: 10px;
    box-sizing: border-box;

    .left {
      width: 80%;

      .ant-input {
        box-sizing: border-box;
        width: 240px;
        height: 34px;
      }

      .ant-btn {
        height: 34px;
      }
    }

    .right {
      width: 20%;
      text-align: right;

      .ant-btn {
        height: 34px;
      }
    }
  }

  .contentWrap {
    height: calc(100% - 110px);
    padding: 10px;
    overflow: hidden;

    .ant-card {
      height: 100%;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      border-radius: 6px;
      cursor: default;

      &:hover {
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
      }

      .ant-card-body {
        padding: 15px;
      }

      .cardHead {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #000;

        .headIcon {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          background: #d1cdfc;
          text-align: center;
          min-width: 50px;

          .anticon-slack {
            font-size: 30px;
            color: #200de7;
            width: 30px;
            height: 30px;
            margin-top: 10px;
          }
        }

        .cardTitle {
          font-size: 14px;
          font-weight: 600;
          margin: 0 10px;
        }

        .ant-dropdown-link {
          color: #000;

          &:hover {
            color: #1890ff;
          }
        }
      }

      .cardInfo {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #999;
        margin: 10px 0;

        .anticon {
          margin-right: 4px;
        }
      }

      .cardRemarks {
        border-top: 1px solid #eee;
        font-size: 12px;
        color: #666;
        padding-top: 10px;
      }
    }
  }

  .pageWrap {
    text-align: right;
    margin-top: 20px;
    height: 38px;
    padding-right: 20px;
  }
}

.ant-modal-body {
  padding: 20px 30px 0 20px;
}

.userHandleForm {
  .ant-input {
    vertical-align: middle;
  }

  .ant-select {
    vertical-align: middle;
  }

  .anticon-environment {
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    color: #666;
    vertical-align: middle;
  }

  .anticon-delete {
    color: red;
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    vertical-align: middle;
  }

  .anticon-plus-circle {
    color: blue;
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    vertical-align: middle;
  }

  .targetItem {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.resetPwdTips {
  text-align: center;
  margin: 20px 0 30px;
  font-size: 20px;
}
.numeric-input.ant-tooltip {
  max-width: none;
  .ant-tooltip-arrow::before {
    background-color: #fff;
  }
  .ant-tooltip-inner {
    min-width: 32px;
    min-height: 37px;
    background-color: #fff;
  }
  .numeric-input-title {
    white-space: nowrap;
    font-size: 14px;
  }
}
.role-checkbox.ant-checkbox-wrapper {
  width: 100%;
  span:nth-of-type(2) {
    /* 样式 */
    width: calc(100% - 16px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
