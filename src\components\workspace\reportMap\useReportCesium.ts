import { reportViewer } from "./initReportCesium";
import * as Cesium from "cesium";
import startBg from '@/assets/images/startBg.png';
import endBg from '@/assets/images/endBg.png';
export function useReportCesium() {

  const flyToEntitiesCenter = (points: any) => {
    // 获取所有点的边界框
    const pointPositions = points.map((point: any) =>
      Cesium.Cartesian3.fromDegrees(point.lon, point.lat)
    );

    const boundingBox = Cesium.BoundingSphere.fromPoints(pointPositions);
    // 设置视角包含所有内容
    reportViewer.zoomTo(
      reportViewer.entities,
      new Cesium.HeadingPitchRange(
        0,
        -Cesium.Math.PI_OVER_TWO,
        boundingBox.radius * 4
      )
    );
  };

   // 添加点
   const addpoint = (point: any, image: any, labelColor: any) => {
    // console.log(point);
     if (image) {
       reportViewer.entities.add({
         id: point.id,
         data: point,
         position: Cesium.Cartesian3.fromDegrees(point.lon, point.lat),
         billboard: {
           image: image, // 使用相对路径引入图片资源
           width: 40,
           height: 40,
           verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 确保图片底部中心指向坐标点
         },
         label: {
           text: point.name ? (point.name + ':' + (point.alarmVO ? ((point.alarmVO.maxValue || point.alarmVO.maxValue === 0) ? (point.alarmVO.maxValue.toString() + (point.detectTargetUnit || '')) : '数据缺失') : '无数据')) : ((point.maxValue || point.maxValue === 0) ? (point.maxValue.toString() + (point.detectTargetUnit || '')) : '数据缺失'),
           font: "14px sans-serif",
           fillColor: Cesium.Color.fromCssColorString(
             labelColor ? labelColor : "#D20000"
           ),
           outlineColor: Cesium.Color.fromCssColorString("#AAA"),
           outlineWidth: 0,
           style: Cesium.LabelStyle.FILL_AND_OUTLINE,
           verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 确保标签位于点的上方
           pixelOffset: new Cesium.Cartesian2(6, -44), // 调整标签位置，确保标签位于点的上方
         },
       });
     } else {
       reportViewer.entities.add({
         id: point.id,
         data: point,
         position: Cesium.Cartesian3.fromDegrees(point.lon, point.lat),
         billboard: {
           image: image, // 使用相对路径引入图片资源
           width: 40,
           height: 40,
         },
         point: {
           pixelSize: 20, // 点的大小，以像素为单位
           color: Cesium.Color.RED, // 点的颜色
           outlineColor: Cesium.Color.WHITE, // 点轮廓颜色
           outlineWidth: 2, // 点轮廓宽度
         },
       });
     }
   };

  // 添加轨迹
  const addTrack = (trackPointsArr: any) => {
    console.log(111111111111111111);
    // 不做平滑处理的线,将坐标转换为 Cartesian3 格式
    let trackPoints = trackPointsArr.map((point: any) =>
      Cesium.Cartesian3.fromDegrees(point.lon, point.lat)
    );

    console.log(222222222222222222222);
    
    
    // 去除重复的点
    const cartesianPositions = trackPoints.filter(
      (point: any, index: number, self: any) =>
        index === 0 || !Cesium.Cartesian3.equals(point, self[index - 1])
    );

    // 创建一条折线来连接这些点
    reportViewer.entities.add({
      polyline: {
        positions: cartesianPositions,
        width: 4,
        material: new Cesium.ColorMaterialProperty(Cesium.Color.BLUE),
      },
    });

    // 给第一个点添加绿色水滴背景和"S"标记
    if (trackPointsArr.length > 0) {
      const firstPointPosition = Cesium.Cartesian3.fromDegrees(
        trackPointsArr[0].lon,
        trackPointsArr[0].lat
      );
      reportViewer.entities.add({
        position: firstPointPosition,
        billboard: {
          image: startBg, // 替换为实际路径
          width: 54,
          height: 70,
          scale: 0.5,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 确保图片底部中心指向坐标点
        },
      });
    }

    // // 给最后一个点添加红色水滴背景和"E"标记
    if (trackPointsArr.length > 1) {
      const lastPointPosition = Cesium.Cartesian3.fromDegrees(
        trackPointsArr[trackPointsArr.length - 1].lon,
        trackPointsArr[trackPointsArr.length - 1].lat
      );
      reportViewer.entities.add({
        position: lastPointPosition,
        billboard: {
          image: endBg, // 替换为实际路径
          width: 54,
          height: 70,
          scale: 0.5,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 确保图片底部中心指向坐标点
        },
      });
    }

    flyToEntitiesCenter(trackPointsArr);
  };

  // 清除地图上的所有内容
  const clearMap = () => {
    if (reportViewer.entities) {
      reportViewer.entities.removeAll(); // 移除所有实体
    }
  };

  return {
    addpoint,
    flyToEntitiesCenter,
    addTrack,
    clearMap,
  };
}
