<template>
  <div class="login loginContainer">
    <div class="videoWrap">
      <video class="loginVideo" :src="videoSrc" autoplay loop muted></video>
    </div>
    <div class="loginMain">
      <div class="loginContent">
        <h2>无人载具及作业管理平台</h2>
        <div :class="['loginFormBox', loginFormShow ? 'loginFormBoxShow' : '']">
          <a-form :model="formState" class="loginForm" v-show="loginFormShow">
            <a-form-item>
              <p class="formItemTitle">账号</p>
              <a-input v-model:value="formState.loginName" placeholder="账号">
                <template #prefix>
                  <UserOutlined />
                </template>
              </a-input>
            </a-form-item>
            <a-form-item>
              <p class="formItemTitle">密码</p>
              <a-input v-model:value="formState.password" type="password" placeholder="密码">
                <template #prefix>
                  <LockOutlined />
                </template>
              </a-input>
            </a-form-item>
            <a-form-item>
              <a-button class="formLoginBtn" type="primary" html-type="submit" :disabled="loginBtnDisabled" @click="onSubmit">登录</a-button>
            </a-form-item>
          </a-form>
        </div>
        <div class="loginBtn">
          <div class="loginOne" v-if="!loginFormShow" @click="loginFormShow = true">登录</div>
          <div class="closeOne" v-else @click="loginFormShow = false">
            <CloseCircleOutlined />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { LockOutlined, UserOutlined, CloseCircleOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { reactive, computed, UnwrapRef, ref, onMounted } from 'vue'
import { getRoot } from '@/root'
import { ELocalStorageKey, ERouterName, EUserType } from '@/types'
import { encryptData } from '@/utils/encrypt'
import { loginApi, LoginBody } from '@/api/loginApi'
import { LOGIN_DEVICE_ENUM } from '@/utils/constants'
import { useStore } from 'vuex'
import { buildRoutes } from '@/router'
const store = useStore()
// 注册组件
defineOptions({
  components: {
    LockOutlined,
    UserOutlined,
    CloseCircleOutlined
  }
})

const root = getRoot()

const loginFormShow = ref(false)
const validateCodeShow = ref(false)

const formState: UnwrapRef<LoginBody> = reactive({
  captchaCode: '1',
  captchaUuid: '1',
  loginName: 'admin',
  password: '123456',
  loginDevice: LOGIN_DEVICE_ENUM.PC.value,
  emailCode: ''
})

const loginBtnDisabled = computed(() => {
  return !formState.loginName || !formState.password
})

const onSubmit = async (e: any) => {
  const encryptPasswordForm: LoginBody = { ...formState, password: encryptData(formState.password) }
  const result: any = await loginApi.login(encryptPasswordForm)
  if (result.code === 0) {
    localStorage.setItem(ELocalStorageKey.Token, result.data.token)
    buildRoutes(result.data.menuList)
    store.dispatch('user/setLoginInfo', result.data)
    root.$router.push(ERouterName.HOME)
    message.success('登录成功')
  } else {
    message.error(result.msg)
  }
}
// 动态导入视频路径
const videoSrc = ref<string>('')
onMounted(async () => {
  videoSrc.value = (await import('@/assets/video/350RTK video.webm')).default
  document.title = '无人载具与作业管理平台'
})
</script>

<style lang="scss" scoped>
.loginContainer {
  width: 100vw;
  height: 100vh;
  position: relative;
  background: #000;
  .videoWrap {
    width: 100%;
    height: 100%;
    .loginVideo {
      width: 100%;
      height: 100%;
    }
  }

  .loginMain {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    .loginContent {
      width: 1000px;
      height: 100%;
      margin: 0 auto;
      position: relative;
      overflow: hidden;
      h2 {
        color: #fff;
        font-size: 44px;
        text-align: center;
        margin: 100px 0;
        // font-weight: bold;
        letter-spacing: 2px;
      }

      .loginFormBox {
        width: 400px;
        border-radius: 6px;
        background: rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(40px);
        margin: 0 auto;
        opacity: 0;
        transition: all 1s;
        height: 0;
        overflow: hidden;

        &.loginFormBoxShow {
          height: auto;
          opacity: 1;
          padding: 38px 32px 28px;
        }

        .loginForm {
          .ant-form-item {
            margin-bottom: 20px;
          }

          .formItemTitle {
            color: #fff;
            line-height: 40px;
          }

          .ant-input-affix-wrapper {
            height: 34px;
            background: none;
            color: #fff;
            border: none;
            padding: 0 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            :deep(.ant-input-prefix) {
              margin-right: 10px;
              .anticon {
                color: #fff !important;
              }
            }
            :deep(.ant-input) {
              background: none;
              color: #fff;
            }
          }

          .formLoginBtn {
            width: 100%;
            background: #fff;
            color: #333;
            border: none;
            margin-top: 20px;
            &:hover {
              opacity: 0.9;
            }
          }
        }
      }

      .loginBtn {
        position: absolute;
        bottom: 80px;
        color: #fff;
        left: 50%;
        transform: translateX(-50%);
        .loginOne {
          height: 40px;
          padding: 0 25px;
          border: 1px solid #fff;
          line-height: 38px;
          border-radius: 20px;
          cursor: pointer;
          letter-spacing: 2px;
          &:hover {
            color: #000;
            background: #fff;
          }
        }
        .closeOne {
          font-size: 38px;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
@/api/loginApi