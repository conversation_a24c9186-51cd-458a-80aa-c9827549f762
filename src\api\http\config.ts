export const CURRENT_CONFIG = {
  // 大疆注册的应用信息
  appId: "Please enter the app id.",
  appKey: "Please enter the app key.",
  appLicense: "Please enter the app license.",

  // 网络配置
  // baseURL: 'http://**************:6789/', // This url must end with "/". Example: 'http://***********:6789/'
  // baseURL: "http://***************:11024", // This url must end with "/". Example: 'http://***********:6789/'
  baseURL: "http://**************:11023", // This url must end with "/". Example: 'http://***********:6789/' 测试
  websocketURL: "Please enter the WebSocket access address.", // Example: 'ws://***********:6789/api/v1/ws'

  // 流媒体服务
  // RTMP:This IP is the address of the streaming server. If you want to see livestream on web page, you need to convert the RTMP stream to WebRTC stream.
  rtmpURL: "Please enter the rtmp access address.", // Example: 'rtmp://***********/live/'
  // WebRTC:This IP is the address of the streaming server. If you want to see livestream on web page, you need to convert the RTMP stream to WebRTC stream.
  // webrtcURL: 'Please enter the WebRTC access address.', // Example: 'ws://***********:6789/api/v1/ws'

  // GB28181 Note:If you don't know what these parameters mean, you can go to Pilot2 and select the GB28181 page in the cloud platform. Where the parameters same as these parameters.
  gbServerIp: "Please enter the server ip.",
  gbServerPort: "Please enter the server port.",
  gbServerId: "Please enter the server id.",
  gbAgentId: "Please enter the agent id",
  gbPassword: "Please enter the agent password",
  gbAgentPort: "Please enter the local port.",
  gbAgentChannel: "Please enter the channel.",

  // RTSP
  rtspUserName: "Please enter the username.",
  rtspPassword: "Please enter the password.",
  rtspPort: "8554",

  // Agora Note:If you don't know what these parameters mean, you can go to Pilot2 and select the Agora page in the cloud platform. Where the parameters same as these parameters.
  agoraAPPID: "Please enter the agora app id.",
  agoraToken: "Please enter the agora temporary token.",
  agoraChannel: "Please enter the agora channel.",

  // 地图配置信息
  // amapKey: 'Please enter the amap key.',
  baiduKey: "Please enter the baidu key.",
};
