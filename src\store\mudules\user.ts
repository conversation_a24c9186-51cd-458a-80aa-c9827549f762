import { Module } from 'vuex'
import { ActionTree, GetterTree, MutationTree } from 'vuex'
import { RootStateType } from '../index'
import { MENU_TYPE_ENUM } from '@/utils/constants'
import { deleteAllCookies } from '@/utils/cookies'
import _ from 'lodash'

// 定义模块的状态类型
export interface RouteState {
  userInfo: object
  // 菜单数据
  menuData: Array<any>
  // 菜单树数据
  menuTreeData: Array<any>
  // 功能点数据
  pointList: Array<any>
  administratorFlag: boolean
}

// 初始化状态
const state: RouteState = {
  userInfo: {},
  menuData: [],
  menuTreeData: [],
  pointList: [],
  administratorFlag: true
}

// 定义 mutations
const mutations: MutationTree<RouteState> = {
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo
  },
  SET_MENU_DATA(state, menuData) {
    state.menuTreeData = menuData
  },
  SET_MENU_TREE_DATA(state, menuTreeData) {
    state.menuTreeData = menuTreeData
  },
  SET_POINT_DATA(state, pointList) {
    state.pointList = pointList
  },
  SET_ADMINISTRATOR_FLAG(state, administratorFlag) {
    state.administratorFlag = administratorFlag
  }
}

// 定义 actions
const actions: ActionTree<RouteState, RootStateType> = {
  // 保存菜单数据
  setLoginInfo({ commit }, info: Array<any>) {
    commit('SET_USER_INFO', info)
    commit('SET_ADMINISTRATOR_FLAG', !!info.administratorFlag)
    const menuData = info.menuList || []
    // 菜单数据
    const menuArr = menuData.map(menu => {
      return {
        ...menu,
        menuId: menu.menuId.toString()
      }
    })
    commit('SET_MENU_DATA', _.cloneDeep(menuArr))
    const menuTreeData = buildMenuTree(_.cloneDeep(menuArr))
    commit('SET_MENU_TREE_DATA', menuTreeData)
    const pointList = _.cloneDeep(menuArr).filter(menu => {
      return menu.menuType == MENU_TYPE_ENUM.POINTS.value && menu.visibleFlag && !menu.disabledFlag
    })
    commit('SET_POINT_DATA', pointList)
  },
  logout() {
    localStorage.clear()
    sessionStorage.clear()
    deleteAllCookies()
  },
}

// 定义 getters
const getters: GetterTree<RouteState, RootStateType> = {
  userInfo: state => state.userInfo,
  menuData: state => state.menuTreeData,
  menuTreeData: state => state.menuTreeData,
  // 根据菜单名称获取对应的菜单
  getMenuByName: state => (menuName: string) => {
    const findMenu = (menus: Array<any>, name: string): any => {
      for (const menu of menus) {
        if (menu.menuName == name) {
          return menu
        }
        if (menu.children) {
          const found = findMenu(menu.children, name)
          if (found) {
            return found
          }
        }
      }
      return null
    }
    return findMenu(state.menuTreeData, menuName)
  },
  pointList: state => state.pointList,
  administratorFlag: state => {
    const userInfo = state.userInfo
    return userInfo.administratorFlag || false
  }
}

// 定义模块
const user: Module<RouteState, RootStateType> = {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
export default user
// 构建菜单树
function buildMenuTree(menuList) {
  function buildMenuChildren(menu, allMenuList) {
    let children = allMenuList.filter(e => e.parentId == menu.menuId)
    if (children.length === 0) {
      return
    }
    menu.children = children
    for (const item of children) {
      buildMenuChildren(item, allMenuList)
    }
  }
  //1 获取有效的目录和菜单
  let catalogAndMenuList = menuList.filter(menu => menu.menuType != MENU_TYPE_ENUM.POINTS.value && !menu.disabledFlag)
  //2 获取顶级目录
  let topCatalogList = catalogAndMenuList.filter(menu => menu.parentId == 0)
  for (const topCatalog of topCatalogList) {
    buildMenuChildren(topCatalog, catalogAndMenuList)
  }
  return topCatalogList
}
