<template>
  <div class="reportCesiumMapContainer">
    <div id="reportCesiumContainer" class="reportCesiumContainer" ref="cesiumContainer"></div>
  </div>
</template>

      
<script lang="ts" setup >
import {
  watch,
  onMounted,
  onBeforeUnmount,
  ref
} from 'vue'
import { useRoute } from 'vue-router';
import { initReportCesium, reportViewer } from './initReportCesium';
import { useReportCesium } from './useReportCesium';
import detection1 from "@/assets/images/detection1.png";
import detection2 from "@/assets/images/detection2.png";
import detection3 from "@/assets/images/detection3.png";
import airPoint1 from "@/assets/images/airPoint1.png";
import airPoint2 from "@/assets/images/airPoint2.png";
import airPoint3 from "@/assets/images/airPoint3.png";

initReportCesium()
const { addTrack, clearMap, addpoint } = useReportCesium();

// 定义props
const props = defineProps({
  // 坐标点
  pointsArr: {
    type: Array,
    default: [],
  },
  // 自主检测报警点
  alarmPointsArr: {
    type: Array,
    default: [],
  },
  // 轨迹数据
  trackData: {
    type: Array,
    default: [],
  }
});

const route = useRoute();

watch(() => route.path, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    clearMap()
  }
})

watch(() => props.pointsArr, (newValue, oldValue) => {
  if (newValue.length > 0) {
    setPointWithStatus(newValue)
  }
});

watch(() => props.alarmPointsArr, (newValue, oldValue) => {
  if (newValue.length > 0) {
    setAlarmPointWithStatus(newValue)
  }
});

watch(() => props.trackData, (newValue, oldValue) => {
  if (newValue.length > 0) {
    newValue.forEach((item: any) => {
      if (item.lon === undefined || item.lat === undefined) {
        console.error("Invalid longitude or latitude:", item);
        return;
      }
    });
    addTrack(newValue)
  }
});

const setPointWithStatus = (points: any) => {
  console.log(points);
  points.forEach((point: any) => {
    if(point.alarmVO) {
      if (point.alarmVO.maxValue && point.alarmVO.gradeId) {
        addpoint(point, airPoint3, '#CE1A0D');
      } else if (point.alarmVO.maxValue && !point.alarmVO.gradeId) {
        addpoint(point, airPoint2, '#C8860D');
      } else {
        addpoint(point, airPoint1, '#0C9815');
      }
    } else {
      addpoint(point, airPoint1, '#0C9815');
    }

  });
};

const setAlarmPointWithStatus = (points: any) => {
  points.forEach((point: any) => {
    if (point.maxValue && point.gradeId) {
      addpoint(point, detection3, '#CE1A0D') ;
    } else if (point.maxValue && !point.gradeId) {
      addpoint(point, detection2, '#C8860D');
    } else {
      addpoint(point, detection1, '#0C9815');
    }
  });
};

onMounted(() => {
  if (props.trackData.length > 0) {
    addTrack(props.trackData)
  }
});

onBeforeUnmount(() => {
  if(reportViewer) {
    reportViewer.destroy();
  }
});


</script>
    
<style scoped>
.reportCesiumMapContainer{
  width: 100%;
  height: 100%;
}
.reportCesiumContainer {
  width: 100%;
  height: 100%;
}
</style>
