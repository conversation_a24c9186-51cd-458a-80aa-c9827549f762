<template>
  <div class="load loadContainer">
    <div class="handleWrap clearfix">
      <div class="left fl">
        <a-form layout="inline" :model="searchFormState">
          <a-form-item>
            <a-select v-model:value="searchType" style="width: 130px;" @change="searchTypeChangeHandle">
              <a-select-option value="loadName">负载名称</a-select-option>
              <a-select-option value="loadCode">负载编码</a-select-option>
              <a-select-option value="loadType">负载类型</a-select-option>
              <a-select-option value="status">在离线状态</a-select-option>
              <a-select-option value="remindStatus">保险过期提醒</a-select-option>
              <a-select-option value="createTime">创建时间</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item v-if="searchType === 'loadName'" style="width: 280px;">
            <a-input v-model:value="searchFormState.loadName" placeholder="请输入负载名称" size="small" allowClear @pressEnter="onSearch"></a-input>
          </a-form-item>
          <a-form-item v-else-if="searchType === 'loadCode'" style="width: 280px;">
            <a-input v-model:value="searchFormState.loadCode" placeholder="请输入负载编码" size="small" allowClear @pressEnter="onSearch"></a-input>
          </a-form-item>
          <a-form-item v-else-if="searchType === 'loadType'" style="width: 280px;">
            <DictSelect dict-code="load_type" v-model:value="searchFormState.loadType" width="100%" placeholder="请选择负载类型" />
          </a-form-item>
          <a-form-item v-else-if="searchType === 'status'" style="width: 280px;">
            <a-select v-model:value="searchFormState.status" style="width: 100%;" placeholder="请选择在离线状态">
              <a-select-option value="0">在线</a-select-option>
              <a-select-option value="1">离线</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item v-else-if="searchType === 'remindStatus'" style="width: 280px;">
            <a-select v-model:value="searchFormState.remindStatus" style="width: 100%;" placeholder="请选择保险过期提醒">
              <a-select-option value="0">正常</a-select-option>
              <a-select-option value="1">临期</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item v-else style="width: 280px;">
            <a-date-picker
              format="YYYY-MM-DD"
              valueFormat="YYYY-MM-DD"
              placeholder="请选择日期时间"
              v-model:value="searchFormState.createTime"
              style="width: 100%;"
            />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="onSearch" v-privilege="'payload:query'">搜索</a-button>
          </a-form-item>
        </a-form>
      </div>
      <div class="right fr">
        <a-button type="primary" @click="addHandle" v-privilege="'payload:add'">
          <template #icon>
            <PlusOutlined />
          </template>
          新增
        </a-button>
      </div>
    </div>
    <div class="contentWrap custom-scrollbar" ref="tableWrapperRef">
      <a-table
        ref="tableRef"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="false"
        rowKey="id"
        bordered
        :scroll="{ x: 1200, y: tableHeight }"
        :loading="tableLoading"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'operation'">
            <span>
              <a-button type="link" size="small" @click="editHandle(record)" v-privilege="'payload:update'">编辑</a-button>
              <a-button type="link" size="small" danger @click="delHandle(record)" v-privilege="'payload:delete'">删除</a-button>
            </span>
          </template>
          <template v-if="column.dataIndex === 'status'">
            <span :style="{color: record.status === 1 ? '' : '#52c41a'}">{{ record.status===1 ? '离线' : '在线'}}</span>
          </template>
          <template v-if="column.dataIndex === 'remindStatus'">
            <span :style="{ color: record.remindStatus === 1 ? '#fa8c16' : '' }">{{ record.remindStatus === 1 ? '临期' : '' }}</span>
          </template>
        </template>
      </a-table>
    </div>
    <div class="pageWrap">
      <a-pagination
        size="small"
        v-model:current="pagination.pageNum"
        :page-size="pagination.pageSize"
        :total="total"
        show-size-changer
        show-quick-jumper
        :show-total="(total: number) => `共 ${total} 条`"
        :page-size-options="PAGE_SIZE_OPTIONS"
        @change="(current: number, size: number) => onPageChange('current', current, size)"
        @showSizeChange="(current: number, size: number) => onPageChange('size', current, size)"
      />
    </div>
    <a-modal
      v-model:open="addEditModalState.visible"
      :title="addEditModalState.type === 'add' ? '新增负载' : '编辑负载'"
      width="600px"
      cancelText="取消"
      okText="提交"
      :closable="false"
      :keyboard="false"
      :maskClosable="false"
      @cancel="handleCancle"
      @ok="handleOk"
    >
      <a-form class="loadHandleForm" ref="loadFormRef" :model="loadFormState" :rules="loadRules" :labelCol="{ span: 5 }">
        <a-form-item ref="loadCode" label="负载编码" name="loadCode">
          <a-input v-model:value="loadFormState.loadCode" :disabled="addEditModalState.type === 'edit'" :maxlength="30" />
        </a-form-item>
        <a-form-item ref="loadName" label="负载名称" name="loadName">
          <a-input v-model:value="loadFormState.loadName" :maxlength="30" />
        </a-form-item>
        <a-form-item ref="loadType" label="负载类型" name="loadType">
          <DictSelect dict-code="load_type" v-model:value="loadFormState.loadType" width="100%" @change="loadTypeChangeHandle" />
        </a-form-item>
        <a-form-item ref="model" label="型号" name="model">
          <a-input v-model:value="loadFormState.model" :maxlength="30" />
        </a-form-item>
        <a-form-item
          ref="targetList"
          label="检测目标"
          name="targetList"
          :rules="loadFormState.loadType == 1 ? [] : [{ required: true, message: '' }]"
          style="margin-bottom: 0;"
          v-if="loadFormState.targetList.length"
        >
          <div class="targetItem" v-for="(item, index) in loadFormState.targetList" :key="index">
            <a-form-item
              :name="['targetList', index]"
              :rules="[{
                required: true,
                message: '请选择检测目标'
              }]"
            >
              <div style="display: flex; align-items: center;">
                <DictSelect dict-code="detect_target" v-model:value="loadFormState.targetList[index]" width="85%" />
                <DeleteOutlined v-if="loadFormState.targetList.length > 1" title="删除目标" @click="delTarget(index)" />
                <PlusCircleOutlined v-if="index == loadFormState.targetList.length - 1" title="添加目标" @click="addTarget" />
              </div>
            </a-form-item>
          </div>
        </a-form-item>
        <a-form-item ref="insurance" label="在保时间" name="insurance">
          <a-range-picker
            v-model:value="loadFormState.insurance"
            style="width: 100%;"
            format="YYYY-MM-DD"
            valueFormat="YYYY-MM-DD"
            @change="insuranceChangeHandle"
          />
        </a-form-item>
        <a-form-item ref="remind" label="保险期提醒天数" name="remind">
          <a-input v-model:value="loadFormState.remind" :maxlength="30" />
        </a-form-item>
        <a-form-item ref="vendor" label="供应商" name="vendor">
          <a-input v-model:value="loadFormState.vendor" :maxlength="30" />
        </a-form-item>
        <a-form-item ref="vendorContact" label="供应商联系人" name="vendorContact">
          <a-input v-model:value="loadFormState.vendorContact" :maxlength="30" />
        </a-form-item>
        <a-form-item ref="vendorPhone" label="供应商电话" name="vendorPhone">
          <a-input v-model:value="loadFormState.vendorPhone" :maxlength="30" />
        </a-form-item>
        <a-form-item ref="des" label="描述" name="des">
          <a-textarea v-model:value="loadFormState.des" :auto-size="{ minRows: 2, maxRows: 3 }" :maxlength="1000" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import {
  PlusOutlined,
  SlackOutlined,
  MoreOutlined,
  FieldTimeOutlined,
  UserOutlined,
  ExclamationCircleOutlined,
  EnvironmentOutlined,
  DeleteOutlined,
  PlusCircleOutlined
} from '@ant-design/icons-vue'
import DictSelect from '@/components/common/dict-select/index.vue'
import moment, { Moment } from 'moment'
// import 'moment/locale/zh-cn'
// moment.locale('zh-cn')
import { Modal, message } from 'ant-design-vue'
import { ValidateErrorEntity } from 'ant-design-vue/es/form/interface'
import { reactive, ref, UnwrapRef, createVNode, onMounted, watch } from 'vue'
import { loadApi } from '@/api/manageTool/loadApi'
import { PageParamModel } from '@/types/paging'
import { PAGE_SIZE_OPTIONS } from '@/utils/constants'
import { getTableHeight } from '@/hooks/getTableHeight'
import _ from 'lodash'
import { regular } from '@/utils/regular'
// 注册组件
defineOptions({
  components: {
    PlusOutlined,
    SlackOutlined,
    MoreOutlined,
    FieldTimeOutlined,
    UserOutlined,
    EnvironmentOutlined,
    DeleteOutlined,
    PlusCircleOutlined,
    DictSelect
  }
})

/* ====================== 表格 ====================== */
const tableWrapperRef = ref()
const tableRef = ref()
const tableHeight = getTableHeight(tableWrapperRef, tableRef)

const tableLoading = ref(false)
const columns = reactive([
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    customRender: ({ index }: any) => (pagination.value.pageNum - 1) * pagination.value.pageSize + index + 1,
    fixed: 'left',
    width: 80,
    align: 'center'
  },
  {
    title: '负载名称',
    dataIndex: 'loadName',
    key: 'loadName',
    fixed: 'left',
    width: 160,
    align: 'center'
  },
  {
    title: '负载编码',
    dataIndex: 'loadCode',
    key: 'loadCode',
    width: 160,
    align: 'center'
  },
  {
    title: '负载类型',
    dataIndex: 'loadTypeName',
    key: 'loadTypeName',
    width: 160,
    align: 'center'
  },
  {
    title: '在离线状态',
    dataIndex: 'status',
    key: 'status',
    // slots: { customRender: 'status' },
    width: 160,
    align: 'center'
  },
  {
    title: '保险过期提醒',
    dataIndex: 'remindStatus',
    key: 'remindStatus',
    // slots: { customRender: 'remindStatus' },
    width: 160,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 200,
    align: 'center'
  },
  {
    title: '操作',
    key: 'operation',
    dataIndex: 'operation',
    // slots: { customRender: 'operation' },
    fixed: 'right',
    width: 130,
    align: 'center'
  }
])

const dataSource = ref([])
/* ====================== 分页 ====================== */
const pagination = ref<PageParamModel>({
  pageNum: 1, // 当前页码
  pageSize: 10, // 每页数量
  sortItemList: [
    {
      isAsc: false,
      column: 'create_time'
    }
  ]
})
const total = ref<number>(0)
const onPageChange = (type: string, pageNum: number, pageSize: number) => {
  console.log(pageNum, pageSize)
  if (type === 'current') {
    pagination.value.pageNum = pageNum
  } else {
    pagination.value.pageNum = 1
    pagination.value.pageSize = pageSize
  }
  queryPayloadPage()
}

/* ====================== 搜索 ====================== */
const searchType = ref('loadName')
interface SearchFormState {
  loadName: string
  loadCode: string
  loadType: string | number | null
  status: string | number | null
  remindStatus: string | number | null
  createTime: string
}
const searchFormStateModel = {
  loadName: '',
  loadCode: '',
  loadType: null,
  status: null,
  remindStatus: null,
  createTime: ''
}
const searchFormState: UnwrapRef<SearchFormState> = reactive({ ...searchFormStateModel })

const queryPayloadPage = async () => {
  try {
    tableLoading.value = true
    const result = await loadApi.queryPayloadPage({
      ...pagination.value,
      ...searchFormState
    })
    console.log(result)
    dataSource.value = result.data.list
    total.value = result.data.total
  } catch (e) {
    console.log(e)
    dataSource.value = []
    total.value = 0
  } finally {
    tableLoading.value = false
  }
}

const onSearch = () => {
  console.log(searchFormState.loadName)
  pagination.value.pageNum = 1
  total.value = 0
  dataSource.value = []
  queryPayloadPage()
}
const searchTypeChangeHandle = () => {
  for (const key in searchFormState) {
    if (key !== searchType.value) {
      searchFormState[key] = searchFormStateModel[key]
    }
  }
}

/* ====================== 表单（新增|编辑） ====================== */
interface LoadFormState {
  id: number | null
  loadCode: string
  loadName: string
  loadType: string
  model: string
  insurance: Array<string>
  insStartTime?: string
  insEndTime?: string
  remind: number
  vendor: string
  vendorContact: string
  vendorPhone: string
  des: string
  targetList: Array<string>
}
const loadFormStateModel = {
  id: null,
  loadCode: '',
  loadName: '',
  loadType: '',
  model: '',
  insurance: [],
  insStartTime: '',
  insEndTime: '',
  remind: 0,
  vendor: '',
  vendorContact: '',
  vendorPhone: '',
  des: '',
  targetList: ['']
}
const loadFormState = ref<LoadFormState>(_.cloneDeep(loadFormStateModel))

const loadFormRef = ref()
const loadRules = reactive({
  loadCode: [{ required: true, message: '请输入负载编码' }],
  loadName: [{ required: true, message: '请输入负载名称' }],
  loadType: [{ required: true, message: '请选择负载类型' }],
  model: [{ required: true, message: '请输入型号' }],
  // insurance: [{ required: true, message: '请选择在保时间' }],
  // insStartTime: [{ required: true, message: '请选择开始日期' }],
  // insEndTime: [{ required: true, message: '请选择结束日期' }],
  remind: [{ pattern: regular.isNumber1, message: '请输入数字' }]
  // vendor: [{ required: true, message: '请输入供应商' }],
  // vendorContact: [{ required: true, message: '请输入供应商联系人' }],
  // vendorPhone: [{ required: true, message: '请输入供应商电话' }],
  // des: [{ required: true, message: '请输入描述' }],
  // targetList: [{ required: true, message: '请选择检测目标' }]
})
const insuranceChangeHandle = (dates: [Moment, Moment] | [string, string], dateStrings: [string, string]) => {
  console.log(loadFormState.value.insurance)
  loadFormState.value.insStartTime = dateStrings[0]
  loadFormState.value.insEndTime = dateStrings[1]
}
interface AddEditModalModel {
  visible: boolean
  type: '' | 'add' | 'edit'
}
const addEditModalState = ref<AddEditModalModel>({
  visible: false,
  type: ''
})
const loadTypeChangeHandle = () => {
  console.log(loadFormState.value.loadType)
  if (loadFormState.value.loadType == '1') {
    loadFormState.value.targetList = []
  } else {
    loadFormState.value.targetList = ['']
  }
}
const handleCancle = () => {
  addEditModalState.value.visible = false
  loadFormState.value = _.cloneDeep(loadFormStateModel)
  loadFormRef.value.clearValidate()
}
const handleOk = () => {
  loadFormRef.value
    .validate()
    .then(async () => {
      if (addEditModalState.value.type === 'add') {
        await loadApi.addPayload(loadFormState.value)
        message.success('新增成功')
        searchFormState.loadName = ''
        onSearch()
      } else {
        await loadApi.updatePayload(loadFormState.value)
        message.success('编辑成功')
        queryPayloadPage()
      }
      addEditModalState.value.visible = false
      loadFormState.value = _.cloneDeep(loadFormStateModel)
      loadFormRef.value.clearValidate()
      // console.log(dictConfigFormState.value)
    })
    .catch((error: ValidateErrorEntity<LoadFormState>) => {
      console.log('error', error)
      // addEditModalState.value.visible = false
    })
}

/* ====================== 操作按钮 ====================== */
const addHandle = () => {
  addEditModalState.value.type = 'add'
  addEditModalState.value.visible = true
}
const editHandle = (item: any) => {
  addEditModalState.value.type = 'edit'
  addEditModalState.value.visible = true
  loadFormState.value = {
    id: item.id,
    loadCode: item.loadCode,
    loadName: item.loadName,
    loadType: item.loadType,
    model: item.model,
    insurance: [item.insStartTime, item.insEndTime],
    insStartTime: item.insStartTime,
    insEndTime: item.insEndTime,
    remind: item.remind,
    vendor: item.vendor,
    vendorContact: item.vendorContact,
    vendorPhone: item.vendorPhone,
    des: item.des,
    targetList:
      (item.loadType == 1 ? [] : item.payloadDetectTargetVOList.map(targetItem => targetItem.detectTarget)) ||
      _.cloneDeep(loadFormStateModel.targetList)
  }
}
const delHandle = (item: any) => {
  console.log(item)
  Modal.confirm({
    title: () => '删除提醒',
    icon: () => createVNode(ExclamationCircleOutlined),
    content: () => '确定是否删除该负载？',
    okText: () => '确认',
    okType: 'danger',
    cancelText: () => '取消',
    onOk: async () => {
      try {
        await loadApi.deletePayload(item.id)
        message.success('删除成功')
        if (pagination.value.pageNum > 1 && dataSource.value.length === 1) {
          pagination.value.pageNum = pagination.value.pageNum - 1
        }
        queryPayloadPage()
      } catch (e) {
        console.log(e)
      }
    },
    onCancel() {
      console.log('Cancel')
    }
  })
}
const delTarget = (index: number) => {
  loadFormState.value.targetList.splice(index, 1)
}
const addTarget = () => {
  if (loadFormState.value.targetList.length >= 20) return message.error('最多可添加20个检测目标')
  loadFormState.value.targetList.push('')
}
onMounted(() => {
  queryPayloadPage()
})
</script>
<style lang="scss">
.loadContainer {
  height: 100%;

  .handleWrap {
    height: 54px;
    padding: 10px;
    box-sizing: border-box;

    .left {
      width: 80%;

      .ant-input {
        box-sizing: border-box;
        // width: 240px;
        height: 32px;
      }

      .ant-btn {
        height: 34px;
      }
    }

    .right {
      width: 20%;
      text-align: right;

      .ant-btn {
        height: 34px;
      }
    }
  }

  .contentWrap {
    height: calc(100% - 110px);
    padding: 10px;
    overflow: hidden;

    .ant-card {
      height: 100%;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      border-radius: 6px;
      cursor: default;

      &:hover {
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
      }

      .ant-card-body {
        padding: 15px;
      }

      .cardHead {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #000;

        .headIcon {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          background: #d1cdfc;
          text-align: center;
          min-width: 50px;

          .anticon-slack {
            font-size: 30px;
            color: #200de7;
            width: 30px;
            height: 30px;
            margin-top: 10px;
          }
        }

        .cardTitle {
          font-size: 14px;
          font-weight: 600;
          margin: 0 10px;
        }

        .ant-dropdown-link {
          color: #000;

          &:hover {
            color: #1890ff;
          }
        }
      }

      .cardInfo {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #999;
        margin: 10px 0;

        .anticon {
          margin-right: 4px;
        }
      }

      .cardRemarks {
        border-top: 1px solid #eee;
        font-size: 12px;
        color: #666;
        padding-top: 10px;
      }
    }
  }

  .pageWrap {
    text-align: right;
    margin-top: 20px;
    height: 38px;
    padding-right: 20px;
  }
}

.ant-modal-body {
  padding: 20px 30px 0 20px;
}

.loadHandleForm {
  .ant-input {
    vertical-align: middle;
  }

  .ant-select {
    vertical-align: middle;
  }

  .anticon-environment {
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    color: #666;
    vertical-align: middle;
  }

  .anticon-delete {
    color: red;
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    vertical-align: middle;
  }

  .anticon-plus-circle {
    color: blue;
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    vertical-align: middle;
  }

  .targetItem {
    // margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.mapContainer {
  height: 500px;
}
.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
  height: 34px;
}
.ant-select-single .ant-select-selector .ant-select-selection-item {
  line-height: 32px;
}
</style>
