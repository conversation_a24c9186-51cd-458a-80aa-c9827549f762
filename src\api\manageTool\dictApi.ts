import { getRequest, postRequest } from '../http/request'

export const dictApi = {
  // 分页查询数据字典KEY
  queryDictKey: (param: object) => {
    return postRequest('/support/dict/key/query', param)
  },
  // 分页查询数据字典value
  queryDictValue: (param: object) => {
    return postRequest('/support/dict/value/query', param)
  },
  // 数据字典Value-添加
  addDictValue: (param: object) => {
    return postRequest('/support/dict/value/add', param)
  },
  // 数据字典Value-更新
  editDictValue: (param: object) => {
    return postRequest('/support/dict/value/edit', param)
  },
  // 数据字典Value-删除
  deleteDictValue: (param: object) => {
    return postRequest('/support/dict/value/delete', param)
  },
  // 获取数据字典-值列表
  getDictValueList: (keyCode: string) => {
    return getRequest(`/support/dict/value/list/${keyCode}`, {});
  },
}
