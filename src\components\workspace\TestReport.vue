<template>
  <div class="testReportContainer">
    <div class="reportHead">
      <a-button size="small" type="primary" @click="downloadReport">下载</a-button>
      <CloseOutlined @click="closeReport" />
    </div>
    <div class="reportContentBox custom-scrollbar">
      <div id="reportContent">
          <h3 class="reportTitle">无人载具与作业管理平台<br>检测报告</h3>
        <div class="reportItem baseInfo">
          <h4 class="title">一、基础信息</h4>
          <div class="infoData">
            <a-descriptions bordered :column="2" size="small">
              <a-descriptions-item label="巡检人">{{ testReport.loginName }}({{ testReport.patrolUserName }})</a-descriptions-item>
              <a-descriptions-item label="检测目标">{{ testReport.detectTargetName }}({{ testReport.detectTargetUnit }})</a-descriptions-item>
              <a-descriptions-item label="载具">{{ testReport.vehicleName }}【{{ testReport.vehicleCode }}】</a-descriptions-item>
              <a-descriptions-item label="检测负载">{{ testReport.loadName }}【{{ testReport.loadCode }}】</a-descriptions-item>
              <a-descriptions-item label="开始时间">{{ testReport.jobRecordVO.startTime }}</a-descriptions-item>
              <a-descriptions-item label="结束时间">{{ testReport.jobRecordVO.endTime }}</a-descriptions-item>
              <a-descriptions-item label="总里程">{{ testReport.totalMileage }}m</a-descriptions-item>
              <a-descriptions-item label="总用时">{{ testReport.totalDuration }}s</a-descriptions-item>
              <a-descriptions-item label="风险数">{{ testReport.riskCount }}</a-descriptions-item>
              <a-descriptions-item label="项目">{{ testReport.projectName }}</a-descriptions-item>
            </a-descriptions>
          </div>
        </div>
        <div class="reportItem riskDetails">
          <h4 class="title">二、风险明细</h4>
          <div class="noRisk" v-if="!testReport.alarmDataVOList.length">
            <p>本次检测并未存在风险的内容！</p>
          </div>
          <div class="riskData" v-else>
            <div class="riskItem" v-for="riskItem in testReport.alarmDataVOList" :key="riskItem.id">
              <div class="time">{{ riskItem.triggerTime }}</div>
              <div class="riskType">
                <div class="typeBox" :style="{ backgroundColor: computedRiskBackground(riskItem) }">浓度报警</div>
              </div>
              <div class="riskInfo">
                <p>检测点：{{ riskItem.address }}</p>
                <p>经度：{{ riskItem.lon }}</p>
                <p>纬度：{{ riskItem.lat }}</p>
                <p>最大浓度：{{ riskItem.maxValue + riskItem.detectTargetUnit }}</p>
                <p>风险等级：{{ riskItem.gradeName }}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="reportItem testLine">
          <h4 class="title">三、检测路线</h4>
          <div class="mapContent">
            <ReportCesiumMap :trackData="reportData.baseDataVOList ? reportData.baseDataVOList : []"  :alarmPointsArr="alarmPointArray" :pointsArr="pointArray" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  GroupOutlined,
  CloseOutlined,
  GatewayOutlined
} from '@ant-design/icons-vue';
import ReportCesiumMap from './reportMap/ReportCesiumMap.vue';
import { ref, onMounted } from "vue";

import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { workspaceApi } from '@/api/workspace/workspaceApi'
// 注册组件
defineOptions({
  components: {
    GroupOutlined,
    CloseOutlined,
    GatewayOutlined,
    ReportCesiumMap
  }
})

const props = defineProps({
  testReport: {
    type: Object,
    default: {}
  }
})

const reportData = ref<any>({})

const computedRiskBackground = (riskItem: any) => {
  if(riskItem.maxValue && riskItem.gradeName) {
    return '#FF0000';
  } else if(riskItem.maxValue && !riskItem.gradeName) {
    return '#FFA500';
  } else {
    return '#008000';
  }
}


const downloadReport = () => {
  // 获取你想要转换为PDF的内容的元素
  const element = document.getElementById('reportContent');
  if (!element) return;

  // 获取元素的完整高度
  const scale = 1; // 提高清晰度
  const elementWidth = element.scrollWidth;
  const elementHeight = element.scrollHeight;

    // 创建一个 canvas
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');

  canvas.width = elementWidth * scale;
  canvas.height = elementHeight * scale;
  context?.scale(scale, scale);

  const opts = {
    canvas: canvas,
    scale: scale,
    useCORS: true,
    scrollY: -window.scrollY,
    windowHeight: document.documentElement.scrollHeight,
    windowWidth: document.documentElement.scrollWidth,
    logging: false,
    ignoreElements: (element: any) => {
      // 可选：忽略某些元素
      return false;
    }
  };

  html2canvas(element, opts).then(canvas => {
    const imgData = canvas.toDataURL('image/png');

    const pdf = new jsPDF('p', 'mm', 'a4'); // A4 纸张方向
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = (canvas.height * pdfWidth) / canvas.width;

    let position = 0;

    // 第一页
    pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, pdfHeight);
    position -= pdf.internal.pageSize.getHeight();

    // 分页添加
    while (position > -pdfHeight) {
      pdf.addPage();
      pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, pdfHeight);
      position -= pdf.internal.pageSize.getHeight();
    }

    // 保存文件
    pdf.save(`${reportData.value.projectName}检测报告_${reportData.value.jobRecordVO.startTime}.pdf`);
  });
}

const emit = defineEmits(['closeReport'])
const closeReport = () => {
  emit('closeReport')
}

const pointArray = ref<any>([])
const alarmPointArray = ref<any>([])

const initData = () => {
  reportData.value = Object.assign({}, props.testReport)
  if (reportData.value.airlinePointVOList) {
    const airlinePointVOList = reportData.value.airlinePointVOList.map((item: any) => {
      const itemAlarmData = { ...item.alarmVO }
      delete itemAlarmData.id
      return { ...item, ...itemAlarmData }
    })
    pointArray.value = [...airlinePointVOList]
  } else {
    pointArray.value = []
  }
  alarmPointArray.value = reportData.value.alarmDataVOList ? [...reportData.value.alarmDataVOList] : []
}

onMounted(() => {
  initData()
})

</script>
<style lang="scss">
.testReportContainer{
  width: 100%;
  height: 100%;
  color: #333;
  .reportHead{
    height: 40px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #333;
    border-bottom: 1px solid #eee;

  }

  .reportContentBox{
    height: calc(100% - 40px);
    #reportContent {
      padding: 40px 120px;
    }
    .reportTitle {
      font-size: 24px;
      font-weight: bold;
      text-align: center;
      line-height: 34px;
      margin-bottom: 40px;
    }

    .reportItem {
      .title {
        font-weight: bold;
        font-size: 18px;
        margin: 30px 0;
      }

      .riskData{
        .riskItem{
          padding: 10px;
          margin-bottom: 30px;
          position: relative;
           &::before {
            content: '';
            position: absolute;
            top: 30px;
            left: 215px;
            bottom: -40px;
            width: 1px;
            background: #d1d0d0;
          }

          &:last-child {
            &::before {
              display: none;
            }
          }

          .time {
            display: inline-block;
            width: 160px;
            vertical-align: top;
            padding-top: 10px;
          }
          .riskType {
            display: inline-block;
            position: relative;
            width: 120px;
            vertical-align: top;

            .typeBox {
              width: 90px;
              height: 30px;
              line-height: 30px;
              text-align: center;
              background: #d41717;
              color: #fff;
              border-radius: 2px;
            }
          }
          .riskInfo {
            display: inline-block;
            width: calc(100% - 160px - 120px);
            vertical-align: top;
            p {
              display: inline-block;
              width: 50%;
              line-height: 30px;

              &:first-child {
                width: 100%;
              }
            }
          }
        }
      }

      .mapContent{
        width: 100%;
        height: 600px;
        border: 1px solid #eee;
      }
    }
  }
}
</style>
