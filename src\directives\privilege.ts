/* 权限 */

import store from '@/store'
import _ from 'lodash'

export const privilegeDirective = {
  mounted(el: { parentNode: { removeChild: (arg0: any) => void } }, binding: DirectiveBinding<any>) {
    // 超级管理员
    if (store.getters['user/administratorFlag']) {
      return true
    }
    // 获取功能点权限
    let userPointsList = store.getters['user/pointList']
    if (!userPointsList) {
      return false
    }
    // 如果没有权限，删除节点
    if (!_.some(userPointsList, ['webPerms', binding.value])) {
      el.parentNode.removeChild(el)
    }
    return true
  }
}
