<template>
  <div class="projectInfo projectInfoContainer custom-scrollbar">
    <div class="introduction">
      <h5>项目简介</h5>
      <p v-if="currentProject.des" class="multi-line" :title="currentProject.des">{{ currentProject.des }}</p>
    </div>
    <div class="devList">
      <h5>在线设备</h5>
      <div class="devListItem" v-for="item in projectDevs" :key="item.id">
        <div class="itemTop">
          <div class="itemName">{{ item.vehicleName }}</div>
          <div class="handleType" title="飞行方式">
            <HeatMapOutlined />
            <span>{{ item.flightStatus ? '航线飞行' : '手动飞行'}}</span>
          </div>
          <div class="liveBtn" @click="liveShowOpen(item)"><YoutubeOutlined /></div>
        </div>
        <div class="itemBottom">
          <div class="handlePerson" title="操作人员">
            <InstagramOutlined />
            <span>{{ item.actualName }}</span>
          </div>
          <div class="loadDevs" title="负载情况">
            <div class="loadItem" v-for="(payloadItem, index) in item.payloadNames" :key="index">
              <VideoCameraOutlined v-if="payloadItem" />
              <span>{{ payloadItem }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div :class="['liveWrap', liveFullScreen ? 'liveFullScreenWrap' : '']" v-if="liveShow">
      <div class="liveTop">
        <h6>{{ livingItem.vehicleCode }}</h6>
        <div class="liveTopRight">
          <!-- <SoundOutlined :class="[liveSoundStatus ? 'liveSoundStatusClose' : '']" @click="liveSoundChange" /> -->
          <ExpandOutlined v-if="!liveFullScreen" @click="liveScreenChange" />
          <CompressOutlined v-else @click="liveScreenChange" />
          <CloseOutlined @click="liveClose"/>
        </div>
      </div>
      <div class="devData">
        <!-- <div class="dataItem">
          <span class="itemLabel">RC</span>
          <div class="signals">
            <div class="signal1"></div>
            <div class="signal2"></div>
            <div class="signal3"></div>
            <div class="signal4"></div>
            <div class="signal5"></div>
          </div>
        </div> -->
        <div class="dataItem">
          <span class="itemLabel">GPS</span>
          <span>{{ jobBaseData.rtk || '-' }}</span>
        </div>
        <div class="dataItem">
          <span class="itemLabel">
            <ThunderboltOutlined />
          </span>
          <span>{{ jobBaseData.electricity || '-' }} %</span>
        </div>
        <div class="dataItem">
          <span class="itemLabel">ASL</span>
          <span>{{ currentData.asl || '-' }} m</span>
        </div>
        <div class="dataItem">
          <span class="itemLabel">AGL</span>
          <span> {{ currentData.agl || '-' }} m</span>
        </div>
        <div class="dataItem">
          <span class="itemLabel">
            <ColumnHeightOutlined />
          </span>
          <span>{{ currentData.h || '-' }} m</span>
        </div>
        <!-- <div class="dataItem liveStartTime">
          <span class="itemLabel">直播开始时间：</span>
          <span>{{ workDetails.jobRecordVO ? (workDetails.jobRecordVO.createTime || '') : '' }}</span>
        </div> -->
      </div>
      <div class="living">
        <div class="livingContent">
          <div class="smallText" @click="videoShowWhoChange">{{ videoShowWho === 'camera' ? 'FPV' : 'H30' }}</div>
          <video id="livingVideoPlayer" class="videoDom"></video>
          <div class="centerIconBox">
            <div class="leftTop angle"></div>
            <div class="rightTop angle"></div>
            <div class="leftBottom angle"></div>
            <div class="rightBottom angle"></div>
            <PlusOutlined />
          </div>
          <div class="loadingBox" v-if="livingChangeLoading">
            <div class="loadingIcon">
              <a-spin :spinning="livingChangeLoading"></a-spin>
            </div>
          </div>
        </div>
        
        <!-- 以下切换推流地址方式 -->
        <!-- <div :class="['cameraVideo', videoShowWho === 'camera' ? '' : 'smallVideo']" @click="videoShowWhoChange('camera')">
          <div v-if="videoShowWho === 'dev'" class="smallText">FPV</div>
          <video id="cameraVideoPlayer" class="videoDom"></video>
        </div>
        <div :class="['devLiveVideo', videoShowWho === 'dev' ? '' : 'smallVideo']" @click="videoShowWhoChange('dev')">
          <div v-if="videoShowWho === 'camera'" class="smallText">H30</div>
          <video id="devVideoPlayer" class="videoDom"></video>
        </div> -->
      </div>
      <div class="liveBottom">
        <div class="map">
          <LiveCesiumMap :pointsArr="pointArray" :alarmPointsArr="alarmPointArray" :trackData="airlineTrackPoints" />
        </div>
        <div class="chart">
          <div class="chartTop">
            <div class="valueItem">
              <div class="value">甲烷：{{ currentData.maxValue }}ppm</div>
              <div class="label">当前检测数据</div>
            </div>
            <div class="valueItem">
              <div class="value">{{ currentData.gradeName || '-' }}</div>
              <div class="label">风险等级</div>    
            </div>
            <!-- <div class="valueItem">
              <div class="value">{{ currentData.distance }}m</div>
              <div class="label">检测距离</div>    
            </div> -->
          </div>
          <div class="chartBottom">
            <EChart refName="realTimeDataChart" :options="realTimeDataOption" />
          </div>
        </div>
      </div>
    </div>
    <div class="monitorResult" v-if="monitorResulShow">
      <div class="monitorResultTitle">
        <h5>检测结果</h5>
        <CloseOutlined @click="monitorResultClose"/>
      </div>
      <div class="hasNoData" v-if="!monitorResultData.startTime">该点无检测数据</div>
      <div class="monitorResoultContent custom-scrollbar" v-else>
        <div class="monitorInfo">
          <div class="monitorInfoItem">
            <em>检测点：</em>
            <span>{{ monitorResultData.pointName || '-' }}</span>
          </div>
          <div class="monitorInfoItem addressItem">
            <em>检测地址：</em>
            <span>{{ monitorResultData.baseDataVO ? monitorResultData.baseDataVO.address : '-' }}{{ monitorResultData.baseDataVO ? ('【' + monitorResultData.baseDataVO.lat + '° N') : '' }}   {{ monitorResultData.baseDataVO ? (monitorResultData.baseDataVO.lon + '° E】') : '' }}</span>
          </div>
          <div class="monitorInfoItem">
            <em>开始时间：</em>
            <span>{{ monitorResultData.startTime }}</span>
          </div>
          <div class="monitorInfoItem">
            <em>结束时间：</em>
            <span>{{ monitorResultData.endTime }}</span>
          </div>
          <div class="monitorInfoItem">
            <em>风险等级：</em>
            <span>{{ monitorResultData.gradeName || '-' }}</span>
          </div>
          <div class="monitorInfoItem">
            <em>最大浓度：</em>
            <!-- <span>{{ (monitorResultData.endMaxValue || '-')  + monitorResultData.detectTargetUnit }}</span> -->
            <span>{{ (monitorResultData.baseDataVO ? (monitorResultData.baseDataVO.maxValue + monitorResultData.baseDataVO.detectTargetUnit) : '-') + (monitorResultData.detectTargetUnit || '') }}</span>
            </div>
        </div>
        <div class="monitorData">
          <h6>浓度趋势</h6>
          <div class="chart">
            <EChart refName="monitorResultChart" :options="monitorResultOption" />
          </div>
        </div>
        <div class="monitorImgs">
          <h6>隐患图片</h6>
          <div class="imgs">
            <div class="imgItem" v-for="(item, index) in monitorResultData.iamgeList" :key="index">
              <a-image :src="CURRENT_CONFIG.baseURL + item.imagePath" />
              </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  HeatMapOutlined,
  YoutubeOutlined,
  InstagramOutlined,
  VideoCameraOutlined,
  VideoCameraAddOutlined,
  ExpandOutlined,
  SoundOutlined,
  CloseOutlined,
  ThunderboltOutlined,
  ColumnHeightOutlined,
  CompressOutlined,
  PlusOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue'
import { ref, reactive, watch, onMounted, computed, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router'
import { workspaceApi } from '@/api/workspace/workspaceApi'
import EChart from '@/components/common/EChart.vue';
import LiveCesiumMap from '@/components/workspace/liveMap/LiveCesiumMap.vue';
// import SSEManager from '@/api/eventSource/eventSource';
import { SSEManager } from '@/api/eventSource/eventSourceClass';
import flvjs from 'flv.js';
import { useStore } from 'vuex'
import { liveViewer } from '@/components/workspace/liveMap/initLiveCesium';
import { useLiveCesium } from '@/components/workspace/liveMap/useLiveCesium'
import { CURRENT_CONFIG } from '@/api/http/config'

const { updatePosition, updateTrack, clearMap } = useLiveCesium();

// 注册组件
defineOptions({
  components: {
    HeatMapOutlined,
    YoutubeOutlined,
    InstagramOutlined,
    VideoCameraOutlined,
    VideoCameraAddOutlined,
    ExpandOutlined,
    SoundOutlined,
    CloseOutlined,
    ThunderboltOutlined,
    ColumnHeightOutlined,
    CompressOutlined,
    EChart,
    LiveCesiumMap
  }
})

interface PROJECT {
  address: string,
  airlineMarkEntityList: any,
  chargeUserName: any,
  createTime: string,
  deletedFlag: number,
  des: string,
  id: number,
  lat: any,
  lon: any,
  mediaStorage: any,
  name: string,
  updateTime: string,
  userId: number,
  vehicleTypes: string[]
}

const store = useStore()

// 使用 defineProps 接收数据，并指定类型和默认值
const props = defineProps<{
  currentProject: PROJECT;
}>();

const emit = defineEmits(['mapDataChange'])

watch(() => props.currentProject, (newValue, oldValue) => {

});

const router = useRouter()

let projectDevs = ref<any>([])
const getProjectDevs = async() => {
  // 获取路由参数中的 id
  const projectIdRaw = router.currentRoute.value.query.id;

  // 确保 projectId 是字符串类型，若无效则使用默认值或抛出错误
  let projectId: string;
  if (Array.isArray(projectIdRaw)) {
    // 如果是数组，取第一个元素并转换为字符串
    projectId = (projectIdRaw[0] || '').toString();
  } else if (typeof projectIdRaw === 'string' || typeof projectIdRaw === 'number') {
    // 如果是字符串或数字，直接转换为字符串
    projectId = projectIdRaw.toString();
  } else {
    // 如果是 null 或 undefined，使用默认值或抛出错误
    console.error('项目ID无效，请检查路由参数');
    return; // 或者可以设置一个默认值，例如：projectId = 'defaultId';
  }

  try {
    const result = await workspaceApi.getProjectDevs(projectId)
    projectDevs.value = result.data ? [...result.data] : []
    const projectDevsArray: any[] = [... projectDevs.value]
    if(!projectDevs.value.length) {
      liveShow.value = false
    }
    emit('mapDataChange', 'projectInfo', projectDevsArray)
  } catch (e) {
    projectDevs.value = []
  } finally {
  }
}

const workDetails = ref<any>({})
const baseDataList = ref<any>([])
const airlineTrackPoints = ref<any>([])
const pointArray = ref<any>([])  // 航点
const alarmPointArray =  ref<any>([])  //自主检测点

const liveShow = ref(false)
const livingItem = ref<any>({})
const realTimeDataOption = ref<any>({})

const detectionDataArr = ref<any>([])
const detectionTimeArr = ref<any>([])
for (let i = 300; i >= 0; i--) {
  detectionTimeArr.value.push(i + 's')
}
const liveShowOpen = (item: any) => {
  liveShow.value = true
  livingItem.value = { ...item }
  getDevWorkInfo(item)
  initSSE(item)
}

const baseDataTimer: any = ref(null)
const getDevWorkInfo = async (item: any) => {
  try {
    const result = await workspaceApi.getWorkRecordDetail({
      projectId: item.projectId,
      jobId: item.id
    })
    workDetails.value = { ...result.data }
    // 检测点
    baseDataList.value = result.data.baseDataVOList ? [...result.data.baseDataVOList] : []
    pointArray.value = result.data.airlinePointVOList ? [...result.data.airlinePointVOList] : []
    alarmPointArray.value =  result.data.alarmDataVOList || []
    const baseDataVOList = result.data.baseDataVOList ? [...result.data.baseDataVOList] : []
    if (baseDataVOList.length > 0) {
      updatePosition(baseDataVOList[baseDataVOList.length - 1])
      // 已行驶轨迹
      if (airlineTrackPoints.value.length > 0) {
        airlineTrackPoints.value = [...baseDataVOList, ...airlineTrackPoints.value]
        updateTrack(airlineTrackPoints.value, true)
      } else {
        airlineTrackPoints.value = baseDataVOList
        updateTrack(airlineTrackPoints.value, true)
      }
    }
    // 画折线图
    baseDataVOList.forEach((baseDataItem, index) => {
      if((index + 300) >= baseDataVOList.length) {
        detectionDataArr.value.push(baseDataItem.maxValue)
      }
    })
    // 已有数据少于301时补全折线图值的长度
    const noDataLength = 301 - detectionDataArr.value.length
    if (detectionDataArr.value.length < 301) {
      for (let i = 0; i < noDataLength; i++) {
        detectionDataArr.value.unshift('')
      }
    }
    updateEchartsData(detectionDataArr.value, detectionTimeArr.value)
    queryJobBaseData(item.id)

    baseDataTimer.value = setInterval(() => {
      queryJobBaseData(item.id)
      queryWorkRecord(item)
    }, 2000);
    setTimeout(() => {
      // 切换推流地址方式
      // createCameraFlvPlayer(workDetails.value.jobRecordVO.ingestUrl)
      // 不切换推流地址方式
      createLivingFlvPlayer(workDetails.value.jobRecordVO.ingestUrl)
    }, 500);
  } catch (e) {
    workDetails.value = {}
  } finally {
  }
}

// 查询已完成部分工作的数据
const queryWorkRecord = async (item: any) => {
  try {
    const result = await workspaceApi.getWorkRecordDetail({
      projectId: item.projectId,
      jobId: item.id
    })
    if(baseDataList.value.length === result.data.baseDataVOList.length) {
      clearInterval(baseDataTimer.value)
      return
    }
    baseDataList.value = result.data.baseDataVOList ? [...result.data.baseDataVOList] : []
    pointArray.value = result.data.airlinePointVOList ? [...result.data.airlinePointVOList] : []
    alarmPointArray.value = result.data.alarmDataVOList || []
  } catch (e) {
    pointArray.value = []
    alarmPointArray.value = []
  } finally {
  }
}

// 添加样本点
const addMapFiyPoint = (pointData: any) => {
  // 延迟执行直到 liveViewer 存在
  if (!liveViewer) {
    console.warn("Viewer 尚未初始化，延迟加载数据源...");
    setTimeout(() => addMapFiyPoint(pointData), 100); // 递归等待
    return;
  }
  // 已行驶轨迹
  airlineTrackPoints.value.push(pointData)
  if(airlineTrackPoints.value.length > 2) { // 当大于两个点时才能开始画线
    updateTrack(airlineTrackPoints.value, false)
  }
  // addSamplePoint(pointData)
  updatePosition(pointData)
};

// 更新echarts数据
const updateEchartsData = (yAxis: any, xAxis: any) => {
  realTimeDataOption.value = Object.assign({}, {
    grid: {
      left: '6%',
      right: '10%',
      bottom: '18%',
      top: '10%'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxis,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true, // 显示网格线
        lineStyle: {
          color: '#aaa', // 网格线颜色
        }
      },
      axisLabel: {
        show: true,
        color: '#aaa' // 标签文字颜色
      }
    },
    yAxis: {
      type: 'value',
      position: 'right',
      axisLine: {
        show: false,
        lineStyle: {
          color: '#aaa'
        }
      },
      splitLine: {
        show: true, // 显示网格线
        lineStyle: {
          color: '#aaa', // 网格线颜色
        }
      }
    },
    series: [{
      data: yAxis,
      smooth: 0.6,
      type: 'line',
      itemStyle: {
        color: '#088e97'
      },
      areaStyle: {}
    }]
  })
}

const jobBaseData = ref<any>({})
const queryJobBaseData = async (jobId: any) => {
  try {
    const result = await workspaceApi.getWorkInfo(jobId)
    jobBaseData.value = result.data ? { ...result.data } : {}
    
  } catch (e: any) {
    if(e.data.code === 30007) {
      clearInterval(baseDataTimer.value)
    }
    jobBaseData.value = {}
  } finally {
  }
}

const livingflvPlayer =  ref<flvjs.Player | null>(null);
const createLivingFlvPlayer = (streamUrl: string) => {
  console.log(livingflvPlayer.value);
  if (livingflvPlayer.value) {
    livingflvPlayer.value.destroy(); // 销毁之前的播放器实例
  }
  const videoPlayer = document.getElementById('livingVideoPlayer') as HTMLVideoElement;
  if (flvjs.isSupported() && videoPlayer) {
    livingflvPlayer.value = flvjs.createPlayer({
      type: 'flv',
      isLive: true,
      url: streamUrl,
    });
    console.log(livingflvPlayer.value);
    livingflvPlayer.value.attachMediaElement(videoPlayer);
    livingflvPlayer.value.load();
    livingflvPlayer.value.play();
    // 监听错误事件
    livingflvPlayer.value.on(flvjs.Events.ERROR, (errType, errDetails) => {
      console.log('flvjs 错误:', errType, errDetails);
      if (livingflvPlayer.value) {
        livingflvPlayer.value.destroy();
        livingflvPlayer.value = null
      }
    });
  }
};

const cameraflvPlayer = ref<flvjs.Player | null>(null);
const createCameraFlvPlayer = (streamUrl: string) => {
  console.log(cameraflvPlayer.value);
  if (cameraflvPlayer.value) {
    cameraflvPlayer.value.destroy(); // 销毁之前的播放器实例
  }
  const videoPlayer = document.getElementById('cameraVideoPlayer') as HTMLVideoElement;
  if (flvjs.isSupported() && videoPlayer) {
    cameraflvPlayer.value = flvjs.createPlayer({
      type: 'flv',
      isLive: true,
      url: streamUrl,
    });
    console.log(cameraflvPlayer.value);
    cameraflvPlayer.value.attachMediaElement(videoPlayer);
    cameraflvPlayer.value.load();
    cameraflvPlayer.value.play();
    // 监听错误事件
    cameraflvPlayer.value.on(flvjs.Events.ERROR, (errType, errDetails) => {
      console.log('flvjs 错误:', errType, errDetails);
      if (cameraflvPlayer.value) {
        cameraflvPlayer.value.destroy();
        cameraflvPlayer.value = null
      }
    });
  }
};

const devflvPlayer = ref<flvjs.Player | null>(null);
const creatDevFlvPlayer = (streamUrl: string) => {
  if (devflvPlayer.value) {
    devflvPlayer.value.destroy(); // 销毁之前的播放器实例
  }
  const videoPlayer = document.getElementById('cameraVideoPlayer') as HTMLVideoElement;
  if (flvjs.isSupported() && videoPlayer) {
    devflvPlayer.value = flvjs.createPlayer({
      type: 'flv',
      isLive: true,
      url: streamUrl,
    });
    console.log(devflvPlayer.value);
    devflvPlayer.value.attachMediaElement(videoPlayer);
    devflvPlayer.value.load();
    devflvPlayer.value.play();
    // 监听错误事件
    devflvPlayer.value.on(flvjs.Events.ERROR, (errType, errDetails) => {
      console.log('flvjs 错误:', errType, errDetails);
      if (devflvPlayer.value) {
        devflvPlayer.value.destroy();
        devflvPlayer.value = null
      }
    });
  }
};

const videoShowWho = ref<string>('camera')
const livingChangeLoading = ref(false)

const videoShowWhoChange = async () => {
  livingChangeLoading.value = true
  try {
    const result: any = await workspaceApi.changeLiving({
      jobId: livingItem.value.id,
      type: videoShowWho.value === 'camera' ? 1 : 2
    })
    console.log(result);
    if(result.ok) {
      videoShowWho.value = videoShowWho.value === 'camera' ? 'dev' : 'camera'
    }
  } catch (e: any) {
    // message.error('切换')
    livingChangeLoading.value = false
  } finally {
    livingChangeLoading.value = false
  }
}

// 切换推流地址方式
// const videoShowWhoChange = (val: string) => {
//   if (videoShowWho.value === val) return
//   videoShowWho.value = videoShowWho.value === 'camera' ? 'dev' : 'camera'
//   setTimeout(() => {
//     if (videoShowWho.value === 'dev') {
//       console.log(cameraflvPlayer.value);
//       if (cameraflvPlayer.value) {
//         cameraflvPlayer.value.destroy();
//         cameraflvPlayer.value = null
//       }
//       creatDevFlvPlayer(workDetails.value.jobRecordVO.fpvIngestUrl)
//     } else {
//       console.log(devflvPlayer.value);
//       if (devflvPlayer.value) {
//         devflvPlayer.value.destroy();
//         devflvPlayer.value = null
//       }
//       createCameraFlvPlayer(workDetails.value.jobRecordVO.ingestUrl)
//     }
//   }, 500)
// }

const sseInfo = ref<any>({})
const sseManager: any = ref(null);

const initSSE = (item: any) => {
  sseManager.value = new SSEManager(`/createSse?jobId=${item.id}&type=0`);
  // 添加监听器
  sseManager.value.addListener('open', handleOpen);
  sseManager.value.addListener('message', handleMessage);
  sseManager.value.addListener('uid', handleUid);
  sseManager.value.connect();
}

const sseUid = ref('')

const liveClose = () => {
  clearMap()
  livingItem.value = {}
  workDetails.value = {}
  airlineTrackPoints.value = []
  baseDataList.value = []
  pointArray.value = []
  alarmPointArray.value = []
  detectionDataArr.value = []
  sseInfo.value = {}
  if(baseDataTimer.value) {
    clearInterval(baseDataTimer.value)
  }
  if (sseManager.value) {
    closeSSE()
  }

  // 不切换推流地址方式
  if (livingflvPlayer.value) {
    try {
      livingflvPlayer.value.destroy();
      livingflvPlayer.value = null;
    } catch (error) {
      console.log('摄像头视频关闭失败', error);
    }
  }

  // 一下为切换推流地址方式
  // if (cameraflvPlayer.value) {
  //   try {
  //     cameraflvPlayer.value.destroy();
  //     cameraflvPlayer.value = null;
  //   } catch (error) {
  //     console.log('摄像头视频关闭失败', error);
  //   }
  // }
  // if (devflvPlayer.value) {
  //   try {
  //     devflvPlayer.value.destroy();
  //     devflvPlayer.value = null;
  //   } catch (error) {
  //     console.log('设备视频关闭失败', error);
  //   }
  // }

  // 关闭直播窗口
  if (liveShow.value) {
    liveShow.value = false
  }
}

const currentData = ref<any>({})
const handleOpen = () => {
  console.log('EventSource opened');
}

const handleUid = (data: any) => {
  console.log('Received uid:', data);
  if(!sseUid.value) {
    sseUid.value = data
  }
}

const handleMessage = (data: any) => {
  // 处理消息
  currentData.value = {...data}
  // 更新地图飞机点位
  addMapFiyPoint(currentData.value)
  // 折线图添加数据
  detectionDataArr.value.push(currentData.value.maxValue)
  // detectionTimeArr.value.push(currentData.value.triggerTime)
  if(detectionDataArr.value.length > 301){
    detectionDataArr.value.shift()
    // detectionTimeArr.value.shift()
  }
  updateEchartsData(detectionDataArr.value, detectionTimeArr.value)
}

const handleCustomEvent = (data: any) => {
  console.log('Received custom event:', data);
  // 处理自定义事件
}

const closeSSE = () => {
  sseManager.value.removeListener('open', handleOpen);
  sseManager.value.removeListener('uid', handleUid);
  sseManager.value.removeListener('message', handleMessage);
  sseManager.value.close();
  console.log(sseUid.value);
  if(!sseUid.value) return
  workspaceApi.closeSSE({
    uid: sseUid.value
  }).then((res) => {
    if (res.data.ok) {
      console.log('closeSSE success')
    } else {
      console.log('closeSSE error')
    }
  }).catch(() => {
    console.log('closeSSE error')
  })
}

const liveSoundStatus = ref(true)
const liveSoundChange = () => {
  liveSoundStatus.value = !liveSoundStatus.value
}

const liveFullScreen = ref(false)
const liveScreenChange = () => {
  liveFullScreen.value = !liveFullScreen.value
  message.info('若要退出全屏模式，请按Esc键');
  addEscListener()
}

const addEscListener = () => {
  document.addEventListener('keydown', (event) => {
    if (event.key === 'Escape') {
      liveFullScreen.value = false
      removeEscListener()
    }
  })
}

const removeEscListener = () => {
  document.removeEventListener('keydown', (event) => {
    if (event.key === 'Escape') {
      liveFullScreen.value = false
    }
  })
}

const monitorResulShow = ref(false)
const monitorResultData = ref<any>({})
const monitorResultOption = ref<any>({})

const selectMapPointData = computed(() => {
  return store.state.workspace.clickPointData
})

watch(() => selectMapPointData, (newValue, oldValue) => {
  queryMonitorResult(newValue.value)
}, { deep: true })

const hasField = (obj: any, field: string) => {
  return Object.prototype.hasOwnProperty.call(obj, field);
}
const startIndex = ref(0)
const endIndex = ref(0)
const queryMonitorResult = async (item: any) => {
  console.log(item);

  const xAxisData: any[] = []
  const yAxisData: any[] = []
  console.log(hasField(item, 'alarmVO'));

  if (hasField(item, 'alarmVO')) {
    try {
      const result = await workspaceApi.getPointDetetionData({
        pointId: item.id,
        baseDataId: item.alarmVO ? item.alarmVO.id : null,
        jobId: item.alarmVO ? null : item.jobId
      })
      monitorResultData.value = { ...result.data }
      if (result.data.jobBaseDataVOList) {
        result.data.jobBaseDataVOList.forEach((item: any, index: number) => {
          xAxisData.push(item.triggerTime.split(' ')[1])
          yAxisData.push(item.maxValue)
          if (item.currData && !startIndex.value) {
            startIndex.value = index
          }
          if (!item.currData && startIndex.value && !endIndex.value && (index > startIndex.value)) {
            endIndex.value = index - 1
          }
        })
      }
      monitorResultOpen(xAxisData, yAxisData)
    } catch (e) {
      console.log(e);
    } finally {
    }
  } else {
    try {
      const result = await workspaceApi.getPointDetetionData({
        baseDataId: item.id
      })
      monitorResultData.value = { ...result.data }
      if (result.data.jobBaseDataVOList) {
        result.data.jobBaseDataVOList.forEach((item: any, index: number) => {
          xAxisData.push(item.triggerTime.split(' ')[1])
          yAxisData.push(item.maxValue)
          if (item.currData && !startIndex.value) {
            startIndex.value = index
          }
          if (!item.currData && startIndex.value && !endIndex.value && (index > startIndex.value)) {
            endIndex.value = index - 1
          }
        })
      }
      monitorResultOpen(xAxisData, yAxisData)
    } catch (e) {
      console.log(e);
    } finally {
    }
  }
}

const monitorResultOpen = (xAxisData: any, yAxisData: any) => {
  monitorResulShow.value = true

  if (xAxisData && yAxisData) {
    // 分割数据为三部分
    const part1 = { x: xAxisData.slice(0, startIndex.value), y: yAxisData.slice(0, startIndex.value) }
    const part2 = { x: xAxisData.slice(startIndex.value, endIndex.value + 1), y: yAxisData.slice(startIndex.value, endIndex.value + 1) }
    const part3 = { x: xAxisData.slice(endIndex.value), y: yAxisData.slice(endIndex.value) }
    console.log(part1.x.length, part3.x.length);
    setTimeout(() => {
      // part2 只有一个点时
      if (part2.x.length === 1) {
        console.log(1111111);
        const specialPoint = part2;
        part1.x.push(specialPoint.x[0])
        part1.y.push(specialPoint.y[0])
        console.log(part1.x, part3.x);
        monitorResultOption.value = Object.assign({}, {
          grid: {
            left: '12%',
            right: '12%',
            bottom: '12%',
            top: '10%'
          },
          tooltip: {
            trigger: 'axis', // 关键配置
            formatter: function (params: any) {
              // 确保我们只显示当前序列的提示信息
              let param = params[0] // 默认选择第一个参数作为基础
              return `时间：${param.value[0]}<br/>浓度：${param.value[1]}`
            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xAxisData,
            axisLine: {
              lineStyle: {
                color: '#fff'
              }
            },
          },
          yAxis: {
            type: 'value',
            axisLine: {
              lineStyle: {
                color: '#fff'
              }
            }
          },
          series: [{
            smooth: 0.6,
            type: 'line',
            data: part1.x.map((_: any, index: any) => ({ value: [part1.x[index], part1.y[index]] })),
            lineStyle: {
              color: '#2856ee'
            },
            symbol: 'none' // 隐藏数据点符号
          }, {
            smooth: 0.6,
            type: 'scatter',
            data: part2.x.map((_: any, index: any) => ({ value: [part2.x[index], part2.y[index]] })),
            symbol: 'circle', // 在数据点处显示圆形
            symbolSize: 10, // 圆形大小
            itemStyle: {
              color: '#CE1A0D' // 数据点颜色，这里设置为红色
            }
          }, {
            smooth: 0.6,
            type: 'line',
            data: part3.x.map((_: any, index: any) => ({ value: [part3.x[index], part3.y[index]] })),
            lineStyle: {
              color: '#2856ee'
            },
            symbol: 'none' // 隐藏数据点符号
          }],
        })
      } else {
        console.log(222222);
        monitorResultOption.value = Object.assign({}, {
          grid: {
            left: '12%',
            right: '12%',
            bottom: '12%',
            top: '10%'
          },
          tooltip: {
            trigger: 'axis', // 关键配置
            formatter: function (params: any) {
              // 确保我们只显示当前序列的提示信息
              let param = params[0] // 默认选择第一个参数作为基础
              return `时间：${param.value[0]}<br/>浓度：${param.value[1]}`
            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xAxisData,
            axisLine: {
              lineStyle: {
                color: '#fff'
              }
            },
          },
          yAxis: {
            type: 'value',
            axisLine: {
              lineStyle: {
                color: '#fff'
              }
            }
          },
          series: [{
            smooth: 0.6,
            type: 'line',
            data: part1.x.map((_: any, index: any) => ({ value: [part1.x[index], part1.y[index]] })),
            lineStyle: {
              color: '#2856ee'
            },
            symbol: 'none' // 隐藏数据点符号
          }, {
            smooth: 0.6,
            type: 'line',
            data: part2.x.map((_: any, index: any) => ({ value: [part2.x[index], part2.y[index]] })),
            lineStyle: {
              color: '#CE1A0D'
            },
            symbol: 'none' // 隐藏数据点符号
          }, {
            smooth: 0.6,
            type: 'line',
            data: part3.x.map((_: any, index: any) => ({ value: [part3.x[index], part3.y[index]] })),
            lineStyle: {
              color: '#2856ee'
            },
            symbol: 'none' // 隐藏数据点符号
          }]
        })
      }
    }, 200)
  }
}
const monitorResultClose = () => {
  monitorResulShow.value = false
  monitorResultOption.value = {}
}

let queryDevTimer: any = ref(null)

onMounted(() => {
  getProjectDevs()
  queryDevTimer.value = setInterval(() => {
    getProjectDevs()
  }, 2000)
})

onBeforeUnmount(() => {
  if(liveShow.value) {
    liveClose()
  }
  if(queryDevTimer.value) {
    clearInterval(queryDevTimer.value)
  }
  monitorResultClose()
})

</script>
<style lang="scss" scoped>
.projectInfoContainer{
  height: 100%;
  background: none;
  h5 {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    margin-bottom: 15px;
  }

  .signals{
    display: flex;
    align-items: end;
    div {
      background: #fff;
      width: 4px;
      margin-right: 2px;
      border-radius: 2px;
    }
    .signal1 {
      height: 20%;
    }
    .signal2 {
      height: 40%;
    }
    .signal3 {
      height: 60%;
    }
    .signal4 {
      height: 80%;
    }
    .signal5 {
      height: 100%;
    }
  }

  .introduction{
    padding: 20px 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);

    h5 {
      margin-bottom: 0;
      // font-size: 18px;
    }

    p {
      margin-top: 15px;
      line-height: 20px;
    }
  }
  .devList{
    padding: 20px 10px;
    .devListItem{
      background: rgba(255, 255, 255, 0.2);
      margin-bottom: 15px;

      .itemTop{
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        padding: 6px 35px 6px 6px;
        position: relative;
        .itemName {
          line-height: 24px;
          margin-bottom: 6px;
        }

        .handleType{
          padding: 6px;
          background: rgba(255, 255, 255, 0.2);
          display: flex;
          align-items: center;
          .anticon {
            line-height: 0;
            font-size: 16px;
            margin-right: 10px;
          }
        }

        .liveBtn{
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          width: 30px;
          background: rgba(255, 255, 255, 0.2);
          font-size: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
      }

      .itemBottom {
        padding: 6px;
        .handlePerson {
          padding: 6px;
          background: rgba(255, 255, 255, 0.2);
          display: flex;
          align-items: center;
          .anticon {
            line-height: 0;
            font-size: 16px;
            margin-right: 10px;
          }
        }

        .loadDevs {
          display: flex;
          align-items: center;
          padding: 6px;
          background: rgba(255, 255, 255, 0.2);
          margin-top: 6px;
          .loadItem {
            display: flex;
            align-items: center;
            width: 50%;
            .anticon {
              line-height: 0;
              font-size: 16px;
              margin-right: 10px;
            }
          }
        }
      }

    }
  }

  .liveWrap{
    position: fixed;
    top: 50px;
    left: 400px;
    bottom: 30px;
    width: 900px;
    background: rgba(0,0,0,0.7);

    &.liveFullScreenWrap {
      position: fixed;
      left: 0;
      width: 100vw;
      top: 0;
      height: 100vh;
      background: #666;
      .liveTop{
        display: none;
      }
      .devData{
        display: none;
      }

      .living {
        height: 100%;
      }

      .liveBottom{
        display: none;
      }
    }
    
    .liveTop {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 10px;
      border-bottom: 1px solid rgba(255,255,255,0.1);
      h6{
        margin: 0;
        color: #fff;
        font-size: 14px;
        font-weight: bold;
      }
      .liveTopRight {
        .anticon {
          line-height: 0;
          margin-left: 20px;
          font-size: 18px;
          cursor: pointer;
        }

        .liveSoundStatusClose{
          position: relative;
        }

        .liveSoundStatusClose::after {
          content: '';
          position: absolute;
          top: -1px;
          left: 6px;
          height: 120%;
          width: 2px;
          background: #fff;
          transform: rotate(-50deg);
          pointer-events: none; /* 确保斜线不会干扰点击事件 */
        }
      }
    }

    .devData {
      display: flex;
      flex-wrap: wrap; /* 允许子元素换行 */
      align-items: center;
      padding: 0px 20px 10px;
      height: 72px;
      .dataItem {
        width: calc(100% / 5);
        display: flex;
        align-items: center;
        margin-top: 15px;
        .itemLabel{
          display: block;
          width: 40px;
        }
        .signals{
          height: 16px;
        }
      }

      .liveStartTime{
        width: 100%;
        .itemLabel {
          width: 100px;
        }
      }
    }

    .living {
      // padding: 6px 0;
      height: calc(100% - 312px);
      background: rgba(0,0,0,0.5);
      position: relative;

      .livingContent{
        height: 100%;
        width: 100%;

        .videoDom{
          width: 100%;
          height: 100%;
        }
        .smallText{
          position: absolute;
          right: 10px;
          bottom: 10px;
          width: 90px;
          height: 60px;
          font-size: 30px;
          text-align: center;
          line-height: 60px;
          background: #2e2d2d;
          border: 1px solid rgba(255,255,255,0.2);
          cursor: pointer;
          z-index: 10;
        }

        .centerIconBox{
          .angle {
            position: absolute;
            width: 30px;
            height: 30px;
            border: 2px solid #eee;
            top: 50%;
            left: 50%;
          }

          .leftTop{
            transform: translate(-230px, -100px);
            border-right: none;
            border-bottom: none;
          }

          .rightTop{
            transform: translate(200px, -100px);
            border-left: none;
            border-bottom: none;
          }

          .leftBottom{
            transform: translate(-230px, 70px);
            border-right: none;
            border-top: none;
          }

          .rightBottom{
            transform: translate(200px, 70px);
            border-left: none;
            border-top: none;
          }
          

          .anticon-plus{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 40px;
            color: #eee;
          }
        }

        .loadingBox {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 20;
          background: rgba(0, 0, 0, 0.6);
          .loadingIcon{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }

      .cameraVideo, .devVideo {
        width: 100%;
        height: 100%;
        .videoDom{
          width: 100%;
          height: 100%;
        }
      }
      .smallVideo {
        position: absolute;
        right: 6px;
        bottom: 6px;
        width: 120px;
        height: 80px;
        border: 1px solid rgba(255,255,255,0.2);
        cursor: pointer;
        background: #2e2d2d;
        .videoDom{
          width: 100%;
          height: 100%;
        }
        .smallText{
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%,-50%);
          font-size: 30px;
        }
      }
    }

    .liveBottom{
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px;
      height: 200px;
      .map, .chart {
        width: 50%;
        border: 1px solid rgba(255,255,255,0.2);
        height: 180px;
      }

      .chart {
        padding: 10px 10px 0;
        .chartTop{
          padding: 0 10px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 40px;
          .valueItem{
            line-height: 20px;
            text-align: center;
            .value {
              font-weight: bold;
            }
            .label {
              font-size: 12px;
              color: #e0dede;
            }
          }
        }
        .chartBottom{
          height: calc(100% - 50px);
        }
      }
    }
  }
  .monitorResult{
    position: fixed;
    right: 0;
    bottom: 30px;
    top: 50px;
    width: 600px;
    padding: 10px 20px;
    background: #2e2d2d;
    color: #fff;
    .monitorResultTitle{
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 30px;
      padding-bottom: 10px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      h5 {
        color: #fff;
        margin: 0;
      }
    }

    .hasNoData{
      margin-top: 50px;
      text-align: center;
      font-size: 16px;
    }

    .monitorResoultContent{
      height: calc(100% - 40px);
    }
    .monitorInfo{
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-top: 10px;
      .monitorInfoItem {
        width: 50%;
        margin-bottom: 6px;
        line-height: 20px;
        em {
          vertical-align: middle;
          display: inline-block;
          width: 80px;
        }
        span {
          vertical-align: middle;
          display: inline-block;
          max-width: calc(100% - 80px);
        }
      }
      .addressItem {
        width: 100%;
      }
    }

    .monitorData{
      margin-top: 20px;
      h6 {
        color: #fff;
        font-weight: bold;
        margin-bottom: 10px;
      }
      .chart {
        border: 1px solid rgba(255, 255, 255, 0.2);
        height: 250px;
      }
    }

    .monitorImgs{
      margin-top: 20px;
      h6 {
        color: #fff;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .imgs {
        display: flex;
        gap: 10px;
        .imgItem{
          width: calc(100% / 6);
          cursor: pointer;
        }
      }
    }
  }
}
</style>
