import { onMounted } from "vue";
import * as Cesium from "cesium";
import "cesium/Build/Cesium/Widgets/widgets.css";
export let viewer: any;
export let handler: any;
export function initCesium() {
  onMounted(() => {
    // Cesium.Ion.defaultAccessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.tdsHRJeCZr2AwNemO7Q1TE52VXbwvBXH5OmW2SL1m6c";
    viewer = new Cesium.Viewer("cesiumContainer", {
      animation: false,
      baseLayerPicker: false,
      fullscreenButton: false,
      geocoder: false,
      homeButton: false,
      infoBox: false,
      sceneModePicker: false,
      selectionIndicator: false,
      timeline: false,
      navigationHelpButton: false,
      // sceneMode: Cesium.SceneMode.SCENE2D, // 设置场景模式为二维
      terrainProvider: new Cesium.EllipsoidTerrainProvider(), // 禁用默认地形
      skyBox: false, // 禁用天空盒
      shouldAnimate: true,
      // 实现canvas缓存获得canvas图像内容
      contextOptions: {
        webgl: { preserveDrawingBuffer: true },
      },
    });

    // 移除默认添加的图层
    viewer.imageryLayers.removeAll();

    // 隐藏版权信息（使用官方推荐方式）
    const creditContainer = viewer.cesiumWidget.creditContainer as
      | HTMLDivElement
      | undefined;
    if (creditContainer) {
      creditContainer.style.display = "none";
    }

    // 禁用默认的地形
    viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider();

    handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    addLayer();

    // 初始化视角飞到那个点位
    // viewer.camera.flyTo({
    //   destination: Cesium.Cartesian3.fromDegrees(108.09876, 37.200787, 1400000),
    //   duration: 1,
    // });
    // 设置初始视角到郑州市
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(
        113.5610851405145,
        34.8230213780326,
        5000
      ),
      orientation: {
        heading: Cesium.Math.toRadians(0.0),
        pitch: Cesium.Math.toRadians(-90.0),
        roll: 0.0,
      },
    });

    // 可选：监听 WebGL 上下文丢失等关键错误
    viewer.scene.canvas.addEventListener("webglcontextlost", (event: any) => {
      event.preventDefault();
      console.error("WebGL 上下文丢失");
    });
  });
  const addLayer = () => {
    // 创建一个Cesium的UrlTemplateImageryProvider实例，用于显示高德地图的影像
    const gaodeMapImageryProvider = new Cesium.UrlTemplateImageryProvider({
      // 定义地图瓦片的URL模板
      // url: 'https://webst0{s}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}', // 高德卫星影像
      // url: 'https://webst0{s}.is.autonavi.com/appmaptile?style=8&x={x}&y={y}&z={z}', // 高德路网注记
      url: "http://webst0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=2&style=7&x={x}&y={y}&z={z}", // 高德矢量底图
      // 设置地图数据的版权信息
      credit: "Gaode Maps",
      // 定义地图覆盖的矩形区域，使用弧度表示
      rectangle: new Cesium.Rectangle(
        Cesium.Math.toRadians(73.63),
        Cesium.Math.toRadians(-5.06),
        Cesium.Math.toRadians(135.78),
        Cesium.Math.toRadians(53.56)
      ),
      // 设置地图的最大级别
      maximumLevel: 18,
      // 设置地图的最小级别
      minimumLevel: 0,
      // 子域名数组，用于负载均衡
      subdomains: ["1", "2", "3", "4"],
    });

    const imageryLayer = viewer.imageryLayers.addImageryProvider(
      gaodeMapImageryProvider
    );
    imageryLayer.alpha = 1.0; // 设置透明度为 1.0，即完全不透明
  };
}
