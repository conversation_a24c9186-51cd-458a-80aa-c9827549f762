<!-- 弹框-->
<template>
  <div class="mapPop" v-if="popupData">
    <div class="popHeader">
      <span>{{ popupData?.name || '测试' }}</span>
      <!-- <CloseOutlined @click="close" /> -->
    </div>
    <div class="popContent">
      <p>项目地址：{{ popupData.address }}</p>
      <p>负责人：{{ popupData.chargeUserName }}</p>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { watch } from 'vue';
import {
  CloseOutlined
} from '@ant-design/icons-vue';
// 注册组件
defineOptions({
  components: {
    CloseOutlined
  }
})

const props = defineProps({
  popupData: {
    type: Object,
    default: () => {}
  }
})
watch(() => props.popupData, (val) => {
  console.log(val);
})
const emit = defineEmits(['closePop'])
// 关闭
const close = () => {
  emit('closePop')
}
</script>
  
<style lang="scss" scoped>
.mapPop {
  z-index: 2;
  transform: translate(-20px, -40px);
  position: absolute;
  max-width: 500px;
  max-height: 160px;
  background: rgba(0, 0, 0, 0.9);
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
  display: flex;
  flex-direction: column;
  z-index: 999;
  border-radius: 4px;

  &::before {
    content: "";
    position: absolute;
    display: inline-block;
    top: 100%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-top-color: rgba(0, 0, 0, 0.9);
    border-width: 10px;
    left: 120px;
    margin-left: -10px;
  }

  .popHeader {
    width: 100%;
    height: 40px;
    // background-color: #656665;
    border-bottom: 1px solid #797878;
    display: flex;
    align-items: center;
    padding: 0 16px;
    box-sizing: border-box;
    justify-content: space-between;

    span:first-child {
      font-size: 16px;
      color: #FFFFFF;
    }

    .anticon-close {
      font-size: 18px;
      color: #FFFFFF;
      cursor: pointer;
    }
  }

  .popContent {
    padding: 15px;
    box-sizing: border-box;
    width: 100%;
    height: 1px;
    flex: 1;

    p {
      font-size: 14px;
      line-height: 20px;
      color: #fff;

      &:not(:last-child) {
        margin-bottom: 10px;
      }
    }
  }
}
</style>