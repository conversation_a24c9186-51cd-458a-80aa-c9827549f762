import { getRequest, postRequest, postRequestForm } from '../http/request'

export const workspaceApi = {
  // 分页查询数据字典KEY
  queryDictKey: (param: object) => {
    return postRequest("/support/dict/key/query", param);
  },
  // 获取首页项目列表
  getProjectList: (param: object) => {
    return getRequest(`/gProject/queryMy`, param);
  },
  // 获取首页项目列表
  getProjectDetails: (id: any) => {
    return getRequest(`/gProject/get/${id}`, {});
  },
  // 获取首页资源列表
  getResources: () => {
    return getRequest(`/gProject/queryProject`, {});
  },
  // 获取项目下在线设备
  getProjectDevs: (id: string) => {
    return getRequest(`/gProject/queryDeviceList/${id}`, {});
  },
  // 获取风险明细列表
  queryRiskList: (param: object) => {
    return postRequest(`/gProject/queryAlarmPage`, param);
  },
  // 获取作业记录列表
  getWorkRecords: (param: object) => {
    return postRequest(`/jobRecord/queryJobList`, param);
  },
  // 获取作业记录详情
  getWorkRecordDetail: (param: object) => {
    return postRequest(`/jobRecord/queryJobVideo`, param);
  },
  // 获取作业检测报告
  getWorkTestReport: (param: object) => {
    return postRequestForm(`/jobReport/one`, param);
  },
  // 获取在线设备作业信息
  getWorkInfo: (id: string) => {
    return getRequest(`/jobBase/detail/${id}`, {});
  },
  // 获取存在作业记录的无人机列表
  getVehicleListHasRecord: (projectId: any) => {
    return postRequest(`/jobRecord/queryVehicleList/${projectId}`, {});
  },
  // 获取存在作业记录的无人机列表
  changeLiving: (param: object) => {
    return postRequestForm(`/jobRecord/liveBroadcast`, param);
  },
  // 获取存在作业记录的航线列表
  getAirlineListHasRecord: (projectId: any, param: object) => {
    return postRequestForm(`/jobRecord/queryAirlineList/${projectId}`, param);
  },
  // 获取存在作业记录某个点的检测数据
  getPointDetetionData: (param: object) => {
    return getRequest(`/airlinePoint/pointTrend`, param);
  },
  // 获取存在作业记录某个点的浓度分析数据
  getPointDetetionAnalyseData: (param: object) => {
    return getRequest(`/airlinePoint/pointTrendAnalyse`, param);
  },
  createSSE: (param: object) => {
    return getRequest(`/createSse`, param);
  },
  closeSSE: (param: object) => {
    return postRequest(`/closeSse`, param);
  },
};
