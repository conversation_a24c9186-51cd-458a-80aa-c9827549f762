import fs from "fs-extra";
import path from "path";
import { fileURLToPath } from "url";

// 用于兼容 ESM 模块中 __dirname 的替代方式
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const src = path.resolve(__dirname, "src/cesium");
const dest = path.resolve(__dirname, "dist/cesium");

try {
  await fs.ensureDir(dest);
  await fs.copy(src, dest);
  console.log("✅ Cesium assets copied successfully.");
} catch (error) {
  console.error("❌ Failed to copy assets:", error);
}