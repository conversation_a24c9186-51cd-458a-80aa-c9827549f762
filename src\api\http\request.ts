import axios from 'axios'
import { CURRENT_CONFIG } from './config'
import { message } from 'ant-design-vue'
import { router } from '@/router'
import { ELocalStorageKey, ERouterName, EUserType } from '@/types/enums'
export * from './type'
import { decryptData, encryptData } from '@/utils/encrypt'
import { DATA_TYPE_ENUM } from '@/utils/constants'
import { deleteAllCookies } from '@/utils/cookies'
import store from '@/store'

// 定义请求头中的X-Request-Id
const REQUEST_ID = 'X-Request-Id'

// token的消息头
const TOKEN_HEADER = 'Authorization'
// 获取本地存储中的token
function getAuthToken() {
  return localStorage.getItem(ELocalStorageKey.Token)
}

// 创建axios实例
const instance = axios.create({
  // withCredentials: true,
  // headers: {
  //   'Content-Type': 'application/json'
  // }
  // timeout: 12000,
})

// 请求拦截器
instance.interceptors.request.use(
  config => {
    // console.log(config)
    // 将token添加到请求头中
    config.headers[TOKEN_HEADER] = 'Bearer ' + getAuthToken()
    // 设置请求的基础URL
    config.baseURL = CURRENT_CONFIG.baseURL
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

const logout = () => {
  store.dispatch('user/logout')
  store.dispatch('user/setLoginInfo', {})
  router.replace('/' + ERouterName.LOGIN)
}
// 响应拦截器
instance.interceptors.response.use(
  response => {
    // 根据content-type ，判断是否为 json 数据
    let contentType = response.headers['content-type'] ? response.headers['content-type'] : response.headers['Content-Type']
    if (contentType.indexOf('application/json') === -1) {
      return Promise.resolve(response)
    }

    // 如果是json数据
    if (response.data && response.data instanceof Blob) {
      return Promise.reject(response.data)
    }

    // 如果是加密数据
    if (response.data.dataType === DATA_TYPE_ENUM.ENCRYPT.value) {
      response.data.encryptData = response.data.data
      let decryptStr = decryptData(response.data.data)
      if (decryptStr) {
        response.data.data = JSON.parse(decryptStr)
      }
    }

    const res = response.data
    if (res.code && res.code !== 1) {
      // `token` 过期或者账号已在别处登录
      if (res.code === 30007 || res.code === 30008) {
        message.destroy()
        message.error('您没有登录，请重新登录')
        setTimeout(logout, 500)
        return Promise.reject(response)
      }

      // 等保安全的登录提醒
      if (res.code === 30010 || res.code === 30011) {
        message.error({
          title: '重要提醒',
          content: res.msg
        })
        return Promise.reject(response)
      }

      // 长时间未操作系统，需要重新登录
      if (res.code === 30012) {
        message.error({
          title: '重要提醒',
          content: res.msg,
          onOk: logout
        })
        setTimeout(logout, 300)
        return Promise.reject(response)
      }
      message.destroy()
      message.error(res.msg)
      return Promise.reject(response)
    } else {
      return Promise.resolve(res)
    }
  },
  error => {
    // 对响应错误做点什么
    if (error.message.indexOf('timeout') !== -1) {
      message.destroy()
      message.error('网络超时')
    } else if (error.message === 'Network Error') {
      message.destroy()
      message.error('网络连接错误')
    } else if (error.message.indexOf('Request') !== -1) {
      message.destroy()
      message.error('网络发生错误')
    }
    return Promise.reject(error)
  }
)
// 响应拦截器
// instance.interceptors.response.use(
//   response => {
//     // 打印请求的URL、数据和响应
//     console.info('URL: ' + response.config.baseURL + response.config.url, '\nData: ', response.data, '\nResponse:', response)
//     // 如果响应数据中的code不为0，则显示错误信息
//     if (response.data.code && response.data.code !== 0) {
//       message.error(response.data.message)
//     }
//     return response
//   },
//   err => {
//     // 获取请求头中的X-Request-Id
//     const requestId = err?.config?.headers && err?.config?.headers[REQUEST_ID]
//     if (requestId) {
//       console.info(REQUEST_ID, '：', requestId)
//     }
//     // 打印请求的URL、方法和错误信息
//     console.info('url: ', err?.config?.url, `【${err?.config?.method}】 \n>>>> err: `, err)

//     let description = '-'
//     // 如果错误信息中有message，则将message赋值给description
//     if (err.response?.data && err.response.data.message) {
//       description = err.response.data.message
//     }
//     // 如果错误信息中有result，则将result中的message赋值给description
//     if (err.response?.data && err.response.data.result) {
//       description = err.response.data.result.message
//     }
//     console.log(description)
//     // @See: https://github.com/axios/axios/issues/383
//     // 如果没有响应状态码，则显示网络异常的错误信息
//     if (!err.response || !err.response.status) {
//       message.error('The network is abnormal, please check the backend service and try again')
//       return
//     }
//     // 如果响应状态码不为200，则显示错误信息
//     if (err.response?.status !== 200) {
//       message.error(`ERROR_CODE: ${err.response?.status}`)
//     }
//     // 如果响应状态码为403，则跳转到首页
//     // if (err.response?.status === 403) {
//     //   // window.location.href = '/'
//     // }
//     // 如果响应状态码为401，则根据用户类型跳转到对应页面
//     if (err.response?.status === 401) {
//       console.error(err.response)
//       const flag: number = Number(localStorage.getItem(ELocalStorageKey.Flag))
//       switch (flag) {
//         case EUserType.Web:
//           router.push(ERouterName.PROJECT)
//           break
//         case EUserType.Pilot:
//           router.push(ERouterName.PILOT)
//           break
//       }
//     }

//     return Promise.reject(err)
//   }
// )
export default instance

// ================================= 对外提供请求方法：通用请求，get， post, 下载download等 =================================

/**
 * get请求
 */
export const getRequest = (url: string, params: any) => {
  return request({ url, method: 'get', params })
}

/**
 * 通用请求封装
 * @param config
 */
export const request = (config: any) => {
  return instance.request(config)
}

/**
 * post请求
 */
export const postRequest = (url: string, data: any) => {
  // console.log(data);
  return request({
    data,
    url,
    method: "post",
  });
};

/**
 * post请求
 */
export const postRequestForm = (url: string, data: any) => {
  const formData = new FormData();
  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      formData.append(key, data[key]);
    }
  }
  return request({
    data: formData,
    url,
    method: "post",
  });
};

// ================================= 加密 =================================

/**
 * 加密请求参数的post请求
 */
export const postEncryptRequest = (url: string, data: any) => {
  return request({
    data: { encryptData: encryptData(data) },
    url,
    method: "post",
  });
};

// ================================= 下载 =================================

export const postDownload = function (url: string, data: any) {
  request({
    method: "post",
    url,
    data,
    responseType: "blob",
  })
    .then((data) => {
      handleDownloadData(data);
    })
    .catch((error) => {
      handleDownloadError(error);
    });
};

/**
 * 文件下载
 */
export const getDownload = function (url: string, params: any) {
  request({
    method: 'get',
    url,
    params,
    responseType: 'blob'
  })
    .then(data => {
      handleDownloadData(data)
    })
    .catch(error => {
      handleDownloadError(error)
    })
}

function handleDownloadError(error: any) {
  if (error instanceof Blob) {
    const fileReader = new FileReader()
    fileReader.readAsText(error)
    fileReader.onload = () => {
      const msg = fileReader.result
      const jsonMsg = JSON.parse(msg)
      message.destroy()
      message.error(jsonMsg.msg)
    }
  } else {
    message.destroy()
    message.error('网络发生错误', error)
  }
}

function handleDownloadData(response: any) {
  if (!response) {
    return
  }

  // 获取返回类型
  let contentType = _.isUndefined(response.headers['content-type']) ? response.headers['Content-Type'] : response.headers['content-type']

  // 构建下载数据
  let url = window.URL.createObjectURL(new Blob([response.data], { type: contentType }))
  let link = document.createElement('a')
  link.style.display = 'none'
  link.href = url

  // 从消息头获取文件名
  let str = _.isUndefined(response.headers['content-disposition'])
    ? response.headers['Content-Disposition'].split(';')[1]
    : response.headers['content-disposition'].split(';')[1]

  let filename = _.isUndefined(str.split('fileName=')[1]) ? str.split('filename=')[1] : str.split('fileName=')[1]
  link.setAttribute('download', decodeURIComponent(filename))

  // 触发点击下载
  document.body.appendChild(link)
  link.click()

  // 下载完释放
  document.body.removeChild(link) // 下载完成移除元素
  window.URL.revokeObjectURL(url) // 释放掉blob对象
}
