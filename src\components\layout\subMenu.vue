<template>
  <a-sub-menu :key="props.data.menuId">
    <template #icon v-if="props.data.icon">
      <component :is="props.data.icon" />
    </template>
    <template #title>
      <span :title="JSON.stringify(props.data.children)">{{ props.data.menuName }}</span>
    </template>

    <template v-if="props.data.children && props.data.children.length">
      <template v-for="menuItem in props.data.children" :key="menuItem.menuId">
        <template v-if="menuItem.children && menuItem.children.length">
          <subMenu :data="menuItem"></subMenu>
        </template>
        <template v-else>
          <!-- <a-menu-item :key="menuItem.name"> -->
          <a-menu-item :key="menuItem.menuId" @click="menuItemClick(menuItem)">
            <template #icon v-if="menuItem.icon">
              <component :is="menuItem.icon" />
            </template>
            <span>{{ menuItem.menuName }}</span>
          </a-menu-item>
        </template>
      </template>
    </template>

    <!-- <a-menu-item key="5">Option 5</a-menu-item>
    <a-menu-item key="6">Option 6</a-menu-item>
    <a-menu-item key="7">Option 7</a-menu-item>
    <a-menu-item key="8">Option 8</a-menu-item>-->
  </a-sub-menu>
</template>

<script lang="ts" setup>
const props = defineProps({
  data: {
    type: Object,
    default: () => {}
  }
})
const emits = defineEmits(['turnToPage']);
  const menuItemClick = (menu) => {
    emits('menuItemClick', menu);
  };
</script>

<style lang="scss" scoped>
</style>