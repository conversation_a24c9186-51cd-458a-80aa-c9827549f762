<template>
<div class="cesiumContainer" ref="cesiumContainer"></div>
</template>

      
<script lang="ts" setup >
import {
  onMounted,
  ref,
  watch
} from 'vue'
import * as Cesium from 'cesium'
import "cesium/Build/Cesium/Widgets/widgets.css";

const cesiumContainer = ref < HTMLDivElement | null > (null)
const viewer = ref < Cesium.Viewer | null > (null)

const initMap = () => {
  if (cesiumContainer.value) {
    // Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI1NDlhMmU0Yi04MzY5LTRhZjktYjcyYi01YjRlMjc0MTc4YTAiLCJpZCI6MzAyMzUyLCJpYXQiOjE3NDcyMDk0Njh9.tdsHRJeCZr2AwNemO7Q1TE52VXbwvBXH5OmW2SL1m6c'
    // 初始化 Cesium Viewer
    viewer.value = new Cesium.Viewer(cesiumContainer.value, {
      terrain: Cesium.Terrain.fromWorldTerrain()
    });

    // 移除默认添加的图层
    viewer.value.imageryLayers.removeAll();

    // 隐藏版权信息（使用官方推荐方式）
    const creditContainer = viewer.value.cesiumWidget.creditContainer as HTMLDivElement | undefined;
    if (creditContainer) {
      creditContainer.style.display = "none";
    }


    // 禁用默认的地形
    viewer.value.terrainProvider = new Cesium.EllipsoidTerrainProvider()

    // 创建一个Cesium的UrlTemplateImageryProvider实例，用于显示高德地图的影像
    const gaodeMapImageryProvider = new Cesium.UrlTemplateImageryProvider({
      // 定义地图瓦片的URL模板
      // url: 'https://webst0{s}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}', // 高德卫星影像
      // url: 'https://webst0{s}.is.autonavi.com/appmaptile?style=8&x={x}&y={y}&z={z}', // 高德路网注记
      url: 'http://webst0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=2&style=7&x={x}&y={y}&z={z}', // 高德矢量底图
      // 设置地图数据的版权信息
      credit: 'Gaode Maps',
      // 定义地图覆盖的矩形区域，使用弧度表示
      rectangle: new Cesium.Rectangle(
        Cesium.Math.toRadians(73.63),
        Cesium.Math.toRadians(-5.06),
        Cesium.Math.toRadians(135.78),
        Cesium.Math.toRadians(53.56)
      ),
      // 设置地图的最大级别
      maximumLevel: 18,
      // 设置地图的最小级别
      minimumLevel: 0,
      // 子域名数组，用于负载均衡
      subdomains: ['1', '2', '3', '4']
    });

    const imageryLayer = viewer.value.imageryLayers.addImageryProvider(gaodeMapImageryProvider);
    imageryLayer.alpha = 1.0; // 设置透明度为 1.0，即完全不透明

    // 监听瓦片加载失败事件
    // const tileLoadError = ref(false);
    // gaodeMapImageryProvider.errorEvent.addEventListener((error) => {
    //   console.error('百度地图瓦片加载失败:', error);
    //   tileLoadError.value = true;
    //   // 这里可以添加重试逻辑，例如：
    //   setTimeout(() => {
    //     viewer.value.imageryLayers.remove(imageryLayer);
    //     viewer.value.imageryLayers.addImageryProvider(gaodeMapImageryProvider);
    //   }, 5000); // 5 秒后重试
    // });

    // 设置初始视角到郑州市
    viewer.value.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(113.5610851405145, 34.8230213780326, 1000),
      orientation: {
        heading: Cesium.Math.toRadians(0.0),
        pitch: Cesium.Math.toRadians(-90.0),
        roll: 0.0
      }
    })
  }
}

onMounted(() => {
  initMap()
})
</script>
    
<style scoped>
.cesiumContainer {
  width: 100%;
  height: 100%;
}
</style>
