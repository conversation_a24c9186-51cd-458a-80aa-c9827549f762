<template>
  <div class="liveCesiumMapContainer">
    <div id="liveCesiumContainer" class="liveCesiumContainer" ref="cesiumContainer"></div>
  </div>
</template>

      
<script lang="ts" setup >
import {
  watch,
  onMounted,
  onBeforeUnmount,
  ref
} from 'vue'
import { useRoute } from 'vue-router';
import { initLiveCesium, liveViewer } from './initLiveCesium';
import { useLiveCesium } from './useLiveCesium';
import detection1 from "@/assets/images/detection1.png";
import detection2 from "@/assets/images/detection2.png";
import detection3 from "@/assets/images/detection3.png";
import airPoint1 from "@/assets/images/airPoint1.png";
import airPoint2 from "@/assets/images/airPoint2.png";
import airPoint3 from "@/assets/images/airPoint3.png";

initLiveCesium()
const { updateTrack, clearMap, addpoint, setEvent, addpointNoLabel, clearAddPoints, clearAddPointsNoLabel } = useLiveCesium();

// 定义props
const props = defineProps({
  // 坐标点
  pointsArr: {
    type: Array,
    default: [],
  },
  alarmPointsArr: {
    type: Array,
    default: [],
  },
  // 轨迹数据
  trackData: {
    type: Array,
    default: [],
  }
});

const route = useRoute();

watch(() => route.path, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    clearMap()
  }
})

watch(() => props.pointsArr, (newValue, oldValue) => {
  if (newValue.length > 0) {
    clearAddPoints()
    setPointWithStatus(newValue)
  }
});

watch(() => props.trackData, (newValue, oldValue) => {
  if (newValue.length > 0) {
    // updateTrack(newValue)
  }
}, { deep: true });

watch(() => props.alarmPointsArr, (newValue, oldValue) => {
  if (newValue.length > 0) {
    clearAddPointsNoLabel()
    setTimeout(() => {
      setAlarmPointWithStatus(newValue)
    }, 200);
  }
});

const setPointWithStatus = (points: any) => {
  points.forEach((point: any) => {
    if (point.alarmVO) {
      if (point.alarmVO.maxValue && point.alarmVO.gradeId) {
        addpoint(point, airPoint3, '#CE1A0D');
      } else if (point.alarmVO.maxValue && !point.alarmVO.gradeId) {
        addpoint(point, airPoint2, '#C8860D');
      } else {
        addpoint(point, airPoint1, '#0C9815');
      }
    } else {
      addpoint(point, airPoint1, '#0C9815');
    }
    setEvent()
  });
};

const setAlarmPointWithStatus = (points: any) => {
  points.forEach((point: any) => {
    if (point.maxValue && point.gradeId) {
      addpointNoLabel(point, detection3);
    } else if (point.maxValue && !point.gradeId) {
      addpointNoLabel(point, detection2);
    } else {
      addpointNoLabel(point, detection1);
    }
    setEvent()
  });
};

onMounted(() => {
  if(props.pointsArr.length > 0) {
    setPointWithStatus(props.pointsArr)
  }
  if (props.trackData.length > 0) {
    // updateTrack(props.trackData)
  }
});

onBeforeUnmount(() => {
  console.log('liveVieweronBeforeUnmount');
  if(liveViewer) {
    clearMap()
    liveViewer.destroy();
  }
});


</script>
    
<style scoped>
.liveCesiumMapContainer{
  width: 100%;
  height: 100%;
}
.liveCesiumContainer {
  width: 100%;
  height: 100%;
}
</style>
