<template>
  <div class="userCenter">
    <a-card title="基本信息">
      <a-row type="flex" justify="space-around" align="middle">
        <a-col :span="8">
          <div class="userTitle">
            <div class="userTop">
              <div class="avatar">
                <a-avatar shape="square" :size="128" :src="avatarUrl">
                  <template #icon>
                    <UserOutlined />
                  </template>
                </a-avatar>
              </div>
              <div class="name">你好！{{ userInfo.actualName }}</div>
            </div>
            <div class="avatarChangeBt">
              <a-upload
                name="file"
                :show-upload-list="false"
                :headers="uploadConfig.headers"
                :customRequest="customRequest"
                :before-upload="beforeUpload"
              >
                <a-button size="small" type="primary" @click="avatarChange">
                  <template #icon>
                    <UserSwitchOutlined />
                  </template>
                  修改头像
                </a-button>
              </a-upload>
            </div>
          </div>
        </a-col>
        <a-col :span="12">
          <ul class="userInfo">
            <li class="userInfoItem">
              <span class="itemKey">姓名：</span>
              <em class="itemValue">{{ userInfo.actualName }}</em>
            </li>
            <li class="userInfoItem">
              <span class="itemKey">组织机构：</span>
              <em class="itemValue">{{ userInfo.departmentName }}</em>
            </li>
            <li class="userInfoItem">
              <span class="itemKey">邮箱：</span>
              <em class="itemValue">{{ userInfo.email }}</em>
              <EditOutlined @click="emailChangeVisible = true" />
            </li>
            <li class="userInfoItem">
              <span class="itemKey">手机号：</span>
              <em class="itemValue">{{ userInfo.phone }}</em>
              <EditOutlined @click="phoneChangeVisible = true" />
            </li>
            <li class="userInfoItem">
              <span class="itemKey">登录密码：</span>
              <em class="itemValue">已设置</em>
              <EditOutlined @click="pwdChangeVisible = true" />
            </li>
            <li class="userInfoItem">
              <span class="itemKey">密码等级：</span>
              <em class="itemValue">
                <a-rate class="custom-rate" v-model:value="userInfo.securityLevel" allowHalf disabled :count="3">
                  <template #character>
                    <SafetyOutlined />
                  </template>
                </a-rate>
              </em>
            </li>
            <li class="userInfoItem">
              <span class="itemKey">最后登录时间：</span>
              <em class="itemValue">{{ userInfo.lastLoginTime }}</em>
            </li>
            <li class="userInfoItem">
              <span class="itemKey">登录保持时间：</span>
              <!-- <em class="itemValue">{{ userInfo.expirationTime / 60 / 60 / 24 }}天</em> -->
              <em class="itemValue">30分钟</em>
            </li>
          </ul>
        </a-col>
      </a-row>
    </a-card>
    <a-modal
      v-model:open="emailChangeVisible"
      title="修改邮箱"
      cancelText="取消"
      okText="确定修改"
      :closable="false"
      :keyboard="false"
      :maskClosable="false"
      @cancel="handleEmailCancle"
      @ok="handleEmailOk"
    >
      <a-form ref="emailFormRef" :model="emailForm" :rules="emailRules">
        <a-form-item ref="email" label="邮箱" name="email">
          <a-input v-model:value="emailForm.email" />
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal
      v-model:open="phoneChangeVisible"
      title="修改手机号"
      cancelText="取消"
      okText="确定修改"
      :closable="false"
      :keyboard="false"
      :maskClosable="false"
      @cancel="handlePhoneCancle"
      @ok="handlePhoneOk"
    >
      <a-form ref="phoneFormRef" :model="phoneForm" :rules="phoneRules">
        <a-form-item ref="phone" label="手机号" name="phone">
          <a-input v-model:value="phoneForm.phone" />
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal
      v-model:open="pwdChangeVisible"
      title="修改密码"
      cancelText="取消"
      okText="确定修改"
      :closable="false"
      :keyboard="false"
      :maskClosable="false"
      @cancel="handlePWDCancle"
      @ok="handlePWDOk"
    >
      <a-form ref="pwdFormRef" :model="pwdForm" :rules="pwdRules" :labelCol="{ span: 5 }">
        <a-form-item ref="pwd" label="密码" name="pwd">
          <a-input-password :visibility-toggle="false" v-model:value="pwdForm.pwd" />
        </a-form-item>
        <a-form-item ref="newPwd" label="新密码" name="newPwd">
          <a-tooltip :trigger="['focus']" placement="bottomLeft" overlay-class-name="numeric-input">
            <template #title>
              <div style="background: #fff; color: #000;font-size: 12px;">
                <div style="display: flex; align-items: center;padding-bottom: 5px;">
                  <div style="white-space: nowrap; font-size: 14px;">安全等级：</div>
                  <div
                    v-for="(item, index) in 3"
                    :key="index"
                    :style="{width: '30px',height: '12px', background: index >= safeLevel ? '#ddd' : '#52c41a',marginRight: '10px'}"
                  ></div>
                </div>
                <div v-for="(item, index) in passwordOptions" :key="index" style="display: flex; align-items: center;padding-bottom: 5px;">
                  <CloseCircleFilled style="color: #ff4d4f; font-size: 16px; margin-right: 5px;" v-if="!item.passFlag" />
                  <CheckCircleFilled style="color: #52c41a; font-size: 16px; margin-right: 5px;" v-else />
                  <div class="numeric-input-title">{{ item.demand }}</div>
                </div>
              </div>
            </template>
            <a-input-password
              :visibility-toggle="false"
              v-model:value="pwdForm.newPwd"
              :maxlength="32"
              @change="validatePasswordHandle"
              @focus="validatePasswordHandle"
            />
          </a-tooltip>
          <!-- <a-input-password :visibility-toggle="false" v-model:value="pwdForm.newPwd" /> -->
        </a-form-item>
        <a-form-item ref="confirmPwd" label="确认新密码" name="confirmPwd">
          <a-input-password :visibility-toggle="false" v-model:value="pwdForm.confirmPwd" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { UserSwitchOutlined, UserOutlined, EditOutlined, SafetyOutlined } from '@ant-design/icons-vue'
import { ValidateErrorEntity } from 'ant-design-vue/es/form/interface'
import { message } from 'ant-design-vue'
import { onMounted, ref, reactive, UnwrapRef, computed } from 'vue'
import { loginApi } from '@/api/loginApi'
import { userApi } from '@/api/manageTool/userApi'
import { useStore } from 'vuex'
import { ELocalStorageKey } from '@/types/enums'
import { CURRENT_CONFIG } from '@/api/http/config'
import { encryptData } from '@/utils/encrypt'
import { regular } from '@/utils/regular'
// 注册组件
defineOptions({
  components: {
    UserSwitchOutlined,
    UserOutlined,
    EditOutlined,
    SafetyOutlined
  }
})
const store = useStore()
const userInfo = ref({})
async function getLoginInfo() {
  try {
    //获取登录用户信息
    const res = await loginApi.getLoginInfo()
    userInfo.value = res.data
    avatarUrl.value = userInfo.value.avatar
    emailForm.email = userInfo.value.email
    phoneForm.phone = userInfo.value.phone
    //更新用户信息到vuex
    store.dispatch('user/setLoginInfo', res.data)
  } catch (e) {
    userInfo.value = {}
  }
}
async function getPasswordComplexityEnabled() {
  try {
    const res = await userApi.getPasswordComplexityEnabled()
    console.log(res)
  } catch (e) {}
}
// -------------------- 修改头像 --------------------
const uploadConfig = {
  headers: {
    Authorization: 'Bearer ' + localStorage.getItem(ELocalStorageKey.Token),
    'Content-Type': 'multipart/form-data'
  }
}
const avatarUrl = ref('')
const accept = ref('.jpg,.jpeg,.png,.gif')
const maxSize = ref(10)
const folder = ref(1)
let updateAvatarLoading = ref(false)
function beforeUpload(file, files) {
  const suffixIndex = file.name.lastIndexOf('.')
  const fileSuffix = file.name.substring(suffixIndex <= -1 ? 0 : suffixIndex)
  if (accept.value.indexOf(fileSuffix) === -1) {
    message.error(`只支持上传 ${accept.value.replaceAll(',', ' ')} 格式的文件`)
    return false
  }

  const isLimitSize = file.size / 1024 / 1024 < maxSize.value
  if (!isLimitSize) {
    message.error(`单个文件大小必须小于 ${maxSize.value} Mb`)
    return false
  }
  return true
}

async function customRequest(options) {
  console.log('customRequest', options)
  updateAvatarLoading.value = true
  try {
    const formData = new FormData()
    formData.append('folder', folder.value)
    formData.append('file', options.file)
    console.log(formData)
    let res = await userApi.uploadFile(formData)
    console.log('res', res)
    let file = res.data
    // avatarUrl.value = CURRENT_CONFIG.baseURL + '/upload/' + file.fileKey
    // 更新头像
    let updateAvatarForm = { avatar: file.fileKey }
    await userApi.updateAvatar(updateAvatarForm)
    message.success('更新成功')
    // 重新获取详情，刷新整体缓存
    await getLoginInfo()
  } catch (e) {
  } finally {
    updateAvatarLoading.value = false
  }
}

// 评级值
const level = ref<number>(2.5)

const avatarChange = () => {
  console.log('修改头像')
}

// -------------------- 修改邮箱 --------------------
const emailChangeVisible = ref<boolean>(false)
const emailFormRef = ref()
interface EmailFormState {
  email: string
}
const emailForm: UnwrapRef<EmailFormState> = reactive({
  email: ''
})
const emailRules = {
  email: [
    {
      required: true,
      message: '请输入邮箱',
      trigger: 'blur'
    },
    { pattern: regular.email, message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

const handleEmailOk = () => {
  console.log('修改邮箱')
  emailFormRef.value
    .validate()
    .then(async () => {
      console.log('values', emailForm)
      try {
        await userApi.updateEmail(emailForm)
        message.success('更新成功')
        await getLoginInfo()
        handleEmailCancle()
      } catch (error) {}
    })
    .catch((error: ValidateErrorEntity<EmailFormState>) => {
      console.log('error', error)
    })
}

const handleEmailCancle = () => {
  emailChangeVisible.value = false
  emailFormRef.value.resetFields()
}

// -------------------- 修改手机号 --------------------
const phoneChangeVisible = ref<boolean>(false)
const phoneFormRef = ref()
interface PhoneFormState {
  phone: string
}
const phoneForm: UnwrapRef<PhoneFormState> = reactive({
  phone: ''
})
const phoneRules = {
  phone: [
    {
      required: true,
      message: '请输入手机号',
      trigger: 'blur'
    },
    { pattern: regular.phone, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

const handlePhoneOk = () => {
  console.log('修改手机号')
  phoneFormRef.value
    .validate()
    .then(async () => {
      try {
        await userApi.updatePhone(phoneForm)
        message.success('更新成功')
        await getLoginInfo()
        handlePhoneCancle()
      } catch (error) {}
    })
    .catch((error: ValidateErrorEntity<PhoneFormState>) => {
      console.log('error', error)
    })
}

const handlePhoneCancle = () => {
  phoneChangeVisible.value = false
  phoneFormRef.value.resetFields()
}

// -------------------- 修改密码 --------------------

let validatePassword = async (rule: RuleObject, value: string) => {
  // console.log('validatePassword')
  if (!value) {
    // console.log('请输入初始密码')
    passwordOptions[0].passFlag = false
    passwordOptions[1].passFlag = false
    passwordOptions[2].passFlag = false
    return Promise.reject('')
  }

  // 检查密码长度,是否包含账号和手机号
  if (
    value.length < 8 ||
    value.length > 32 ||
    (userInfo.value.loginName && value.includes(userInfo.value.loginName)) ||
    (userInfo.value.phone && value.includes(userInfo.value.phone))
  ) {
    passwordOptions[0].passFlag = false
    // console.log('密码长度应在8到32个字符之间,不包含账号和手机号')
  } else {
    passwordOptions[0].passFlag = true
  }

  // 检查密码是否只包含大小写字母、数字、特殊符号
  const validChars = /^[a-zA-Z0-9!@#$%^&*(),.?":{}|<>]*$/
  if (!validChars.test(value)) {
    passwordOptions[1].passFlag = false
    // console.log('密码只能包含大小写字母、数字、特殊符号')
  } else {
    passwordOptions[1].passFlag = true
  }

  // 检查密码是否至少包含3种字符类型
  const hasUpperCase = /[A-Z]/.test(value)
  const hasLowerCase = /[a-z]/.test(value)
  const hasDigit = /[0-9]/.test(value)
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value)
  const charTypesCount = [hasUpperCase, hasLowerCase, hasDigit, hasSpecialChar].filter(Boolean).length
  if (charTypesCount < 3) {
    passwordOptions[1].passFlag = false
    // console.log('密码至少包含大小写字母、数字、特殊符号中的3种')
  } else {
    passwordOptions[1].passFlag = true
  }
  // 检查字符串中是否有连续的字母或数字组合
  const isContinuous = (str: string, patt: RegExp, len: number) => {
    try {
      if (patt.test(str)) {
        const arr = str.match(patt)
        if (arr) {
          return arr.some(arrItem => {
            const asciiArr: any[] = []
            return arrItem.split('').some(strItem => {
              if (asciiArr.length === 0) {
                asciiArr.push(strItem.charCodeAt(0))
              } else {
                if (asciiArr[asciiArr.length - 1] + 1 === strItem.charCodeAt(0)) {
                  asciiArr.push(strItem.charCodeAt(0))
                } else {
                  asciiArr.splice(asciiArr.length - 1, 1)
                }
              }
              return asciiArr.length > len
            })
          })
        } else {
          return false
        }
      } else {
        return false
      }
    } catch (e) {
      return false
    }
  }

  // 检查密码是否包含超过4位连续或相同的字母或者数字组合
  const sameChars = /([0-9A-Za-z])\1{4,}/g
  const continuousNumbers = /[\d]{5,}/g
  const continuousChars = /[A-Za-z]{5,}/g
  const sameCharsFlag = sameChars.test(value)
  const continuousNumbersFlag = isContinuous(value, continuousNumbers, 4)
  const continuousChartsFlag = isContinuous(value, continuousChars, 4)
  if (sameCharsFlag || continuousNumbersFlag || continuousChartsFlag) {
    passwordOptions[2].passFlag = false
    // console.log('密码不能包含超过4位连续或相同的字母或者数字组合')
  } else {
    passwordOptions[2].passFlag = true
  }
  if (passwordOptions.every(option => option.passFlag)) {
    return Promise.resolve()
  } else {
    return Promise.reject('密码不符合要求')
  }
}
const pwdChangeVisible = ref<boolean>(false)
const pwdFormRef = ref()
interface PWDFormState {
  pwd: string
  newPwd: string
  confirmPwd: string
}
const pwdForm: UnwrapRef<PWDFormState> = reactive({
  pwd: '',
  newPwd: '',
  confirmPwd: ''
})
const pwdRules = {
  pwd: [
    {
      required: true,
      message: '请输入原密码',
      trigger: 'blur'
    }
  ],
  newPwd: [
    {
      required: true,
      message: '请输入新密码',
      trigger: 'blur'
    },
    { validator: validatePassword }
  ],
  confirmPwd: [
    {
      required: true,
      message: '请确认新密码',
      trigger: 'blur'
    }
  ]
}

const handlePWDOk = () => {
  pwdFormRef.value
    .validate()
    .then(async () => {
      if (pwdForm.newPwd !== pwdForm.confirmPwd) {
        message.error('两次密码输入不一致')
      } else {
        try {
          const params = {
            oldPassword: pwdForm.pwd,
            newPassword: pwdForm.newPwd
          }
          await userApi.updatePassword(params)
          message.success('更新成功')
          await getLoginInfo()
          handlePWDCancle()
        } catch (error) {}
      }
    })
    .catch((error: ValidateErrorEntity<PWDFormState>) => {
      console.log('error', error)
    })
}

const handlePWDCancle = () => {
  pwdChangeVisible.value = false
  pwdFormRef.value.resetFields()
} // 验证密码
const passwordOptions = reactive([
  {
    demand: '8-32个字符，密码不能包含账号、手机号',
    passFlag: false
  },
  {
    demand: '只能包含大小写字母、数字、特殊符号，至少包含其中3种',
    passFlag: false
  },
  {
    demand: '不能包含超过4位连续或相同的字母或者数字组合',
    passFlag: false
  }
])
// const safeLevel = computed(() => {
//   let level = 0
//   passwordOptions.forEach(option => {
//     if (option.passFlag) {
//       level++
//     }
//   })
//   return level
// })
const safeLevel = ref(0)
const validatePasswordHandle = async () => {
  if (pwdForm.newPwd) {
    pwdFormRef.value.validate(['newPwd'])
    try {
      const params = {
        password: pwdForm.newPwd
      }
      const res = await userApi.getPasswordSecurityLevel(params)
      safeLevel.value = res.data
    } catch (error) {
      safeLevel.value = 0
    }
  }
}

onMounted(() => {
  getLoginInfo()
  // getPasswordComplexityEnabled()
})
</script>

<style lang="scss">
.userCenter {
  overflow: auto;
  width: 100%;
  height: 100%;
  .ant-card {
    width: 1200px;
    margin: 20px auto;
    border-radius: 8px;
    .ant-card-head {
      .ant-card-head-wrapper {
        .ant-card-head-title {
          font-weight: bold;
        }
      }
    }
  }
  .userTitle {
    .userTop {
      display: flex;
      align-items: center;
      .avatar {
        margin-right: 40px;
        .ant-avatar {
          border-radius: 10px;
        }
      }
      .name {
        font-size: 24px;
        font-weight: bold;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .avatarChangeBt {
      margin-top: 20px;
      height: 28px;
      .ant-btn {
        height: 28px;
        border-radius: 4px;
      }
    }
  }
  .userInfo {
    overflow: hidden;
    .userInfoItem {
      float: left;
      width: 50%;
      margin: 15px 0;
      .itemKey {
        display: inline-block;
        width: 100px;
        text-align: right;
        vertical-align: middle;
        color: #b7b7b9;
      }

      .itemValue {
        margin-left: 6px;
        font-style: normal;
        vertical-align: middle;
      }

      .anticon-edit {
        margin-left: 10px;
        cursor: pointer;
        vertical-align: middle;
        color: #1890ff;
      }
    }
  }

  .custom-rate {
    color: #04a704;
  }
}

.numeric-input.ant-tooltip {
  max-width: none;
  .ant-tooltip-arrow::before {
    background-color: #fff;
  }
  .ant-tooltip-inner {
    min-width: 32px;
    min-height: 37px;
    background-color: #fff;
  }
  .numeric-input-title {
    white-space: nowrap;
    font-size: 14px;
  }
}
</style>
