<template>
<div class="cesiumContainer" ref="cesiumContainer"></div>
</template>

      
<script lang="ts" setup >
import {
  onMounted,
  ref,
  watch
} from 'vue'
import * as Cesium from 'cesium'
import "cesium/Build/Cesium/Widgets/widgets.css";

// 定义props
const props = defineProps({
  // 地图缩放级别
  projectPoints: {
    type: Array,
    default: [],
  },
});

watch(() => props.projectPoints, (newValue, oldValue) => {
  if (newValue.length > 0) {
    addPoints(newValue)
  }
});

const cesiumContainer = ref < HTMLDivElement | null > (null)
const viewer = ref < Cesium.Viewer | null > (null)

const initMap = () => {
  if (cesiumContainer.value) {
    // 初始化 Cesium Viewer
    viewer.value = new Cesium.Viewer(cesiumContainer.value, {
      animation: false,
      baseLayerPicker: false,
      fullscreenButton: false,
      geocoder: false,
      homeButton: false,
      infoBox: false,
      sceneModePicker: false,
      selectionIndicator: false,
      timeline: false,
      navigationHelpButton: false,
      // sceneMode: Cesium.SceneMode.SCENE2D, // 设置场景模式为二维
      terrainProvider: new Cesium.EllipsoidTerrainProvider(), // 禁用默认地形
      skyBox: false // 禁用天空盒
    });

    // 移除默认添加的图层
    viewer.value.imageryLayers.removeAll();

    // 隐藏版权信息（使用官方推荐方式）
    const creditContainer = viewer.value.cesiumWidget.creditContainer as HTMLDivElement | undefined;
    if (creditContainer) {
      creditContainer.style.display = "none";
    }


    // 禁用默认的地形
    viewer.value.terrainProvider = new Cesium.EllipsoidTerrainProvider()

    // 创建一个Cesium的UrlTemplateImageryProvider实例，用于显示高德地图的影像
    const gaodeMapImageryProvider = new Cesium.UrlTemplateImageryProvider({
      // 定义地图瓦片的URL模板
      // url: 'https://webst0{s}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}', // 高德卫星影像
      // url: 'https://webst0{s}.is.autonavi.com/appmaptile?style=8&x={x}&y={y}&z={z}', // 高德路网注记
      url: 'http://webst0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=2&style=7&x={x}&y={y}&z={z}', // 高德矢量底图
      // 设置地图数据的版权信息
      credit: 'Gaode Maps',
      // 定义地图覆盖的矩形区域，使用弧度表示
      rectangle: new Cesium.Rectangle(
        Cesium.Math.toRadians(73.63),
        Cesium.Math.toRadians(-5.06),
        Cesium.Math.toRadians(135.78),
        Cesium.Math.toRadians(53.56)
      ),
      // 设置地图的最大级别
      maximumLevel: 18,
      // 设置地图的最小级别
      minimumLevel: 0,
      // 子域名数组，用于负载均衡
      subdomains: ['1', '2', '3', '4']
    });

    const imageryLayer = viewer.value.imageryLayers.addImageryProvider(gaodeMapImageryProvider);
    imageryLayer.alpha = 1.0; // 设置透明度为 1.0，即完全不透明

    // 监听瓦片加载失败事件
    // const tileLoadError = ref(false);
    // gaodeMapImageryProvider.errorEvent.addEventListener((error) => {
    //   console.error('百度地图瓦片加载失败:', error);
    //   tileLoadError.value = true;
    //   // 这里可以添加重试逻辑，例如：
    //   setTimeout(() => {
    //     viewer.value.imageryLayers.remove(imageryLayer);
    //     viewer.value.imageryLayers.addImageryProvider(gaodeMapImageryProvider);
    //   }, 5000); // 5 秒后重试
    // });

    // 设置初始视角到郑州市
    viewer.value.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(113.5610851405145, 34.8230213780326, 1000),
      orientation: {
        heading: Cesium.Math.toRadians(0.0),
        pitch: Cesium.Math.toRadians(-90.0),
        roll: 0.0
      }
    })
  }
}

const addPoints = (points: any[]) => {
  
  points.forEach((pointItem: any) => {
    if (!viewer.value) {
      return;
    }
    // 添加点
    const point = viewer.value.entities.add({
      position: Cesium.Cartesian3.fromDegrees(pointItem.lat, pointItem.lon), // 设置点的地理坐标（经度，纬度）
      point: {
        pixelSize: 20, // 点的大小，以像素为单位
        color: Cesium.Color.RED, // 点的颜色
        outlineColor: Cesium.Color.WHITE, // 点轮廓颜色
        outlineWidth: 2 // 点轮廓宽度
      }
    });
    addInfoBox(viewer.value, point);
  })
  // 如果有添加的点，将相机视角移动到第一个点位置
  if (points.length > 0 && viewer.value) {
    const firstPoint = Cesium.Cartesian3.fromDegrees(points[0].lat, points[0].lon, 1000);
    viewer.value.camera.flyTo({
      destination: firstPoint,
      orientation: {
        heading: Cesium.Math.toRadians(0.0),
        pitch: Cesium.Math.toRadians(-90.0),
        roll: 0.0
      }
    });
  }
}

const addInfoBox = (viewer: any, point: any) => {
  const handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
  handler.setInputAction((event: any) => {
    //click.position 获取的是屏幕坐标
    let cartesian = viewer.scene.pickPosition(event.position);
    let pickingEntity = viewer.scene.pick(event.position);   //获取三维坐标和点击的实体对象
    let coord: any;
    //转经纬度坐标
    if (pickingEntity && pickingEntity.id && pickingEntity.id.position) {
      cartesian = pickingEntity.id.position._value;
      let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
      let lontable = Number(
        Cesium.Math.toDegrees(cartographic.longitude).toFixed(7)
      );
      let lattable = Number(
        Cesium.Math.toDegrees(cartographic.latitude).toFixed(7)
      );
      let height = cartographic.height;
      coord = { lon: lontable, lat: lattable, height: height };
    } else {
      let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
      let lontable = Cesium.Math.toDegrees(cartographic.longitude).toFixed(5) * 1;
      let lattable = Cesium.Math.toDegrees(cartographic.latitude).toFixed(5) * 1;
      let height = cartographic.height;
      coord = { lon: lontable, lat: lattable, height: height };
    }
    if (callback != undefined && typeof callback === "function") {
      callback(pickingEntity, coord);
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK); // 或者使用其他事件类型，例如RIGHT_CLICK等。
}

onMounted(() => {
  initMap()
})
</script>
    
<style scoped>
.cesiumContainer {
  width: 100%;
  height: 100%;
}
</style>
