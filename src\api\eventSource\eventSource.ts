// eventSource.ts
import { EventSourcePolyfill } from "event-source-polyfill";
import { CURRENT_CONFIG } from "../http/config";
import { ELocalStorageKey } from "@/types/enums";

// 获取本地存储中的token
function getAuthToken() {
  return localStorage.getItem(ELocalStorageKey.Token);
}

let eventSource: any = null;
let reconnectAttempts = 0; // 重连次数

export default function SSEManager(url: string) {
  if (eventSource) {
    console.log("sse已经存在：", eventSource);
    return eventSource;
  } else {
    eventSource = new EventSourcePolyfill(CURRENT_CONFIG.baseURL + url, {
      // heartbeatTimeout: 3 * 60 * 1000,
      headers: {
        Authorization: "Bearer " + getAuthToken(),
        Accept: "text/event-stream",
      },
      withCredentials: true,
    });

    // 自定义 onopen 处理器
    eventSource.onopen = function (e: any) {
      console.log(e, "连接刚打开时触发");
      reconnectAttempts = 0; // 重置重连次数

      // 可以在这里添加更多逻辑，比如发送心跳包等
    };

    // 自定义 onmessage 处理器
    eventSource.onmessage = (event: any) => {
      console.log("收到消息内容是:", event.data);

      // 处理接收到的消息，可以在这里根据业务需求进行处理
      handleIncomingMessage(event.data);
    };

    // 自定义 onerror 处理器
    eventSource.onerror = (event: any) => {
      console.error("SSE 连接出错：", event);
      eventSource.close(); // 关闭连接
      eventSource = null;

      // 自动重连逻辑
      reconnectAttempts++;
      const reconnectDelay = Math.min(
        30000,
        1000 * Math.pow(2, reconnectAttempts)
      ); // 计算重连延迟，最大延迟为30秒
      console.log(`将在 ${reconnectDelay} 毫秒后尝试重连...`);

      // 等待一定时间后重连
      setTimeout(() => {
        if (!eventSource) {
          console.log("尝试重连 SSE...");
          SSEManager(url); // 递归调用重连
        }
      }, reconnectDelay);
    };

    // 添加自定义事件监听器
    eventSource.addEventListener("custom-event", (event: any) => {
      console.log("收到自定义事件:", event.data);
      handleCustomEvent(event.data);
    });

    // 发送心跳包保持连接活跃
    setInterval(() => {
      if (eventSource && eventSource.readyState === EventSourcePolyfill.OPEN) {
        console.log("发送心跳包...");
        // 这里假设服务器能识别心跳包，实际应用中可能需要调整
        // 注意：并非所有SSE实现都支持发送数据回服务器
        // 如果需要双向通信，考虑使用WebSocket
      }
    }, 60000); // 每分钟发送一次心跳

    return eventSource;
  }
}

// 处理接收到的消息
function handleIncomingMessage(data: any) {
    console.log('处理消息:', data);
    // 根据具体业务逻辑处理消息
}

// 处理自定义事件
function handleCustomEvent(data: any) {
  console.log("处理自定义事件:", data);
  // 根据具体业务逻辑处理自定义事件
}
