<!-- 弹框-->
<template>
  <div :class="['mapPop', popupData.name ? 'airlinePoint' : '']" v-if="popupData">
    <div class="popContent">
      <p>{{ popupData.name ? popupData.name : '自主检测' }} : {{ popupData.name ? (popupData.alarmVO ? (popupData.alarmVO.maxValue + popupData.detectTargetUnit) : '无检测数据') : ((popupData.maxValue || '') + popupData.detectTargetUnit) }}</p>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { watch, onMounted } from 'vue';
import {
  CloseOutlined
} from '@ant-design/icons-vue';
// 注册组件
defineOptions({
  components: {
    CloseOutlined
  }
})

const props = defineProps({
  popupData: {
    type: Object,
    default: () => {}
  }
})
watch(() => props.popupData, (val) => {
  // console.log(val);
})
const emit = defineEmits(['closePop'])
// 关闭
const close = () => {
  emit('closePop')
}

onMounted(() => { 
  // console.log(props.popupData);
})

</script>
  
<style lang="scss" scoped>
.mapPop {
  z-index: 2;
  transform: translate(10px, -40px);
  position: absolute;
  width: 180px;
  max-height: 160px;
  background: rgba(0, 0, 0, 0.9);
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
  display: flex;
  flex-direction: column;
  z-index: 999;
  border-radius: 4px;
  text-align: center;

  &.airlinePoint {
    transform: translate(10px, -75px);
  }

  &::before {
    content: "";
    position: absolute;
    display: inline-block;
    top: 100%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-top-color: rgba(0, 0, 0, 0.9);
    border-width: 10px;
    left: 50%;
    margin-left: -10px;
  }

  .popContent {
    padding: 15px;
    box-sizing: border-box;
    width: 100%;
    height: 1px;
    flex: 1;

    p {
      font-size: 14px;
      line-height: 20px;
      color: #fff;
      margin: 0;
    }
  }
}
</style>