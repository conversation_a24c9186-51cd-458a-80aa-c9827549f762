import { Module } from 'vuex'
import { ActionTree, GetterTree, MutationTree } from 'vuex'

// 定义模块的状态类型
export interface RouteState {
  clickPointData: object;
  selectedJob: object;
}

// 初始化状态
const state: RouteState = {
  clickPointData: {},
  selectedJob: {}
}

// 定义 mutations
const mutations: MutationTree<RouteState> = {
  SET_POINT_DATA(state, clickPointData) {
    console.log("state");
    console.log(state);
    state.clickPointData = Object.assign({}, clickPointData);
  },
  SET_SELECTED_JOB_DATA(state, data) {
    console.log("state");
    console.log(state);
    state.selectedJob = Object.assign({}, data);
  }
};

// 定义 actions
const actions: ActionTree<RouteState, any> = {
  // 保存菜单数据
  mapPointClick({ commit }, data: Array<any>) {
    console.log("store");
    console.log(data);
    commit("SET_POINT_DATA", data);
  },
  // 首页跳转作业记录时选中的作业
  workrecordSelected({ commit }, data: Object) {
    console.log("store");
    console.log(data);
    commit("SET_SELECTED_JOB_DATA", data);
  },
};

// 定义 getters
const getters: GetterTree<RouteState, any> = {
}

// 定义模块
const workspace: Module<RouteState, any> = {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
export default workspace
