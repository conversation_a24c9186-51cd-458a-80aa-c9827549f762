import { getRequest, postRequest } from '../http/request'

export const ruleApi = {
  // 分页查询报警规则
  queryAlarmRulePage: (param: object) => {
    return postRequest('/alarmRule/queryPage', param)
  },
  // 添加报警规则
  addAlarmRule: (param: object) => {
    return postRequest('/alarmRule/add', param)
  },
  // 更新报警规则
  updateAlarmRule: (param: object) => {
    return postRequest('/alarmRule/update', param)
  },
  // 删除报警规则
  deleteAlarmRule: (id: string | number) => {
    return getRequest(`/alarmRule/delete/${id}`, {})
  },
}
