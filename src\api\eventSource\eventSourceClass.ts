// eventSource.ts
import { EventSourcePolyfill } from "event-source-polyfill";
import { CURRENT_CONFIG } from "../http/config";
import { ELocalStorageKey } from "@/types/enums";

// 获取本地存储中的token
function getAuthToken() {
  return localStorage.getItem(ELocalStorageKey.Token);
}

export class SSEManager {
  private eventSource: EventSource | null = null;
  private listeners: { [key: string]: ((data: any) => void)[] } = {};
  private url: string;

  /**
   * 构造函数，初始化 EventSource 的 URL
   * @param url - EventSource 的目标 URL
   */
  constructor(url: string) {
    this.url = url;
  }

  /**
   * 初始化 EventSource 连接
   */
  public connect() {
    if (this.eventSource) return;

    try {
      // 创建 EventSourcePolyfill 实例并设置自定义请求头
      this.eventSource = new EventSourcePolyfill(CURRENT_CONFIG.baseURL + this.url, {
        headers: {
          Authorization: "Bearer " + getAuthToken(),
          Accept: "text/event-stream",
        },
        withCredentials: true,
      });
      console.log("sse创建成功");
      

      // 心跳检测逻辑（替代 heartbeatTimeout）
      const heartbeatInterval = setInterval(() => {
        if (!this.eventSource || this.eventSource.readyState !== 1) {
          console.warn("EventSource connection lost, attempting to reconnect...");
          this.close();
          this.connect();
        }
      }, 3 * 60 * 1000); // 每 3 分钟检查一次连接状态

      // 空值检查，确保 eventSource 不为 null
      if (this.eventSource) {
        // 监听消息事件
        this.eventSource.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.notifyListeners("message", data);
          } catch (error) {
            console.error("Failed to parse message:", error);
          }
        };

        // 监听自定义事件
        this.eventSource.addEventListener("custom-event", (event) => {
          try {
            const data = JSON.parse(event.data);
            this.notifyListeners("custom-event", data);
          } catch (error) {
            console.error("Failed to parse custom event:", error);
          }
        });

        // 错误处理
        this.eventSource.onerror = (error) => {
          console.error("Error occurred:", error);
          clearInterval(heartbeatInterval); // 清除心跳检测定时器
          this.close();
        };
      }
    } catch (error) {
      console.error("Failed to create EventSource:", error);
    }
  }

  /**
   * 添加监听器
   * @param type - 事件类型 ('message', 'custom-event' 等)
   * @param listener - 监听器函数
   */
  public addListener(type: string, listener: (data: any) => void) {
    if (!this.listeners[type]) {
      this.listeners[type] = [];
    }
    this.listeners[type]?.push(listener);
  }

  /**
   * 移除监听器
   * @param type - 事件类型
   * @param listener - 要移除的监听器函数
   */
  public removeListener(type: string, listener: (data: any) => void) {
    if (this.listeners[type]) {
      this.listeners[type] = this.listeners[type]?.filter((l) => l !== listener);
    }
  }

  /**
   * 关闭 EventSource 连接
   */
  public close() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }

  /**
   * 通知所有监听者
   * @param type - 事件类型
   * @param data - 数据
   */
  private notifyListeners(type: string, data: any) {
    this.listeners[type]?.forEach((listener) => listener(data));
  }
}