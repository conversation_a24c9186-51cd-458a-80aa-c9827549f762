<template>
  <a-modal
    :visible="localVisible"
    :title="isEditing ? '编辑航线' : '添加航线'"
    @update:visible="(val: boolean) => emit('update:visible', val)"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :confirmLoading="confirmLoading"
  >
    <a-form
      :model="formData"
      :rules="rules"
      ref="formRef"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
    >
      <a-form-item name="name" label="航线名称">
        <a-input v-model:value="formData.name" placeholder="请输入航线名称" />
      </a-form-item>
      <a-form-item name="type" label="航线类型">
        <a-select v-model:value="formData.type" placeholder="请选择航线类型">
          <a-select-option value="inspection">巡检航线</a-select-option>
          <a-select-option value="transport">运输航线</a-select-option>
          <a-select-option value="survey">测绘航线</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item name="description" label="航线描述">
        <a-textarea v-model:value="formData.description" placeholder="请输入航线描述" :rows="4" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed } from 'vue'
import type { FormInstance } from 'ant-design-vue'

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  isEditing: {
    type: Boolean,
    default: false
  },
  initialFormData: {
    type: Object,
    default: () => ({
      name: '',
      description: '',
      type: 'inspection'
    })
  },
  confirmLoading: {
    type: Boolean,
    default: false
  }
})

// 定义事件
const emit = defineEmits(['update:visible', 'submit', 'cancel'])

// 创建本地计算属性来处理visible的双向绑定
const localVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  type: 'inspection'
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入航线名称', trigger: 'blur' },
    { min: 2, max: 30, message: '航线名称长度为2-30个字符', trigger: 'blur' }
  ],
  type: [{ required: true, message: '请选择航线类型', trigger: 'change' }]
}

// 监听初始数据变化，更新表单数据
watch(
  () => props.initialFormData,
  (newVal) => {
    Object.assign(formData, newVal)
  },
  { deep: true, immediate: true }
)

// 监听弹窗显示状态，当关闭时重置表单
watch(
  () => props.visible,
  (newVal) => {
    if (!newVal) {
      resetForm()
    }
  }
)

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    emit('submit', { ...formData })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 取消操作
const handleCancel = () => {
  emit('update:visible', false)
  emit('cancel')
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
}
</script>

<style lang="scss">
/* 全局样式，确保弹窗中的文字颜色清晰可见 */
.ant-modal {
  .ant-modal-content {
    background-color: #fff;

    .ant-modal-header {
      background-color: #fff;

      .ant-modal-title {
        color: rgba(0, 0, 0, 0.85);
        font-weight: bold;
      }
    }

    .ant-modal-body {
      .ant-form-item-label > label {
        color: rgba(0, 0, 0, 0.85);
      }

      .ant-input,
      .ant-select-selector,
      .ant-input-number {
        background-color: #fff !important;
        color: rgba(0, 0, 0, 0.85) !important;
        border-color: #d9d9d9 !important;
      }

      .ant-select-selection-placeholder,
      .ant-input::placeholder {
        color: rgba(0, 0, 0, 0.45) !important;
      }
    }

    .ant-modal-footer {
      .ant-btn {
        color: rgba(0, 0, 0, 0.85);

        &.ant-btn-primary {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #fff;
        }
      }
    }
  }
}
</style> 