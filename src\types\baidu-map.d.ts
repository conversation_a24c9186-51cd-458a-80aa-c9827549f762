// types/baidu-map.d.ts
declare namespace BMapGL {
  class Map {
    constructor(id: string);
    centerAndZoom(point: Point, zoom: number): void;
    addControl(control: any): void;
    enableScrollWheelZoom(enable: boolean): void;
    addEventListener(event: string, callback: (e: any) => void): void;
    clearOverlays(): void;
    addOverlay(overlay: any): void;
  }

  class Point {
    constructor(lng: number, lat: number);
  }

  class NavigationControl {}

  class LocalSearch {
    constructor(map: Map, options: any);
    search(keyword: string): void;
    getStatus(): number;
  }

  class Icon {
    constructor(url: string, size: Size);
  }

  class Marker {
    constructor(point: Point, options?: { icon: Icon });
  }

  const BMAP_STATUS_SUCCESS: number;
}

  interface Size {
    width: number;
    height: number;
  }