<!-- 管理工具-数据字典 -->
<template>
  <div class="dict dictContainer">
    <div class="handleWrap clearfix">
      <div class="left fl">
        <a-form layout="inline" :model="searchFormState">
          <a-form-item>
            <a-input v-model:value="searchFormState.searchWord" placeholder="请输入字典编码或名称" size="small" allowClear @pressEnter="onSearch"></a-input>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="onSearch">搜索</a-button>
          </a-form-item>
        </a-form>
      </div>
    </div>
    <div class="contentWrap custom-scrollbar" ref="tableWrapperRef">
      <a-table
        ref="tableRef"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="false"
        bordered
        :rowKey="(record: any) => record.dictKeyId"
        :scroll="{ x: 1200,  y: tableHeight }"
        :loading="tableLoading"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'operation'">
            <span>
              <a-button type="link" size="small" @click="dictConfigHandle(record)" style="color: #52c41a;">字典配置</a-button>
            </span>
          </template>
        </template>
      </a-table>
    </div>
    <div class="pageWrap">
      <a-pagination
        size="small"
        v-model:current="pagination.pageNum"
        :page-size="pagination.pageSize"
        :total="total"
        show-size-changer
        show-quick-jumper
        :show-total="(total: number) => `共 ${total} 条数据`"
        :page-size-options="PAGE_SIZE_OPTIONS"
        @change="(current: number, size: number) => onPageChange('current', current, size)"
        @showSizeChange="(current: number, size: number) => onPageChange('size', current, size)"
      />
    </div>
    <a-drawer title="字典配置" placement="right" width="1000px" v-model:open="dictConfigDrawer.visible" :destroyOnClose="true">
      <DictConfig :rowData="dictConfigDrawer.rowData" />
    </a-drawer>
  </div>
</template>

<script lang="ts" setup>
import {
  PlusOutlined,
  SlackOutlined,
  MoreOutlined,
  FieldTimeOutlined,
  UserOutlined,
  EnvironmentOutlined,
  DeleteOutlined,
  PlusCircleOutlined
} from '@ant-design/icons-vue'
import DictConfig from '@/components/Dict/DictConfig.vue'
import { onMounted, reactive, ref, UnwrapRef } from 'vue'
import { dictApi } from '@/api/manageTool/dictApi'
import { PageParamModel } from '@/types/paging'
import { PAGE_SIZE_OPTIONS } from '@/utils/constants'
import { getTableHeight } from '@/hooks/getTableHeight'

// 注册组件
defineOptions({
  components: {
    PlusOutlined,
    SlackOutlined,
    MoreOutlined,
    FieldTimeOutlined,
    UserOutlined,
    EnvironmentOutlined,
    DeleteOutlined,
    PlusCircleOutlined,
    DictConfig
  }
})
/* ====================== 表格 ====================== */
const tableWrapperRef = ref()
const tableRef = ref()
const tableHeight = getTableHeight(tableWrapperRef, tableRef)

const tableLoading = ref(false)
const columns = reactive([
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    customRender: ({ index }: any) => (pagination.value.pageNum - 1) * pagination.value.pageSize + index + 1,
    fixed: 'left',
    width: 80,
    align: 'center'
  },
  {
    title: '字典编号',
    dataIndex: 'keyCode',
    key: 'keyCode',
    align: 'center'
  },
  {
    title: '字典名称',
    dataIndex: 'keyName',
    key: 'keyName',
    align: 'center'
  },
  {
    title: '字典描述',
    dataIndex: 'remark',
    key: 'remark',
    ellipsis: true,
    align: 'center'
  },
  {
    title: '操作',
    key: 'operation',
    dataIndex: 'operation',
    // slots: { customRender: 'operation' },
    fixed: 'right',
    width: 120,
    align: 'center'
  }
])
const dataSource = ref([])

/* ====================== 分页 ====================== */
const pagination = ref<PageParamModel>({
  pageNum: 1, // 当前页码
  pageSize: 10, // 每页数量
  sortItemList: [
    {
      isAsc: false,
      column: 'key_code'
    }
  ]
})
const total = ref<number>(0)
const onPageChange = (type: string, pageNum: number, pageSize: number) => {
  console.log(pageNum, pageSize)
  if (type === 'current') {
    pagination.value.pageNum = pageNum
  } else {
    pagination.value.pageNum = 1
    pagination.value.pageSize = pageSize
  }
  queryDictKey()
}

/* ====================== 搜索 ====================== */
interface SearchFormState {
  // 搜索词
  searchWord?: string
  dictKeyId?: number
}
const searchFormState: UnwrapRef<SearchFormState> = reactive({
  searchWord: ''
})
const queryDictKey = async () => {
  try {
    tableLoading.value = true
    const result = await dictApi.queryDictKey({
      ...pagination.value,
      ...searchFormState
    })
    console.log(result)
    dataSource.value = result.data.list
    total.value = result.data.total
  } catch (e) {
    console.log(e)
    dataSource.value = []
    total.value = 0
  } finally {
    tableLoading.value = false
  }
}

const onSearch = () => {
  console.log(searchFormState.searchWord)
  pagination.value.pageNum = 1
  total.value = 0
  dataSource.value = []
  queryDictKey()
}

/* ====================== 字典配置 ====================== */
interface dictConfigDrawerModel {
  visible: boolean
  rowData?: any
}
const dictConfigDrawer = reactive<dictConfigDrawerModel>({
  visible: false,
  rowData: {}
})

const dictConfigHandle = (item: any) => {
  dictConfigDrawer.visible = true
  dictConfigDrawer.rowData = { ...item }
}
onMounted(() => {
  onSearch()
})
</script>
<style lang="scss">
.dictContainer {
  height: 100%;

  .handleWrap {
    height: 54px;
    padding: 10px;
    box-sizing: border-box;

    .left {
      width: 80%;

      .ant-input {
        box-sizing: border-box;
        width: 240px;
        height: 34px;
      }

      .ant-btn {
        height: 34px;
      }
    }

    .right {
      width: 20%;
      text-align: right;

      .ant-btn {
        height: 34px;
      }
    }
  }

  .contentWrap {
    height: calc(100% - 110px);
    padding: 10px;
    overflow: hidden;

    .ant-card {
      height: 100%;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      border-radius: 6px;
      cursor: default;

      &:hover {
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
      }

      .ant-card-body {
        padding: 15px;
      }

      .cardHead {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #000;

        .headIcon {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          background: #d1cdfc;
          text-align: center;
          min-width: 50px;

          .anticon-slack {
            font-size: 30px;
            color: #200de7;
            width: 30px;
            height: 30px;
            margin-top: 10px;
          }
        }

        .cardTitle {
          font-size: 14px;
          font-weight: 600;
          margin: 0 10px;
        }

        .ant-dropdown-link {
          color: #000;

          &:hover {
            color: #1890ff;
          }
        }
      }

      .cardInfo {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #999;
        margin: 10px 0;

        .anticon {
          margin-right: 4px;
        }
      }

      .cardRemarks {
        border-top: 1px solid #eee;
        font-size: 12px;
        color: #666;
        padding-top: 10px;
      }
    }
  }

  .pageWrap {
    text-align: right;
    margin-top: 20px;
    height: 38px;
    padding-right: 20px;
  }
}

.ant-modal-body {
  padding: 20px 30px 0 20px;
}

.dictHandleForm {
  .ant-input {
    vertical-align: middle;
  }

  .ant-select {
    vertical-align: middle;
  }

  .anticon-environment {
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    color: #666;
    vertical-align: middle;
  }

  .anticon-delete {
    color: red;
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    vertical-align: middle;
  }

  .anticon-plus-circle {
    color: blue;
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    vertical-align: middle;
  }

  .targetItem {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.resetPwdTips {
  text-align: center;
  margin: 20px 0 30px;
  font-size: 20px;
}

.ant-drawer {
  .ant-drawer-wrapper-body {
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .ant-drawer-header {
    }
    .ant-drawer-body {
      flex: 1;
      overflow: hidden;
    }
  }
}
</style>
