<template>
  <div class="workspace workspaceContainer">
    <div class="mapWrap">
      <WorkCesiumMap :pointsArr="pointArray" :alarmPointsArr="alarmPointArray" :trackData="airlineTrackPoints" :samplePointsData="samplePoints" :routeNow="routeNow" />
    </div>
    <div :class="['leftSide', 'clearfix', route.name === 'workRecordApp' ? 'appUse' : '']">
      <div class="workMenu fl" v-if="route.name !== 'workRecordApp'">
        <div :class="['workMenuItem', routeNow === 'projectInfo' ? 'active' : '']" title="项目信息" @click="taggleRoute('projectInfo')"><GroupOutlined /></div>
        <div :class="['workMenuItem', routeNow === 'workRecord' ? 'active' : '']" title="作业记录" @click="taggleRoute('workRecord')"><GatewayOutlined /></div>
      </div>
      <div class="workInfo fl">
        <router-view :currentProject="selectedProject" @mapDataChange="mapDataChange"></router-view>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import WorkCesiumMap from '@/components/workspace/WorkCesiumMap.vue';
import {
  GroupOutlined,
  GatewayOutlined
} from '@ant-design/icons-vue';
import { ref, watch, reactive, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { workspaceApi } from '@/api/workspace/workspaceApi'
// 注册组件
defineOptions({
  components: {
    WorkCesiumMap,
    GroupOutlined,
    GatewayOutlined
  }
})

const route = useRoute();

const router = useRouter();
const routeNow = ref<string | undefined>(undefined);

const taggleRoute = (routeName: string) => {
  routeNow.value = routeName;
  router.push('/workspace/' + routeName + '?id=' + route.query.id);
}
interface SELECTEDPROJECT {
  address: string,
  airlineMarkEntityList: any,
  chargeUserName: any,
  createTime: string,
  deletedFlag: number,
  des: string,
  id: number,
  lat: any,
  lon: any,
  mediaStorage: any,
  name: string,
  updateTime: string,
  userId: number,
  vehicleTypes: string[]
}
let selectedProject = ref<any>({})
const loading = ref(false);
const queryProject = async () => {
  // 获取 localStorage 中的项目信息
  const projectSelectedId = route.query.id;
  try {
    loading.value = true
    const result = await workspaceApi.getProjectDetails(projectSelectedId)
    selectedProject.value = {...result.data}
    document.title = result.data.name
  } catch (e) {
    selectedProject.value = {}
  } finally {
    loading.value = false
  }
}

const pointArray = ref<any>([])
const alarmPointArray = ref<any>([])

const mapDataChange = (from: string, data: any) => {
  if(from === 'record') {
    workRecordHandle(data)
  } else {
    projectInfoHandle(data)
  }
}

const samplePoints = ref<any>([])
const airlineTrackPoints = ref<any>([])

const workRecordHandle = (data: any) => {
  if (data.airlinePointVOList) {
    const airlinePointVOList = data.airlinePointVOList.map((item: any) => {
      const itemAlarmData = { ...item.alarmVO }
      delete itemAlarmData.id
      return { ...item, ...itemAlarmData }
    })
    pointArray.value = [...airlinePointVOList]
  }
  alarmPointArray.value = data.alarmDataVOList ? [...data.alarmDataVOList] : []
  airlineTrackPoints.value = data.baseDataVOList ? [...data.baseDataVOList] : []
  samplePoints.value = [...data.baseDataVOList]
}

const projectInfoHandle = (data: any) => {
  const projectDevsArray: any[] = []
  data.forEach((item: any) => {
    if (item.baseDataVO) {
      projectDevsArray.push({
        id: item.id,
        vehicleName: item.vehicleName,
        name: item.vehicleName,
        lon: item.baseDataVO.lon,
        lat: item.baseDataVO.lat,
      })
    }
  })
  pointArray.value = [...projectDevsArray]
  alarmPointArray.value = data.alarmDataVOList ? [...data.alarmDataVOList] : []
  alarmPointArray.value = []
}

onMounted(() => {
  if (route.name !== undefined) {
    routeNow.value = String(route.name); // 确保 route.name 是字符串
  } else {
    routeNow.value = ''; // 设置默认值，避免 undefined
  }
  queryProject()
})

</script>
<style lang="scss">
.workspaceContainer {
  width: 100%;
  height: 100%;
  position: relative;
  background: rgb(210, 210, 214);

  .leftSide {
    position: absolute;
    width: 400px;
    top: 0;
    left: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    color: #fff;
    &.appUse {
      width: auto;
    }
    .workMenu {
      width: 40px;
      padding: 10px 0;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      .workMenuItem{
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        cursor: pointer;
        &:hover{
          background: rgba(64, 96, 241, 0.75);
        }
        &.active{
          background: rgba(64, 96, 241, 0.75);
        }
      }
    }
    .workInfo {
      width: calc(100% - 40px);
      height: 100%;
    }
  }

  .mapWrap {
    width: 100%;
    height: 100%;
  }

}</style>
