import { createApp, ComponentCustomProperties, App as VueApp } from 'vue'
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $aMap: any // Map类
    $map: any // 地图对象
    $mouseTool: any
  }
}
let root: ComponentCustomProperties
let app = null as any

// 导出一个函数，用于创建Vue实例
export function createInstance (App: any): VueApp {
  // 创建Vue实例
  app = createApp(App)
  // 将Vue实例的配置属性赋值给root
  root = app.config.globalProperties as ComponentCustomProperties
  // 返回Vue实例
  return app
}

// 导出一个函数，用于获取根组件的自定义属性
export function getRoot (): ComponentCustomProperties {
  // 返回根组件的自定义属性
  return root
}

// 导出一个函数，用于获取Vue应用实例
export function getApp (): VueApp {
  // 返回Vue应用实例
  return app
}
