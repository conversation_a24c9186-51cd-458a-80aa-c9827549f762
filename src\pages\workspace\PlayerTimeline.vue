<template>
  <div class="playerTimeline">
    <!-- 时间显示 -->
    <div class="topWrap">
      <div class="dateTime">{{ currentDateTime }}</div>
      <!-- 总时长和当前播放时间 -->
      <span class="timeShow"> {{ formattedTime }} / {{ formatTime(duration) }}</span>
    </div>
    <!-- 播放进度条 -->
    <div class="sliderWrap">
      <!-- 播放/暂停按钮 -->
      <CaretRightOutlined v-if="!isPlaying" @click="togglePlay"/>
      <PauseOutlined v-else @click="togglePlay"/>
      <div class="sliderMain">
        <a-slider 
          :min="0" 
          :max="duration" 
          v-model:value="currentTime" 
          style="width: 100%;" 
          @change="onDrag" 
          :tipFormatter="formatTime"
        />
        <!-- 模拟点位 -->
        <div class="clickable-points">
          <div 
            v-for="(mark, key) in alarmMarks" 
            :key="key"
            :style="getPointStyle(key)"
            @click="handlePointClick(key)"
          >
            <p>{{ mark.label }}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="timeWrap">

    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, PropType, watch } from 'vue';
import {
  CaretRightOutlined,
  PauseOutlined
} from '@ant-design/icons-vue';
import moment from "moment";
import { onMounted } from 'vue';
import { viewer } from '@/components/cesiumMap/initCesium';
import { useCesium } from '@/components/cesiumMap/useCesium';

const { toggleClock, pauseAnimate, restartAnimation } = useCesium();

defineOptions({
  components: {
    CaretRightOutlined,
    PauseOutlined
  }
})

const props = defineProps({
  duration: {
    type: Number,
    required: true,
  },
  time: {
    type: Number || String,
    required: true,
  },
  startTime: {
    type: String,
    required: true,
  },
  alarmData: {
    type: Array as PropType<any[]>,
    required: true,
  }
});

const emit = defineEmits(['update:time', 'toggleStatus', 'dateTimeChange']);

const currentTime = ref(props.time);
const oldTime = ref(0);
const currentDateTime = ref('');
const isPlaying = ref(false);

watch(() => currentTime.value, (newVal, oldVal) => {
  oldTime.value = newVal;
});

const formattedTime = computed(() => formatTime(currentTime.value));

const alarmMarks = reactive<Record<number, { label: any }>>({});
const initAlarmMarks = () => {
  props.alarmData.forEach((item: any) => {
    const seconds = getTimeDiffInSeconds(props.startTime ,item.triggerTime || item.createTime)
    alarmMarks[seconds] = {
      label: (item.name ? item.name : '自主检测') + ': ' + (item.name ? (item.alarmVO ? (item.alarmVO.maxValue + item.detectTargetUnit) : '无检测数据') : ((item.maxValue || '') + item.detectTargetUnit))
    };
  });
}

// 计算每个点的位置
const getPointStyle = (key: number) => {
  return {
    left: `${key/props.duration * 100}%`, // 调整点的位置
  };
};

const handlePointClick = (time: number) => {
  currentTime.value = time;
  updateDateTime(currentDateTime.value, currentTime.value - oldTime.value, 'drag')
  emit('update:time', currentTime.value);
  
  if (isPlaying.value) {
    isPlaying.value = false
    pause();
    pauseAnimate()
  }
};

const onDrag = (value: number) => {
  // currentTime.value = value;
  console.log(value);
  
  updateDateTime(currentDateTime.value, currentTime.value - oldTime.value, 'drag')
  emit('update:time', +currentTime.value);

  if (isPlaying.value) {
    isPlaying.value = false
    pause();
    pauseAnimate()
  }
}

const togglePlay = () => {
  if(!props.duration) return
  if (currentTime.value >= props.duration) {
    currentTime.value = 0; // 重置时间为0
    currentDateTime.value = props.startTime
    emit('update:time', +currentTime.value);
    restartAnimation()
  }else {
    if (viewer) {
      toggleClock()
    }
  }

  isPlaying.value = !isPlaying.value;
  emit('toggleStatus', isPlaying.value);
  if (isPlaying.value) {
    play();
  } else {
    pause();
  }
}

let interval = ref<number | null>(null);
const play = () => {
  if(currentTime.value !== 0) {
    currentTime.value += 1;
    emit('update:time', +currentTime.value);
    updateDateTime(currentDateTime.value, 1, 'play')
  }
  interval.value = setInterval(() => {
    if (currentTime.value >= props.duration) {
      clearInterval(interval.value!);
      isPlaying.value = false;
      // goBackStart()
      return;
    }
    currentTime.value += 1;
    updateDateTime(currentDateTime.value, 1, 'play')
    emit('update:time', +currentTime.value);
  }, 1000);
}

const updateDateTime = (dateTime: string, addSecond: number, type: string) => {
  const currentDateTimeObj = moment(dateTime);
  currentDateTimeObj.add(addSecond, 'second');
  currentDateTime.value = currentDateTimeObj.format('YYYY-MM-DD HH:mm:ss');
  emit('dateTimeChange', currentDateTime.value, currentTime.value, type === 'drag' ? 'drag' : 'play');
  // if(addSecond !== 1) {
  //   var isoFormattedStr = moment(currentDateTime.value, "YYYY-MM-DD HH:mm:ss").utc().format("YYYY-MM-DDTHH:mm:ss") + 'Z'
  //   , pauseAnimate(isoFormattedStr)
  // }
}

const pause = () => {
  clearInterval(interval.value!);
}

const formatTime = (seconds: number): string => {
  const date = new Date(0); // 使用 0 替代 null
  date.setSeconds(seconds);
  return date.toISOString().substr(11, 8); // HH:mm:ss
}

const tiemFormatSeconds = (timeStr: string): number => {
  const parts = timeStr.split(':').map(Number);
  const hours = parts[0] || 0;
  const minutes = parts[1] || 0;
  const seconds = parts[2] || 0;
  return hours * 3600 + minutes * 60 + seconds;
}
const timeToSeconds = (timeStr: string) => {
  const [hours, minutes, seconds] = timeStr.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds;
}

const getTimeDiffInSeconds = (start: string, end: string) => {
  const startSeconds = timeToSeconds(start.split(' ')[1]);
  const endSeconds = timeToSeconds(end.split(' ')[1]);
  return endSeconds - startSeconds;
}

watch(
  () => props.time,
  (newVal) => {
    if (!isPlaying.value) {
      currentTime.value = newVal;
    }
  }
);

onMounted(() => {
  currentDateTime.value = props.startTime
  initAlarmMarks()
})

defineExpose({ pause })
</script>

<style lang="scss" scoped>
.playerTimeline {
  padding: 10px 20px 0;
  height: 65px;

  .topWrap{
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #222;
  }

  .sliderWrap {
    margin: 4px 0 2px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #222;
    gap: 15px;

    .sliderMain{
      flex: 1;
      position: relative;
      .anticon{
        font-size: 20PX;
      }
      .ant-slider{
        margin: 11px 0;  
      }

      :deep(.ant-slider-rail) {
        background-color: #b7b8b9;
      }

      :deep(.ant-slider-track) {
        background-color: #123dfa;
      }

      :deep(.ant-slider-handle) {
        border-color: #123dfa;
        background-color: #123dfa;
      }

      :deep(.ant-slider-mark) {
        display: none;
      }

      .clickable-points {
        div {
          width: 6px;
          height: 6px;
          background-color: #e70404;
          border-radius: 50%;
          cursor: pointer;
          position: absolute;
          top: 14px;
          transform: translateX(-50%);
          transition: all 0.2s;
          p {
            display: none;
            position: absolute;
            width: 120px;
            text-align: center;
            left: calc(50% - 50px);
            top: -100%;
            transform: translateY(-100%);
            background: #e70404;
            padding: 4px 0;
            border-radius: 3px;
            color: #fff;
          }

          &:hover {
            width: 10px;
            height: 10px;
            top: 12px;
            p {
              display: block;
            }
          }
        }
      }
    }
  }
}
</style>