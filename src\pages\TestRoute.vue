<template>
  <div class="test-route-container">
    <RouteLibrary class="left-panel" @routeSelected="handleRouteSelected" />
    <div class="map-content-wrapper">
      <div class="map-wrap">
        <HomeCesiumMap :pointsArr="pointArray" />
      </div>
      <div class="right-controls">
        <div class="control-panel">
          <h4>功能区</h4>
          <div class="control-buttons">
            <a-button class="control-btn" @click="zoomIn">
              <PlusOutlined />
            </a-button>
            <a-button class="control-btn" @click="zoomOut">
              <MinusOutlined />
            </a-button>
            <a-button class="control-btn" @click="resetView">
              <AimOutlined />
            </a-button>
            <a-button
              class="control-btn"
              @click="togglePointSelection"
              :type="isSelectingPoints ? 'primary' : 'default'"
              :class="{ 'selecting-point': isSelectingPoints }"
            >
              <ForkOutlined />
            </a-button>
            <a-button class="control-btn" @click="clearAllPoints">
              <DeleteOutlined />
            </a-button>
            <a-button
              class="control-btn"
              @click="saveCurrentRoute"
              :disabled="selectedPoints.length < 2"
            >
              <SaveOutlined />
            </a-button>
          </div>
          <div class="position-info">
            <p>高度: {{ formatNumber(cameraHeight) }}m</p>
            <p>经度: {{ formatNumber(centerLongitude) }}°</p>
            <p>纬度: {{ formatNumber(centerLatitude) }}°</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 使用公共的航线表单模态框组件 -->
    <RouteFormModal
      v-model:visible="saveRouteModalVisible"
      :initialFormData="routeForm"
      :confirmLoading="saveRouteLoading"
      @submit="confirmSaveRoute"
      @cancel="saveRouteModalVisible = false"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, reactive } from 'vue'
import HomeCesiumMap from '@/components/home/<USER>'
import RouteLibrary from '@/components/workspace/RouteLibrary.vue'
import RouteFormModal from '@/components/workspace/RouteFormModal.vue'
import { PlusOutlined, MinusOutlined, HomeOutlined, AimOutlined, DeleteOutlined, PlusCircleOutlined, ForkOutlined, SaveOutlined } from '@ant-design/icons-vue'
import { viewer } from '@/components/cesiumMap/initCesium'
import * as Cesium from 'cesium'
import { useCesium } from '@/components/cesiumMap/useCesium'
import { message } from 'ant-design-vue'

// 注册组件
defineOptions({
  components: {
    HomeCesiumMap,
    RouteLibrary,
    RouteFormModal,
    PlusOutlined,
    MinusOutlined,
    HomeOutlined,
    AimOutlined,
    DeleteOutlined,
    PlusCircleOutlined,
    ForkOutlined,
    SaveOutlined
  }
})

// 初始化地图点位数据
const pointArray = ref<any[]>([])
const { flyToEntitiesCenter } = useCesium()

// 视角信息
const cameraHeight = ref<number>(0)
const centerLongitude = ref<number>(0)
const centerLatitude = ref<number>(0)

// 用户当前位置
const currentPosition = ref<{
  longitude: number
  latitude: number
  height: number
  heading: number
  pitch: number
  roll: number
}>({
  longitude: 116.3915,
  latitude: 39.9053,
  height: 500000,
  heading: 0,
  pitch: -Cesium.Math.PI_OVER_TWO,
  roll: 0
})

// 当前选中的航线
const currentRoute = ref<any>(null)

// 格式化数字，保留2位小数
const formatNumber = (num: number): string => {
  return num.toFixed(2)
}

// 处理选择航线事件
const handleRouteSelected = (route: any) => {
  currentRoute.value = route
  
  if (route.points && route.points.length > 0) {
    // 清空当前的点
    clearAllPoints()
    
    // 加载航线点位
    selectedPoints.value = route.points.map((point: any) => {
      return Cesium.Cartesian3.fromDegrees(point.longitude, point.latitude, point.height || 200)
    })
    
    // 绘制点位
    drawPointsAndLines()
    
    // 飞向所有点位中心
    if (viewer) {
      // 将点位转换为flyToEntitiesCenter需要的格式（带有lon和lat属性的对象数组）
      const pointsForFly = route.points.map((point: any) => ({
        lon: point.longitude,
        lat: point.latitude
      }))
      
      // 调用flyToEntitiesCenter飞向点位中心
      flyToEntitiesCenter(pointsForFly)
    }
  } else {
    message.info('该航线没有点位数据')
  }
}

// 更新位置信息
const updatePositionInfo = () => {
  if (!viewer) return

  // 获取相机高度
  const ellipsoid = viewer.scene.globe.ellipsoid
  const cameraPosition = viewer.camera.position
  const cartographic = ellipsoid.cartesianToCartographic(cameraPosition)
  cameraHeight.value = cartographic.height

  // 获取地图中心点
  const centerPosition = new Cesium.Cartesian2(viewer.canvas.clientWidth / 2, viewer.canvas.clientHeight / 2)
  const centerCartesian = viewer.camera.pickEllipsoid(centerPosition, ellipsoid)

  if (centerCartesian) {
    const centerCartographic = ellipsoid.cartesianToCartographic(centerCartesian)
    centerLongitude.value = Cesium.Math.toDegrees(centerCartographic.longitude)
    centerLatitude.value = Cesium.Math.toDegrees(centerCartographic.latitude)
  }
}

// 保存当前位置
const saveCurrentPosition = () => {
  if (!viewer) return

  const camera = viewer.camera
  const position = camera.position
  const ellipsoid = viewer.scene.globe.ellipsoid
  const cartographic = ellipsoid.cartesianToCartographic(position)

  currentPosition.value = {
    longitude: Cesium.Math.toDegrees(cartographic.longitude),
    latitude: Cesium.Math.toDegrees(cartographic.latitude),
    height: cartographic.height,
    heading: camera.heading,
    pitch: camera.pitch,
    roll: camera.roll
  }
}

// 获取当前位置
const getCurrentPosition = () => {
  if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(
      position => {
        currentPosition.value = {
          longitude: position.coords.longitude,
          latitude: position.coords.latitude,
          height: 10000, // 设置一个合适的高度
          heading: 0,
          pitch: -Cesium.Math.PI_OVER_TWO,
          roll: 0
        }

        // 立即飞到当前位置
        if (viewer) {
          viewer.camera.setView({
            destination: Cesium.Cartesian3.fromDegrees(
              currentPosition.value.longitude,
              currentPosition.value.latitude,
              currentPosition.value.height
            ),
            orientation: {
              heading: currentPosition.value.heading,
              pitch: currentPosition.value.pitch,
              roll: currentPosition.value.roll
            }
          })
          updatePositionInfo()
        }
      },
      error => {
        console.error('获取位置信息失败:', error)
        // 使用默认位置
        saveCurrentPosition()
      },
      {
        enableHighAccuracy: true,
        timeout: 5000,
        maximumAge: 0
      }
    )
  } else {
    console.error('浏览器不支持地理位置服务')
    saveCurrentPosition()
  }
}

// 放大地图
const zoomIn = () => {
  if (viewer) {
    // 获取当前高度
    const ellipsoid = viewer.scene.globe.ellipsoid
    const cameraPosition = viewer.camera.position
    const cartographic = ellipsoid.cartesianToCartographic(cameraPosition)
    const currentHeight = cartographic.height

    // 设置固定的放大间隔为2000m
    const zoomAmount = 2000

    // 确保高度不会变为负数
    if (currentHeight - zoomAmount > 1000) {
      viewer.camera.zoomIn(zoomAmount)
    } else {
      // 如果放大后高度会小于1000m，则只放大到1000m
      const safeZoomAmount = currentHeight - 1000
      if (safeZoomAmount > 0) {
        viewer.camera.zoomIn(safeZoomAmount)
      }
    }
    updatePositionInfo()
  }
}

// 缩小地图
const zoomOut = () => {
  if (viewer) {
    // 使用固定的缩小间隔为2000m
    const zoomAmount = 2000
    viewer.camera.zoomOut(zoomAmount)
    updatePositionInfo()
  }
}

// 重置视角
const resetView = () => {
  if (!viewer) return

  // 目标位置（从保存的位置中获取）
  const destination = Cesium.Cartesian3.fromDegrees(
    currentPosition.value.longitude,
    currentPosition.value.latitude,
    currentPosition.value.height
  )

  // 使用Cesium内置的flyTo方法，它内部使用了贝塞尔曲线实现平滑飞行
  viewer.camera.flyTo({
    destination: destination,
    orientation: {
      heading: currentPosition.value.heading,
      pitch: currentPosition.value.pitch,
      roll: currentPosition.value.roll
    },
    duration: 3.0, // 飞行持续3秒
    maximumHeight: currentPosition.value.height * 2, // 飞行过程中的最大高度
    pitchAdjustHeight: 10000, // 调整视角的高度阈值
    flyOverLongitude: undefined, // 自动选择最佳路径
    flyOverLongitudeWeight: 0, // 飞越经度的权重
    convert: true, // 自动转换坐标
    easingFunction: Cesium.EasingFunction.CUBIC_IN_OUT, // 使用三次贝塞尔缓动函数
    complete: () => {
      // 飞行完成后更新位置信息
      updatePositionInfo()
    }
  })
}

// 监听相机变化事件
let cameraChangeListener: any = null

// 添加状态变量
const isSelectingPoints = ref(false)
const selectedPoints = ref<Cesium.Cartesian3[]>([])
let pointSelectionHandler: Cesium.ScreenSpaceEventHandler | null = null
let dragHandler: Cesium.ScreenSpaceEventHandler | null = null
let isDragging = ref(false)
let draggedPointIndex = ref(-1)
// 添加临时连线和距离显示相关变量
let tempLineEntity: Cesium.Entity | null = null
let distanceDisplayEntity: Cesium.Entity | null = null
let mouseMoveHandler: Cesium.ScreenSpaceEventHandler | null = null
// 管理固定实体的数组，避免与临时实体冲突
let fixedEntities: Cesium.Entity[] = []

// 切换选点模式
const togglePointSelection = () => {
  isSelectingPoints.value = !isSelectingPoints.value

  if (isSelectingPoints.value) {
    startPointSelection()
    stopDragMode() // 关闭拖拽模式
  } else {
    stopPointSelection()
    startDragMode() // 开启拖拽模式
  }
}

// 开始选点
const startPointSelection = () => {
  if (!viewer) return

  // 设置鼠标样式
  viewer.canvas.style.cursor = 'crosshair'

  // 创建点击事件处理器
  pointSelectionHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas)

  pointSelectionHandler.setInputAction((movement: any) => {
    if (!viewer) return

    // 获取点击位置的坐标
    const ray = viewer.camera.getPickRay(movement.position)
    if (!ray) return

    const position = viewer.scene.globe.pick(ray, viewer.scene)
    if (!position) return

    // 添加点到数组
    selectedPoints.value.push(position)

    // 先处理鼠标移动处理器，避免临时连线被清除
    if (selectedPoints.value.length === 1) {
      // 如果这是第一个点，添加鼠标移动处理器来显示临时连线
      addMouseMoveHandler()
    } else {
      // 如果不是第一个点，更新临时线的起点为最后一个点
      updateMouseMoveHandler()
    }

    // 然后绘制点和线（这样临时连线不会被意外清除）
    if (selectedPoints.value.length > 0) {
      drawPointsAndLines()
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
}

// 添加鼠标移动处理器，显示从最后一个点到鼠标位置的临时连线
const addMouseMoveHandler = () => {
  if (!viewer || !pointSelectionHandler) return

  // 创建鼠标移动事件处理器
  mouseMoveHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas)

  mouseMoveHandler.setInputAction((movement: any) => {
    if (!viewer || selectedPoints.value.length === 0) return

    // 获取鼠标当前位置的坐标
    const ray = viewer.camera.getPickRay(movement.endPosition)
    if (!ray) return

    const mousePosition = viewer.scene.globe.pick(ray, viewer.scene)
    if (!mousePosition) return

    // 获取最后一个选中的点
    const lastPoint = selectedPoints.value[selectedPoints.value.length - 1]
    
    // 计算两点之间的距离（米）
    const distance = calculateDistance(lastPoint, mousePosition)
    
    // 计算线的中点位置
    const midPoint = Cesium.Cartesian3.midpoint(lastPoint, mousePosition, new Cesium.Cartesian3())

    // 优化临时线实体的更新，减少闪烁
    if (tempLineEntity && tempLineEntity.polyline) {
      // 直接更新现有实体的位置
      tempLineEntity.polyline.positions = new Cesium.ConstantProperty([lastPoint, mousePosition])
    } else {
      // 如果临时线实体不存在，先移除旧的再创建新的
      if (tempLineEntity) {
        viewer.entities.remove(tempLineEntity)
      }
      tempLineEntity = viewer.entities.add({
        polyline: {
          positions: [lastPoint, mousePosition],
          width: 2,
          material: new Cesium.ColorMaterialProperty(Cesium.Color.BLUE.withAlpha(0.7))
        }
      })
    }

    // 优化距离显示实体的更新，减少闪烁
    if (distanceDisplayEntity && distanceDisplayEntity.label) {
      // 直接更新现有实体的位置和文本
      distanceDisplayEntity.position = new Cesium.ConstantPositionProperty(midPoint)
      distanceDisplayEntity.label.text = new Cesium.ConstantProperty(`${distance.toFixed(2)}m`)
    } else {
      // 如果距离显示实体不存在，先移除旧的再创建新的
      if (distanceDisplayEntity) {
        viewer.entities.remove(distanceDisplayEntity)
      }
      distanceDisplayEntity = viewer.entities.add({
        position: midPoint,
        label: {
          text: `${distance.toFixed(2)}m`,
          font: '12pt sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.CENTER,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          pixelOffset: new Cesium.Cartesian2(0, 15),
          backgroundColor: Cesium.Color.DARKBLUE.withAlpha(0.7),
          backgroundPadding: new Cesium.Cartesian2(7, 5)
        }
      })
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)
}

// 更新鼠标移动处理器
const updateMouseMoveHandler = () => {
  // 如果已有鼠标移动处理器，先移除
  if (mouseMoveHandler) {
    mouseMoveHandler.destroy()
    mouseMoveHandler = null
  }
  
  // 重新添加鼠标移动处理器
  addMouseMoveHandler()
}

// 计算两点之间的距离（米）
const calculateDistance = (point1: Cesium.Cartesian3, point2: Cesium.Cartesian3): number => {
  // 将笛卡尔坐标转换为地理坐标
  const ellipsoid = viewer.scene.globe.ellipsoid
  const cartographic1 = ellipsoid.cartesianToCartographic(point1)
  const cartographic2 = ellipsoid.cartesianToCartographic(point2)
  
  // 使用Cesium提供的geodesic计算地表距离
  const geodesic = new Cesium.EllipsoidGeodesic(cartographic1, cartographic2, ellipsoid)
  return geodesic.surfaceDistance
}

// 停止选点
const stopPointSelection = () => {
  if (!viewer) return

  // 恢复鼠标样式
  viewer.canvas.style.cursor = ''

  // 移除事件处理器
  if (pointSelectionHandler) {
    pointSelectionHandler.destroy()
    pointSelectionHandler = null
  }
  
  // 移除鼠标移动处理器
  if (mouseMoveHandler) {
    mouseMoveHandler.destroy()
    mouseMoveHandler = null
  }
  
  // 移除临时线和距离显示
  if (tempLineEntity && viewer) {
    viewer.entities.remove(tempLineEntity)
    tempLineEntity = null
  }
  
  if (distanceDisplayEntity && viewer) {
    viewer.entities.remove(distanceDisplayEntity)
    distanceDisplayEntity = null
  }
}

// 开始拖拽模式
const startDragMode = () => {
  if (!viewer || selectedPoints.value.length === 0) return

  // 清理临时连线和距离显示
  if (tempLineEntity && viewer) {
    viewer.entities.remove(tempLineEntity)
    tempLineEntity = null
  }
  
  if (distanceDisplayEntity && viewer) {
    viewer.entities.remove(distanceDisplayEntity)
    distanceDisplayEntity = null
  }

  // 创建拖拽事件处理器
  dragHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas)

  // 鼠标移动时检测是否悬停在点上
  dragHandler.setInputAction((movement: any) => {
    if (!viewer || isSelectingPoints.value || isDragging.value) return

    // 获取鼠标下的实体
    const pickedObject = viewer.scene.pick(movement.endPosition)

    if (pickedObject && pickedObject.id && pickedObject.id.point) {
      // 鼠标悬停在点上，改变鼠标样式
      viewer.canvas.style.cursor = 'move'
    } else {
      // 不在点上时恢复默认样式
      viewer.canvas.style.cursor = ''
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)

  // 鼠标按下时，检测是否在点上
  dragHandler.setInputAction((movement: any) => {
    if (!viewer || isSelectingPoints.value) return

    // 获取点击的实体
    const pickedObject = viewer.scene.pick(movement.position)

    if (pickedObject && pickedObject.id && pickedObject.id.point) {
      // 找到被选中的点的索引
      const pointId = pickedObject.id.id
      if (typeof pointId === 'number') {
        draggedPointIndex.value = pointId
        isDragging.value = true
        viewer.canvas.style.cursor = 'grabbing'

        // 关键：禁用地图默认的导航控制，防止拖拽点时地图也跟着移动
        viewer.scene.screenSpaceCameraController.enableRotate = false
        viewer.scene.screenSpaceCameraController.enableTranslate = false
        viewer.scene.screenSpaceCameraController.enableZoom = false
        viewer.scene.screenSpaceCameraController.enableTilt = false
        viewer.scene.screenSpaceCameraController.enableLook = false
      }
    }
  }, Cesium.ScreenSpaceEventType.LEFT_DOWN)

  // 鼠标移动时，如果正在拖拽，更新点的位置
  dragHandler.setInputAction((movement: any) => {
    if (!viewer || !isDragging.value || draggedPointIndex.value === -1) return

    // 获取鼠标当前位置对应的地球表面坐标
    const ray = viewer.camera.getPickRay(movement.endPosition)
    if (!ray) return

    const newPosition = viewer.scene.globe.pick(ray, viewer.scene)
    if (!newPosition) return

    // 更新点的位置
    if (draggedPointIndex.value >= 0 && draggedPointIndex.value < selectedPoints.value.length) {
      selectedPoints.value[draggedPointIndex.value] = newPosition

      // 立即重新绘制，确保点位跟随鼠标移动
      drawPointsAndLines()
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)

  // 鼠标松开时，结束拖拽
  dragHandler.setInputAction(() => {
    if (isDragging.value) {
      isDragging.value = false
      draggedPointIndex.value = -1
      viewer.canvas.style.cursor = ''

      // 恢复地图导航控制
      if (viewer) {
        viewer.scene.screenSpaceCameraController.enableRotate = true
        viewer.scene.screenSpaceCameraController.enableTranslate = true
        viewer.scene.screenSpaceCameraController.enableZoom = true
        viewer.scene.screenSpaceCameraController.enableTilt = true
        viewer.scene.screenSpaceCameraController.enableLook = true
      }
      
      // 重新绘制点和线，使点颜色从黄色变回红色
      drawPointsAndLines()
    }
  }, Cesium.ScreenSpaceEventType.LEFT_UP)
}

// 停止拖拽模式
const stopDragMode = () => {
  if (dragHandler) {
    dragHandler.destroy()
    dragHandler = null
  }
  isDragging.value = false
  draggedPointIndex.value = -1
}

// 优化绘制点和线的函数，避免闪烁问题
const drawPointsAndLines = () => {
  if (!viewer) return

  // 清除之前的固定实体（不影响临时实体）
  clearFixedEntities()

  // 预先计算所有点的位置
  const positions = selectedPoints.value.map(point => {
    const cartographic = Cesium.Cartographic.fromCartesian(point)
    return Cesium.Cartesian3.fromRadians(cartographic.longitude, cartographic.latitude, 200)
  })

  // 绘制所有点
  selectedPoints.value.forEach((point, index) => {
    const cartographic = Cesium.Cartographic.fromCartesian(point)
    const groundPosition = Cesium.Cartesian3.fromRadians(cartographic.longitude, cartographic.latitude, 0)

    // 创建点实体
    const pointEntity = viewer.entities.add({
      id: index,
      position: positions[index],
      point: {
        pixelSize: 15,
        color: isDragging.value && index === draggedPointIndex.value ? Cesium.Color.YELLOW : Cesium.Color.RED,
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 2,
        disableDepthTestDistance:
          isDragging.value && index === draggedPointIndex.value ? Number.POSITIVE_INFINITY : undefined
      },
      label: {
        text: `点 ${index + 1}`,
        font: '14pt sans-serif',
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        outlineWidth: 2,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        pixelOffset: new Cesium.Cartesian2(0, -15)
      }
    })
    fixedEntities.push(pointEntity)

    // 绘制垂直于地面的蓝线
    const verticalLineEntity = viewer.entities.add({
      polyline: {
        positions: [groundPosition, positions[index]],
        width: 5,
        material: new Cesium.ColorMaterialProperty(Cesium.Color.BLUE),
        clampToGround: false
      }
    })
    fixedEntities.push(verticalLineEntity)
  })

  // 绘制连线（使用预先计算的positions数组）
  if (selectedPoints.value.length > 1) {
    // 绘制连线
    const connectionLineEntity = viewer.entities.add({
      name: '连线',
      polyline: {
        positions: positions,
        width: 5,
        material: new Cesium.ColorMaterialProperty(Cesium.Color.BLUE),
        clampToGround: false
      }
    })
    fixedEntities.push(connectionLineEntity)

    // 在每段线上显示距离
    for (let i = 0; i < selectedPoints.value.length - 1; i++) {
      const startPoint = selectedPoints.value[i]
      const endPoint = selectedPoints.value[i + 1]

      // 计算距离
      const distance = calculateDistance(startPoint, endPoint)

      // 计算线段中点
      const midPoint = Cesium.Cartesian3.midpoint(
        positions[i],
        positions[i + 1],
        new Cesium.Cartesian3()
      )

      // 添加距离标签
      const distanceLabelEntity = viewer.entities.add({
        position: midPoint,
        label: {
          text: `${distance.toFixed(2)}m`,
          font: '12pt sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.CENTER,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          pixelOffset: new Cesium.Cartesian2(0, 15),
          backgroundColor: Cesium.Color.DARKBLUE.withAlpha(0.7),
          backgroundPadding: new Cesium.Cartesian2(7, 5)
        }
      })
      fixedEntities.push(distanceLabelEntity)
    }
  }
}

// 清除固定实体的函数
const clearFixedEntities = () => {
  if (!viewer) return

  fixedEntities.forEach(entity => {
    viewer.entities.remove(entity)
  })
  fixedEntities = []
}

// 添加清除所有点位和线的函数
const clearAllPoints = () => {
  if (!viewer) return

  // 清空选中的点数组
  selectedPoints.value = []

  // 重置拖拽相关状态
  isDragging.value = false
  draggedPointIndex.value = -1

  // 如果当前在选点模式，先停止选点
  if (isSelectingPoints.value) {
    stopPointSelection()
    isSelectingPoints.value = false
  }

  // 移除临时连线和距离显示
  if (tempLineEntity) {
    viewer.entities.remove(tempLineEntity)
    tempLineEntity = null
  }

  if (distanceDisplayEntity) {
    viewer.entities.remove(distanceDisplayEntity)
    distanceDisplayEntity = null
  }

  // 清除固定实体（不影响临时实体）
  clearFixedEntities()
}

// 保存航线相关
const saveRouteModalVisible = ref(false)
const saveRouteLoading = ref(false)

const routeForm = reactive({
  name: '',
  description: '',
  type: 'inspection'
})

// 保存当前航线
const saveCurrentRoute = () => {
  if (selectedPoints.value.length < 2) {
    message.warning('至少需要两个点位才能保存航线')
    return
  }
  
  saveRouteModalVisible.value = true
}

// 确认保存航线
const confirmSaveRoute = async (formData: any) => {
  try {
    saveRouteLoading.value = true
    
    // 准备点位数据
    const pointsData = selectedPoints.value.map(point => {
      const ellipsoid = viewer.scene.globe.ellipsoid
      const cartographic = ellipsoid.cartesianToCartographic(point)
      return {
        longitude: Cesium.Math.toDegrees(cartographic.longitude),
        latitude: Cesium.Math.toDegrees(cartographic.latitude),
        height: cartographic.height
      }
    })
    
    // 计算总距离
    let totalDistance = 0
    for (let i = 0; i < selectedPoints.value.length - 1; i++) {
      totalDistance += calculateDistance(selectedPoints.value[i], selectedPoints.value[i + 1])
    }
    
    // 构建航线数据对象
    const routeData = {
      id: Date.now().toString(), // 临时ID，真实环境下应由后端生成
      name: formData.name,
      description: formData.description,
      type: formData.type,
      pointsCount: pointsData.length,
      distance: Math.round(totalDistance),
      createTime: new Date().toLocaleDateString(),
      points: pointsData
    }
    
    // 这里应该调用API保存数据
    console.log('保存航线数据:', routeData)
    
    // 模拟保存成功
    message.success('航线保存成功')
    
    saveRouteModalVisible.value = false
    saveRouteLoading.value = false
  } catch (error) {
    console.error('保存航线失败:', error)
    saveRouteLoading.value = false
  }
}

onMounted(() => {
  document.title = '测试航线 - 无人载具与作业管理平台'

  // 初始更新位置信息
  if (viewer) {
    // 获取当前位置
    getCurrentPosition()

    updatePositionInfo()

    // 添加相机移动结束事件监听器
    cameraChangeListener = viewer.camera.moveEnd.addEventListener(() => {
      updatePositionInfo()
    })

    // 初始化拖拽模式
    startDragMode()
  }
})

onUnmounted(() => {
  // 移除事件监听器
  stopPointSelection()
  stopDragMode()
  
  // 移除鼠标移动处理器
  if (mouseMoveHandler) {
    mouseMoveHandler.destroy()
    mouseMoveHandler = null
  }
  
  // 清理临时连线和距离显示
  if (tempLineEntity && viewer) {
    viewer.entities.remove(tempLineEntity)
    tempLineEntity = null
  }
  
  if (distanceDisplayEntity && viewer) {
    viewer.entities.remove(distanceDisplayEntity)
    distanceDisplayEntity = null
  }
  
  if (viewer && cameraChangeListener) {
    cameraChangeListener()
  }
})
</script>

<style lang="scss" scoped>
.test-route-container {
  width: 100%;
  height: 100%;
  display: flex;
  background: rgb(210, 210, 214);

  .left-panel {
    width: 350px;
    height: 100%;
    flex-shrink: 0;
  }

  .map-content-wrapper {
    flex: 1;
    height: 100%;
    position: relative;
  }

  .control-btn {
    margin: 5px 0;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.4);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.4);
    }

    &.ant-btn-primary {
      background: rgba(24, 144, 255, 0.8);
      border-color: rgba(24, 144, 255, 1);
    }

    &.selecting-point {
      border: 2px solid #1890ff !important;
      background: rgba(24, 144, 255, 0.3) !important;
    }
  }

  .map-wrap {
    width: 100%;
    height: 100%;
  }

  .right-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 120px;
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
    border-radius: 4px;
    z-index: 100;

    .control-panel {
      padding: 10px 0;

      h4 {
        color: #fff;
        font-size: 14px;
        text-align: center;
        margin-bottom: 10px;
      }

      .control-buttons {
        display: flex;
        flex-direction: column;
        align-items: center;

        .control-btn {
          margin: 5px 0;
          width: 36px;
          height: 36px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(255, 255, 255, 0.2);
          color: #fff;
          border: 1px solid rgba(255, 255, 255, 0.4);

          &:hover {
            background: rgba(255, 255, 255, 0.4);
          }
        }
      }

      .position-info {
        margin-top: 10px;
        padding: 5px;
        font-size: 12px;
        text-align: center;
        border-top: 1px solid rgba(255, 255, 255, 0.2);

        p {
          margin: 5px 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

/* 全局样式，确保弹窗中的文字颜色清晰可见 */
.ant-modal {
  .ant-modal-content {
    background-color: #fff;
    
    .ant-modal-header {
      background-color: #fff;
      border-bottom: 1px solid #f0f0f0;
      
      .ant-modal-title {
        color: rgba(0, 0, 0, 0.85);
        font-weight: bold;
      }
    }
    
    .ant-modal-body {
      .ant-form-item-label > label {
        color: rgba(0, 0, 0, 0.85);
      }
      
      .ant-input, .ant-select-selector, .ant-input-number {
        background-color: #fff !important;
        color: rgba(0, 0, 0, 0.85) !important;
        border-color: #d9d9d9 !important;
      }
      
      .ant-select-selection-placeholder,
      .ant-input::placeholder {
        color: rgba(0, 0, 0, 0.45) !important;
      }
    }
    
    .ant-modal-footer {
      border-top: 1px solid #f0f0f0;
      
      .ant-btn {
        color: rgba(0, 0, 0, 0.85);
        
        &.ant-btn-primary {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #fff;
        }
      }
    }
  }
}
</style>
