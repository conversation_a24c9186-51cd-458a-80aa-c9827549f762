<template>
  <div class="cesiumMapContainer">
    <AlarmPop v-if="popFlag && popIsShow" ref="mapPopup" :popupData="popData"></AlarmPop>
    <div id="cesiumContainer" class="cesiumContainer" ref="cesiumContainer"></div>
  </div>
</template>

      
<script lang="ts" setup >
import {
  watch,
  onMounted,
  onBeforeUnmount,
  ref
} from 'vue'
import { useRoute } from 'vue-router';
import { initCesium, viewer } from '@/components/cesiumMap/initCesium';
import { useCesium } from '@/components/cesiumMap/useCesium';
import iconPop from '@/components/cesiumMap/IconPop.vue';
import AlarmPop from './AlarmPop.vue';
import detection1 from "@/assets/images/detection1.png";
import detection2 from "@/assets/images/detection2.png";
import detection3 from "@/assets/images/detection3.png";
import airPoint1 from "@/assets/images/airPoint1.png";
import airPoint2 from "@/assets/images/airPoint2.png";
import airPoint3 from "@/assets/images/airPoint3.png";
import pointIcon from "@/assets/images/point.png";

initCesium()
const { popFlag, popData, mapPopup, closeMapPopup, addTrack, addSampleTrack, clearMap, addSample, addpoint, addpointNoLabel, setEvent, setPopEvent } = useCesium();

// 定义props
const props = defineProps({
  // 坐标点
  pointsArr: {
    type: Array,
    default: [],
  },
  alarmPointsArr: {
    type: Array,
    default: [],
  },
  // 轨迹数据
  trackData: {
    type: Array,
    default: [],
  },
  // 带移动样本的轨迹数据
  sampleTrackData: {
    type: Array,
    default: [],
  },
  // 移动样本运动点数据
  samplePointsData: {
    type: Array,
    default: [],
  },
  routeNow: {
    type: String,
    default: '',
  }
});

const route = useRoute();

watch(() => route.path, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    clearMap()
  }
})

const popIsShow = ref(false);
watch(() => props.pointsArr, (newValue, oldValue) => {
  if (newValue.length > 0) {
    if(props.routeNow === 'projectInfo') {
      setPoint(newValue)
    } else {
      popIsShow.value = true
      setPointWithStatus(newValue)
    }
  }
});

watch(() => props.alarmPointsArr, (newValue, oldValue) => {
  if (newValue.length > 0) {
    popIsShow.value = true
    setAlarmPointWithStatus(newValue)
  }
});

watch(() => props.trackData, (newValue, oldValue) => {
  if (newValue.length > 0) {
    // console.log(newValue);
    // const aaa = []
    // newValue.forEach((item: any, index: number) => {
    //   if(index > 0) {
    //     if(Math.abs(item.lon - newValue[index - 1].lon)*10000 > 1) {
    //       console.log(item.triggerTime);
    //       console.log(Math.abs(item.lon - newValue[index - 1].lon) * 10000);
    //       console.log(index);
    //     } else {
    //       // aaa.push(item)
    //     }
    //   }
    // });
    // console.log(aaa);
    addTrack(newValue)
  }
});

watch(() => props.sampleTrackData, (newValue, oldValue) => {
  if (newValue.length > 0) {
    addSampleTrack(newValue)
  }
});

watch(() => props.samplePointsData, (newValue, oldValue) => {
  if (newValue.length > 0) {
    addSample(newValue)
  }
});

const setPoint = (points: any) => {
  points.forEach((point: any) => {
    addpoint(point, pointIcon, '');
  });
};

const setPointWithStatus = (points: any) => {
  points.forEach((point: any) => {
    if(point.alarmVO) {
      if (point.alarmVO.maxValue && point.alarmVO.gradeId) {
        addpoint(point, airPoint3, '#CE1A0D');
      } else if (point.alarmVO.maxValue && !point.alarmVO.gradeId) {
        addpoint(point, airPoint2, '#C8860D');
      } else {
        addpoint(point, airPoint1, '#0C9815');
      }
    } else {
      addpoint(point, airPoint1, '#0C9815');
    }
    setEvent()
  });
};

const setAlarmPointWithStatus = (points: any) => {
  points.forEach((point: any) => {
    if (point.maxValue && point.gradeId) {
      addpointNoLabel(point, detection3);
    } else if (point.maxValue && !point.gradeId) {
      addpointNoLabel(point, detection2);
    } else {
      addpointNoLabel(point, detection1);
    }
    setEvent()
    setPopEvent()
  });
};

onMounted(() => {
  if(props.pointsArr.length > 0) {
    if (props.routeNow === 'projectInfo') {
      setPoint(props.pointsArr)
    } else {
      setPointWithStatus(props.pointsArr)
    }
  }
  if (props.trackData.length > 0) {
    addTrack(props.trackData)
  }
});

onBeforeUnmount(() => {
  if(viewer) {
    viewer.destroy();
  }
});


</script>
    
<style scoped>
.cesiumMapContainer{
  width: 100%;
  height: 100%;
}
.cesiumContainer {
  width: 100%;
  height: 100%;
}
</style>
