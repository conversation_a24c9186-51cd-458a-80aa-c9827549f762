<template>
  <div class="rule ruleContainer">
    <div class="handleWrap clearfix">
      <div class="left fl">
        <a-form layout="inline" :model="searchFormState">
          <a-form-item>
            <a-input v-model:value="searchFormState.name" placeholder="请输入报警规则名称" size="small" allowClear @pressEnter="onSearch"></a-input>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="onSearch" v-privilege="'alarmRule:query'">搜索</a-button>
          </a-form-item>
        </a-form>
      </div>
      <div class="right fr">
        <a-button type="primary" @click="addHandle" v-privilege="'alarmRule:add'">
          <template #icon>
            <PlusOutlined />
          </template>
          新增
        </a-button>
      </div>
    </div>
    <div class="contentWrap custom-scrollbar" ref="tableWrapperRef">
      <a-table
        ref="tableRef"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="false"
        rowKey="id"
        bordered
        :scroll="{ x: 1200, y: tableHeight }"
        :loading="tableLoading"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'operation'">
            <span>
              <a-button type="link" size="small" @click="editHandle(record)" v-privilege="'alarmRule:update'">编辑</a-button>
              <a-button type="link" size="small" danger @click="delHandle(record)" v-privilege="'alarmRule:delete'">删除</a-button>
            </span>
          </template>
        </template>
      </a-table>
    </div>
    <div class="pageWrap">
      <a-pagination
        size="small"
        v-model:current="pagination.pageNum"
        :page-size="pagination.pageSize"
        :total="total"
        show-size-changer
        show-quick-jumper
        :show-total="(total: number) => `共 ${total} 条`"
        :page-size-options="PAGE_SIZE_OPTIONS"
        @change="(current: number, size: number) => onPageChange('current', current, size)"
        @showSizeChange="(current: number, size: number) => onPageChange('size', current, size)"
      />
    </div>
    <a-modal
      :open="addEditModalState.visible"
      :title="addEditModalState.type === 'add' ? '新增报警规则' : '编辑报警规则'"
      width="800px"
      cancelText="取消"
      okText="提交"
      :closable="false"
      :keyboard="false"
      :maskClosable="false"
      @cancel="handleCancle"
      @ok="handleOk"
    >
      <a-form class="ruleHandleForm" ref="ruleFormRef" :model="ruleFormState" :rules="ruleRules" :labelCol="{ span: 5 }">
        <a-form-item ref="alarmCode" label="报警规则编码" name="alarmCode">
          <a-input v-model:value="ruleFormState.alarmCode" :disabled="addEditModalState.type === 'edit'" :maxlength="30" />
        </a-form-item>
        <a-form-item ref="name" label="报警规则名称" name="name">
          <a-input v-model:value="ruleFormState.name" :maxlength="30" />
        </a-form-item>
        <a-form-item ref="detectTarget" label="检测目标" name="detectTarget">
          <DictSelect dict-code="detect_target" v-model:value="ruleFormState.detectTarget" width="100%" />
        </a-form-item>
        <a-form-item ref="unit" label="检测介质单位" name="unit">
          <a-input v-model:value="ruleFormState.unit" :maxlength="30" />
        </a-form-item>
        <a-form-item ref="upperLimit" label="单位默认上限" name="upperLimit">
          <a-input v-model:value="ruleFormState.upperLimit" :maxlength="30" />
        </a-form-item>
        <a-form-item ref="lowerLimit" label="单位默认下限" name="lowerLimit">
          <a-input v-model:value="ruleFormState.lowerLimit" :maxlength="30" />
        </a-form-item>
        <a-form-item
          label="风险等级阈值"
          name="alarmRuleGradeAddFormList"
          :rules="{
                required: true,
                message: ''
              }"
        >
          <div class="targetItem" v-for="(item, index) in ruleFormState.alarmRuleGradeAddFormList" :key="index" style="display: flex;">
            <div style="width: 20%; margin-right: 2%;">
              <a-form-item
                ref="gradeCode"
                label
                :name="['alarmRuleGradeAddFormList', index, 'gradeCode']"
                :rules="[{
                required: true,
                message: '请输入编码'
              },{
                pattern: /^[^\u4e00-\u9fa5]*$/,
                message: '编码中不能包含汉字'
              }]"
              >
                <a-input
                  v-model:value="ruleFormState.alarmRuleGradeAddFormList[index].gradeCode"
                  placeholder="编码"
                  style="width: 100%;"
                  :maxlength="30"
                />
              </a-form-item>
            </div>
            <div style="width: 30%;margin-right: 2%;">
              <a-form-item
                ref="gradeName"
                label
                :name="['alarmRuleGradeAddFormList', index, 'gradeName']"
                :rules="{
                required: true,
                message: '请输入名称'
              }"
              >
                <a-input
                  v-model:value="ruleFormState.alarmRuleGradeAddFormList[index].gradeName"
                  placeholder="名称"
                  style="width: 100%;"
                  :maxlength="30"
                />
              </a-form-item>
            </div>
            <div style="width: 30%;">
              <a-form-item
                ref="alarmValue"
                label
                :name="['alarmRuleGradeAddFormList', index, 'alarmValue']"
                :rules="{
                required: true,
                message: '请输入阈值'
              }"
              >
                <!-- <a-input
                  v-model:value="ruleFormState.alarmRuleGradeAddFormList[index].alarmValue"
                  placeholder="阈值"
                  style="width: 100%;"
                  :maxlength="30"
                />-->
                <a-input-number
                  v-model:value="ruleFormState.alarmRuleGradeAddFormList[index].alarmValue"
                  :min="0"
                  :max="1000000"
                  style="width: 100%;"
                  :precision="0"
                  v-input-number-change
                  placeholder="阈值"
                />
              </a-form-item>
            </div>

            <span style="padding-top: 7px;">
              <DeleteOutlined
                v-if="ruleFormState.alarmRuleGradeAddFormList.length > 1"
                title="删除目标"
                @click="deleteAlarmRuleGradeAddFormItem(index)"
              />
            </span>
            <span style="padding-top: 7px;">
              <PlusCircleOutlined
                v-if="index == ruleFormState.alarmRuleGradeAddFormList.length - 1"
                title="添加目标"
                @click="addAlarmRuleGradeAddFormItem"
              />
            </span>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import {
  PlusOutlined,
  SlackOutlined,
  MoreOutlined,
  FieldTimeOutlined,
  UserOutlined,
  ExclamationCircleOutlined,
  EnvironmentOutlined,
  DeleteOutlined,
  PlusCircleOutlined
} from '@ant-design/icons-vue'
import DictSelect from '@/components/common/dict-select/index.vue'
import moment, { Moment } from 'moment'
import { Modal, message } from 'ant-design-vue'
import { ValidateErrorEntity } from 'ant-design-vue/es/form/interface'
import { reactive, ref, UnwrapRef, createVNode, onMounted } from 'vue'
import { ruleApi } from '@/api/manageTool/ruleApi'
import { PageParamModel } from '@/types/paging'
import { PAGE_SIZE_OPTIONS } from '@/utils/constants'
import { getTableHeight } from '@/hooks/getTableHeight'
import _ from 'lodash'
import { regular } from '@/utils/regular'
import { Item } from 'ant-design-vue/es/menu'
// 注册组件
defineOptions({
  components: {
    PlusOutlined,
    SlackOutlined,
    MoreOutlined,
    FieldTimeOutlined,
    UserOutlined,
    EnvironmentOutlined,
    DeleteOutlined,
    PlusCircleOutlined,
    DictSelect
  }
})

/* ====================== 表格 ====================== */
const tableWrapperRef = ref()
const tableRef = ref()
const tableHeight = getTableHeight(tableWrapperRef, tableRef)

const tableLoading = ref(false)
const columns = reactive([
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    customRender: ({ index }: any) => (pagination.value.pageNum - 1) * pagination.value.pageSize + index + 1,
    fixed: 'left',
    width: 80,
    align: 'center'
  },
  {
    title: '报警规则名称',
    dataIndex: 'name',
    key: 'name',
    fixed: 'left',
    width: 160,
    align: 'center'
  },
  {
    title: '报警规则编号',
    dataIndex: 'alarmCode',
    key: 'alarmCode',
    width: 160,
    align: 'center'
  },
  {
    title: '检测目标',
    dataIndex: 'detectTargetName',
    key: 'detectTargetName',
    width: 160,
    align: 'center'
  },
  {
    title: '单位',
    dataIndex: 'unit',
    key: 'unit',
    width: 160,
    align: 'center'
  },
  {
    title: '上限值',
    dataIndex: 'upperLimit',
    key: 'upperLimit',
    width: 160,
    align: 'center'
  },
  {
    title: '下限值',
    dataIndex: 'lowerLimit',
    key: 'lowerLimit',
    width: 160,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 200,
    align: 'center'
  },
  {
    title: '操作',
    key: 'operation',
    dataIndex: 'operation',
    // slots: { customRender: 'operation' },
    fixed: 'right',
    width: 130,
    align: 'center'
  }
])
const dataSource = ref([])

/* ====================== 分页 ====================== */
const pagination = ref<PageParamModel>({
  pageNum: 1, // 当前页码
  pageSize: 10, // 每页数量
  sortItemList: [
    {
      isAsc: false,
      column: 'create_time'
    }
  ]
})
const total = ref<number>(0)
const onPageChange = (type: string, pageNum: number, pageSize: number) => {
  console.log(pageNum, pageSize)
  if (type === 'current') {
    pagination.value.pageNum = pageNum
  } else {
    pagination.value.pageNum = 1
    pagination.value.pageSize = pageSize
  }
  queryAlarmRulePage()
}

/* ====================== 搜索 ====================== */
interface SearchFormState {
  name: string
}
const searchFormState: UnwrapRef<SearchFormState> = reactive({
  name: ''
})

const queryAlarmRulePage = async () => {
  try {
    tableLoading.value = true
    const result = await ruleApi.queryAlarmRulePage({
      ...pagination.value,
      ...searchFormState
    })
    console.log(result)
    dataSource.value = result.data.list
    total.value = result.data.total
  } catch (e) {
    console.log(e)
    dataSource.value = []
    total.value = 0
  } finally {
    tableLoading.value = false
  }
}

const onSearch = () => {
  console.log(searchFormState.name)
  pagination.value.pageNum = 1
  total.value = 0
  dataSource.value = []
  queryAlarmRulePage()
}

/* ====================== 表单（新增|编辑） ====================== */
interface alarmRuleGradeAddForm {
  // 等级编码
  gradeCode: string
  // 等级名称
  gradeName: string
  // 报警值
  alarmValue: number | null
}

interface RuleFormState {
  id?: number | null
  // 报警规则名称
  name: string
  // 报警规则编码
  alarmCode: string
  // 检测目标
  detectTarget: string
  // 单位
  unit: string
  // 上限
  upperLimit: number | null
  // 下限
  lowerLimit: number | null
  // 风险等级阈值
  alarmRuleGradeAddFormList: Array<alarmRuleGradeAddForm>
}
const ruleFormStateModel = {
  id: null,
  name: '',
  alarmCode: '',
  detectTarget: '',
  unit: '',
  upperLimit: null,
  lowerLimit: null,
  alarmRuleGradeAddFormList: [{ gradeCode: '', gradeName: '', alarmValue: null }]
}
const ruleFormState = ref<RuleFormState>(_.cloneDeep(ruleFormStateModel))

const ruleFormRef = ref()
const ruleRules = reactive({
  alarmCode: [{ required: true, message: '请输入报警规则编码' }],
  name: [{ required: true, message: '请输入报警规则名称' }],
  detectTarget: [{ required: true, message: '请选择检测目标' }],
  unit: [{ required: true, message: '请输入检测介质单位' }],
  upperLimit: [{ pattern: regular.isNumber1, message: '请输入数字' }],
  lowerLimit: [{ pattern: regular.isNumber1, message: '请输入数字' }]
})
interface AddEditModalModel {
  visible: boolean
  type: '' | 'add' | 'edit'
}
const addEditModalState = ref<AddEditModalModel>({
  visible: false,
  type: ''
})

const handleCancle = () => {
  addEditModalState.value.visible = false
  ruleFormState.value = _.cloneDeep(ruleFormStateModel)
  ruleFormState.value.alarmRuleGradeAddFormList = []
  ruleFormRef.value.clearValidate()
}
const handleOk = () => {
  ruleFormRef.value
    .validate()
    .then(async () => {
      console.log('values', ruleFormState)
      const params = {
        ...ruleFormState.value,
        alarmRuleGradeAddFormList: _.cloneDeep(ruleFormState.value.alarmRuleGradeAddFormList).sort((a, b) => a.alarmValue - b.alarmValue)
      }
      // for (const key in params) {
      //   console.log(key)
      //   if (params[key] === '') delete params[key]
      // }
      if (addEditModalState.value.type === 'add') {
        await ruleApi.addAlarmRule(params)
        message.success('新增成功')
        searchFormState.name = ''
        onSearch()
      } else {
        await ruleApi.updateAlarmRule(params)
        message.success('编辑成功')
        queryAlarmRulePage()
      }
      ruleFormState.value = _.cloneDeep(ruleFormStateModel)
      ruleFormState.value.alarmRuleGradeAddFormList = []
      addEditModalState.value.visible = false
      ruleFormRef.value.clearValidate()
      // console.log(dictConfigFormState.value)
    })
    .catch((error: ValidateErrorEntity<RuleFormState>) => {
      // console.log('error', error)
      // addEditModalState.value.visible = false
    })
}

/* ====================== 操作按钮 ====================== */
const addHandle = () => {
  ruleFormState.value = _.cloneDeep(ruleFormStateModel)
  addEditModalState.value.type = 'add'
  addEditModalState.value.visible = true
}
const editHandle = (item: any) => {
  ruleFormState.value = {
    id: item.id,
    name: item.name,
    alarmCode: item.alarmCode,
    detectTarget: item.detectTarget,
    unit: item.unit,
    upperLimit: item.upperLimit,
    lowerLimit: item.lowerLimit,
    alarmRuleGradeAddFormList:
      item.alarmRuleGradeVOList && item.alarmRuleGradeVOList.length
        ? _.cloneDeep(item.alarmRuleGradeVOList)
        : _.cloneDeep(ruleFormStateModel.alarmRuleGradeAddFormList)
  }
  addEditModalState.value.type = 'edit'
  addEditModalState.value.visible = true
}
const delHandle = (item: any) => {
  console.log(item)
  Modal.confirm({
    title: () => '删除提醒',
    icon: () => createVNode(ExclamationCircleOutlined),
    content: () => '确定是否删除该报警规则？',
    okText: () => '确认',
    okType: 'danger',
    cancelText: () => '取消',
    onOk: async () => {
      try {
        await ruleApi.deleteAlarmRule(item.id)
        message.success('删除成功')
        if (pagination.value.pageNum > 1 && dataSource.value.length === 1) {
          pagination.value.pageNum = pagination.value.pageNum - 1
        }
        queryAlarmRulePage()
      } catch (e) {
        console.log(e)
      }
    },
    onCancel() {
      console.log('Cancel')
    }
  })
}
const deleteAlarmRuleGradeAddFormItem = (index: number) => {
  ruleFormState.value.alarmRuleGradeAddFormList.splice(index, 1)
}
const addAlarmRuleGradeAddFormItem = () => {
  if (ruleFormState.value.alarmRuleGradeAddFormList.length >= 20) {
    return message.error('最多可添加20组')
  }
  ruleFormState.value.alarmRuleGradeAddFormList.push({ gradeCode: '', gradeName: '', alarmValue: null })
}

onMounted(() => {
  queryAlarmRulePage()
})
</script>
<style lang="scss">
.ruleContainer {
  height: 100%;

  .handleWrap {
    height: 54px;
    padding: 10px;
    box-sizing: border-box;

    .left {
      width: 80%;

      .ant-input {
        box-sizing: border-box;
        width: 240px;
        height: 34px;
      }

      .ant-btn {
        height: 34px;
      }
    }

    .right {
      width: 20%;
      text-align: right;

      .ant-btn {
        height: 34px;
      }
    }
  }

  .contentWrap {
    height: calc(100% - 110px);
    padding: 10px;
    overflow: hidden;

    .ant-card {
      height: 100%;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      border-radius: 6px;
      cursor: default;

      &:hover {
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
      }

      .ant-card-body {
        padding: 15px;
      }

      .cardHead {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #000;

        .headIcon {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          background: #d1cdfc;
          text-align: center;
          min-width: 50px;

          .anticon-slack {
            font-size: 30px;
            color: #200de7;
            width: 30px;
            height: 30px;
            margin-top: 10px;
          }
        }

        .cardTitle {
          font-size: 14px;
          font-weight: 600;
          margin: 0 10px;
        }

        .ant-dropdown-link {
          color: #000;

          &:hover {
            color: #1890ff;
          }
        }
      }

      .cardInfo {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #999;
        margin: 10px 0;

        .anticon {
          margin-right: 4px;
        }
      }

      .cardRemarks {
        border-top: 1px solid #eee;
        font-size: 12px;
        color: #666;
        padding-top: 10px;
      }
    }
  }

  .pageWrap {
    text-align: right;
    margin-top: 20px;
    height: 38px;
    padding-right: 20px;
  }
}

.ant-modal-body {
  padding: 20px 30px 0 20px;
}

.ruleHandleForm {
  .ant-input {
    vertical-align: middle;
  }

  .ant-select {
    vertical-align: middle;
  }

  .anticon-environment {
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    color: #666;
    vertical-align: middle;
  }

  .anticon-delete {
    color: red;
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    vertical-align: middle;
  }

  .anticon-plus-circle {
    color: blue;
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    vertical-align: middle;
  }

  .targetItem {
    // margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.mapContainer {
  height: 500px;
}
</style>
