import { onUpdated, onBeforeUpdate, onMounted, onBeforeUnmount, ref, nextTick, Ref, computed, watch } from 'vue'
import _ from 'lodash'

// 定义类型
type TableWrapperRef = Ref<HTMLElement | null>
type TableRef = Ref<{ $el: HTMLElement } | null>
export function getTableHeight(tableWrapperRef: TableWrapperRef, tableRef: TableRef) {
  // console.log('getTableHeight');
  const tableHeight = ref(400)
  function getTableContentHeight(element: HTMLElement | null): number {
    if (element) {
      const rect = element.getBoundingClientRect()
      const style = window.getComputedStyle(element)

      // 获取上下边框高度
      const borderTopWidth = parseFloat(style.borderTopWidth)
      const borderBottomWidth = parseFloat(style.borderBottomWidth)

      // 获取上下内边距
      const paddingTop = parseFloat(style.paddingTop)
      const paddingBottom = parseFloat(style.paddingBottom)

      // 计算除去内外边距和边框的高度
      const heightWithoutMarginPaddingAndBorder = rect.height - borderTopWidth - borderBottomWidth - paddingTop - paddingBottom

      // console.log('元素除去内外边距和边框的高度:', heightWithoutMarginPaddingAndBorder);
      return heightWithoutMarginPaddingAndBorder
    }
    return 400
  }
  // 计算表格动态高度
  function calculateTableHeight() {
    // console.log('getTableHeight-calculateTableHeight');
    if (tableWrapperRef.value && tableRef.value && tableRef.value.$el) {
      // const tableWrapperHeight = tableWrapperRef.value.offsetHeight
      const tableWrapperHeight = getTableContentHeight(tableWrapperRef.value)
      const thead = tableRef.value.$el.querySelector('.ant-table-thead')
      if (thead) {
        tableHeight.value = tableWrapperHeight - thead.offsetHeight
      }
    }
  }

  let resizeHandler: (() => void) | undefined
  let resizeObserver: any
  // 初始化和监听 resize 事件，这里使用防抖提高性能
  onMounted(() => {
    // calculateTableHeight()
    // console.log('getTableHeight-onMounted')
    // resizeHandler = _.debounce(calculateTableHeight, 300)
    // window.addEventListener('resize', resizeHandler as EventListener)

    resizeObserver = new ResizeObserver(entries => {
      console.log('getTableHeight-resizeObserver')
      _.debounce(calculateTableHeight, 300)()
    })
    if (tableWrapperRef.value) {
      resizeObserver.observe(tableWrapperRef.value)
    }
  })
  // 移除监听器
  onBeforeUnmount(() => {
    // console.log('getTableHeight-onBeforeUnmount');
    // if (resizeHandler) {
    //   window.removeEventListener('resize', resizeHandler as EventListener)
    // }
    if (tableWrapperRef.value && resizeObserver) {
      resizeObserver.unobserve(tableWrapperRef.value)
    }
  })
  // 监听 resize 事件，这里使用防抖提高性能
  onUpdated(() => {
    console.log('getTableHeight-onUpdated')
    nextTick(() => {
      calculateTableHeight()
    })
  })
  return tableHeight
}
