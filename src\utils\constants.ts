/*
 * 通用常量
 */
export interface EnumWrapper<T> {
  [key: string]: Enum<T>
}

export interface Enum<T> {
  [key: string]: EnumItem<T>
}

interface EnumItem<T> {
  value: T
  desc: string
}

interface EnumPlugin {
  getDescByValue(constantName: string, value: string | number | undefined): string

  getValueDescList(constantName: string): EnumItem<string | number>[]

  getValueDesc(constantName: string): { [key: string]: string }
}

export const PAGE_SIZE = 10

export const PAGE_SIZE_OPTIONS = ['10', '20', '30', '40']

//登录页面名字
export const PAGE_PATH_LOGIN = '/login'

//首页页面名字
export const HOME_PAGE_NAME = 'Home';

//首页页面路径
export const HOME_PAGE_PATH = '/home';

//404页面名字
export const PAGE_PATH_404 = '/404'

export const showTableTotal = function (total: number | string) {
  return `共${total}条`
}

export const FLAG_NUMBER_ENUM: Enum<number> = {
  TRUE: {
    value: 1,
    desc: '是'
  },
  FALSE: {
    value: 0,
    desc: '否'
  }
}

export const GENDER_ENUM: Enum<number> = {
  UNKNOWN: {
    value: 0,
    desc: '未知'
  },
  MAN: {
    value: 1,
    desc: '男'
  },
  WOMAN: {
    value: 2,
    desc: '女'
  }
}

export const USER_TYPE_ENUM: Enum<number> = {
  ADMIN_EMPLOYEE: {
    value: 1,
    desc: '员工'
  }
}

// 数据类型
export const DATA_TYPE_ENUM: Enum<number> = {
  NORMAL: {
    value: 1,
    desc: '普通'
  },
  ENCRYPT: {
    value: 10,
    desc: '加密'
  }
}

// 登录设备
export const LOGIN_DEVICE_ENUM: Enum<number> = {
  PC: {
    value: 1,
    desc: 'PC'
  },
  ANDROID: {
    value: 2,
    desc: '安卓'
  },
  APPLE: {
    value: 3,
    desc: '苹果'
  },
  H5: {
    value: 4,
    desc: 'H5'
  },
  WEIXIN_MP: {
    value: 5,
    desc: '微信小程序'
  }
}

export const MENU_TYPE_ENUM: Enum<number> = {
  CATALOG: {
    value: 1,
    desc: '目录',
  },
  MENU: {
    value: 2,
    desc: '菜单',
  },
  POINTS: {
    value: 3,
    desc: '按钮',
  },
};

/**
 * 权限类型
 */
export const MENU_PERMS_TYPE_ENUM: Enum<number> = {
  SA_TOKEN: {
    value: 1,
    desc: 'Sa-Token模式',
  }
};

/**
 * 默认的顶级菜单id为0
 */
export const MENU_DEFAULT_PARENT_ID = 0;
