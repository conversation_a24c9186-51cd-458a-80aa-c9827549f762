<template>
  <div :id="refName" style="width: 100%; height: 100%;"></div>
</template>

<script lang="ts" setup>
import * as echarts from 'echarts';
import { onMounted, ref, watch } from 'vue';

const props = defineProps({
  refName: {
    type: String,
    default: ''
  },
  options: {
    type: Object,
    default: {},
  }
});

// 定义图表实例
let chartInstance: echarts.ECharts | null = null;
let resizeObserver: ResizeObserver | null = null;

onMounted(() => {
  const chartDom = document.getElementById(props.refName);
  if (chartDom) {
    chartInstance = echarts.init(chartDom); // 初始化图表
    chartInstance.setOption(props.options); // 设置初始配置

    // 初始化 ResizeObserver
    resizeObserver = new ResizeObserver(() => {
      if (chartInstance) {
        chartInstance.resize(); // 自动适配容器大小
      }
    });

    resizeObserver.observe(chartDom); // 开始监听容器变化
  } else {
    console.error(`无法找到 ID 为 ${props.refName} 的 DOM 元素`);
  }
});

watch(
  () => props.options,
  (newValue) => {
    if (chartInstance && newValue) {
      chartInstance.setOption(newValue); // 更新图表配置
    }
  }
);

// 在组件销毁时释放图表实例
defineExpose({
  disposeChart() {
    if (resizeObserver) {
      resizeObserver.disconnect();
      resizeObserver = null;
    }
    if (chartInstance) {
      chartInstance.dispose();
      chartInstance = null;
    }
  },
});
</script>

<style scoped>
/* 根据需要添加样式 */
</style>