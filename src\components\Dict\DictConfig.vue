<!-- 管理工具-数据字典-字典配置 -->
<template>
  <div class="dictConfig dictConfigContainer">
    <div class="handleWrap clearfix">
      <div class="left fl">
        <a-form layout="inline" :model="searchFormState">
          <a-form-item>
            <a-input v-model:value="searchFormState.searchWord" placeholder="请输入名称" size="small" allowClear @pressEnter="onSearch"></a-input>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="onSearch">搜索</a-button>
          </a-form-item>
        </a-form>
      </div>
      <div class="right fr">
        <a-button type="primary" @click="addHandle">
          <template #icon>
            <PlusOutlined />
          </template>
          新增
        </a-button>
      </div>
    </div>
    <div class="contentWrap custom-scrollbar" ref="tableWrapperRef">
      <a-table
        ref="tableRef"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="false"
        bordered
        :rowKey="(record: any) => record.dictValueId"
        :scroll="{ y: tableHeight }"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'operation'">
            <span>
              <a-button type="link" size="small" @click="editHandle(record)">编辑</a-button>
              <a-button type="link" size="small" danger @click="delHandle(record)" style="color: #f5222d;">删除</a-button>
            </span>
          </template>
        </template>
      </a-table>
    </div>
    <div class="pageWrap">
      <a-pagination
        size="small"
        v-model:current="pagination.pageNum"
        :page-size="pagination.pageSize"
        :total="total"
        show-size-changer
        show-quick-jumper
        :show-total="(total: number) => `共 ${total} 条`"
        :page-size-options="PAGE_SIZE_OPTIONS"
        @change="(current: number, size: number) => onPageChange('current', current, size)"
        @showSizeChange="(current: number, size: number) => onPageChange('size', current, size)"
      />
    </div>
    <a-modal
      v-model:open="addEditModalState.visible"
      :title="addEditModalState.type === 'add' ? '新增字典值' : '编辑字典值'"
      width="600px"
      cancelText="取消"
      okText="提交"
      :closable="false"
      :keyboard="false"
      :maskClosable="false"
      :destroyOnClose="true"
      @cancel="handleCancle"
      @ok="handleOk"
    >
      <a-form
        class="dictConfigHandleForm"
        ref="dictConfigFormRef"
        :model="dictConfigFormState"
        :rules="dictConfigRules"
        :labelCol="{ span: 5 }"
      >
        <a-form-item label="名称" name="valueName">
          <a-input v-model:value="dictConfigFormState.valueName" :maxlength="30" />
        </a-form-item>
        <a-form-item label="数据值" name="valueCode">
          <a-input v-model:value="dictConfigFormState.valueCode" :maxlength="30" :disabled="addEditModalState.type === 'edit'" />
        </a-form-item>
        <a-form-item label="排序" name="sort">
          <a-input-number
            ref="sortRef"
            v-model:value="dictConfigFormState.sort"
            :min="1"
            :max="1000000"
            style="width: 30%;"
            :precision="0"
            v-input-number-change
          />
        </a-form-item>
        <a-form-item label="描述" name="remark">
          <a-textarea
            v-model:value="dictConfigFormState.remark"
            placeholder="请输入描述"
            :auto-size="{ minRows: 2, maxRows: 3 }"
            show-count
            :maxlength="1000"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import {
  PlusOutlined,
  SlackOutlined,
  MoreOutlined,
  FieldTimeOutlined,
  UserOutlined,
  ExclamationCircleOutlined,
  EnvironmentOutlined,
  DeleteOutlined,
  PlusCircleOutlined
} from '@ant-design/icons-vue'
import { message, Modal, TreeSelect } from 'ant-design-vue'
import { ValidateErrorEntity } from 'ant-design-vue/es/form/interface'
import { reactive, ref, UnwrapRef, createVNode, onMounted } from 'vue'

import { dictApi } from '@/api/manageTool/dictApi'
import { PageParamModel } from '@/types/paging'
import { PAGE_SIZE_OPTIONS } from '@/utils/constants'
import { getTableHeight } from '@/hooks/getTableHeight'
import _ from 'lodash'
// 注册组件
defineOptions({
  components: {
    PlusOutlined,
    SlackOutlined,
    MoreOutlined,
    FieldTimeOutlined,
    UserOutlined,
    EnvironmentOutlined,
    DeleteOutlined,
    PlusCircleOutlined
  }
})
const props = defineProps({
  rowData: {
    type: Object,
    default: () => {}
  }
})

/* ====================== 表格 ====================== */
const tableWrapperRef = ref()
const tableRef = ref()
const tableHeight = getTableHeight(tableWrapperRef, tableRef)

const columns = reactive([
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    customRender: ({ index }: any) => (pagination.value.pageNum - 1) * pagination.value.pageSize + index + 1,
    width: 80,
    align: 'center'
  },
  {
    title: '名称',
    dataIndex: 'valueName',
    key: 'valueName',
    width: 160,
    align: 'center'
  },
  {
    title: '数据值',
    dataIndex: 'valueCode',
    key: 'valueCode',
    width: 160,
    align: 'center'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    // slots: { customRender: 'operation' },
    width: 120,
    align: 'center'
  }
])

const dataSource = ref([])

/* ====================== 分页 ====================== */
const pagination = ref<PageParamModel>({
  pageNum: 1, // 当前页码
  pageSize: 10 // 每页数量
})
const total = ref<number>(0)
const onPageChange = (type: string, pageNum: number, pageSize: number) => {
  console.log(pageNum, pageSize)
  if (type === 'current') {
    pagination.value.pageNum = pageNum
  } else {
    pagination.value.pageNum = 1
    pagination.value.pageSize = pageSize
  }
  queryDictValue()
}

/* ====================== 搜索 ====================== */
interface SearchFormState {
  dictKeyId: number
  searchWord: string
}
const searchFormState = reactive<SearchFormState>({
  dictKeyId: props.rowData.dictKeyId,
  searchWord: ''
})

const queryDictValue = async () => {
  try {
    const result = await dictApi.queryDictValue({
      ...pagination.value,
      ...searchFormState
    })
    console.log(result)
    dataSource.value = result.data.list
    total.value = result.data.total
  } catch (e) {
    console.log(e)
    dataSource.value = []
    total.value = 0
  }
}

const onSearch = () => {
  console.log(searchFormState.searchWord)
  pagination.value.pageNum = 1
  total.value = 0
  dataSource.value = []
  queryDictValue()
}

/* ====================== 操作按钮 ====================== */
const addHandle = () => {
  console.log(dictConfigFormState)
  addEditModalState.value.type = 'add'
  addEditModalState.value.visible = true
  dictConfigFormState.value.dictKeyId = props.rowData.dictKeyId
}
const editHandle = (item: any) => {
  addEditModalState.value.type = 'edit'
  addEditModalState.value.visible = true
  dictConfigFormState.value.valueName = item.valueName
  dictConfigFormState.value.valueCode = item.valueCode
  dictConfigFormState.value.sort = item.sort
  dictConfigFormState.value.remark = item.remark
  dictConfigFormState.value.dictValueId = item.dictValueId
  dictConfigFormState.value.dictKeyId = props.rowData.dictKeyId
}
const delHandle = (item: any) => {
  console.log(item)
  Modal.confirm({
    title: () => '删除提醒',
    icon: () => createVNode(ExclamationCircleOutlined),
    content: () => '确定是否删除该字典值？',
    okText: () => '确认',
    okType: 'danger',
    cancelText: () => '取消',
    onOk: async () => {
      console.log('OK')
      try {
        await dictApi.deleteDictValue([item.dictValueId])
        message.success('删除成功')
        if (pagination.value.pageNum > 1 && dataSource.value.length === 1) {
          pagination.value.pageNum = pagination.value.pageNum - 1
        }
        queryDictValue()
      } catch (e) {
        console.log(e)
      }
    },
    onCancel() {
      console.log('Cancel')
    }
  })
}

/* ====================== 表单（新增、编辑） ====================== */
interface DictConfigFormState {
  // 字典keyId(不能为空)
  dictKeyId: number | null
  // 字典值Id(编辑时不能为空)
  dictValueId: number | null
  // 数据值
  valueCode: string
  // 名称
  valueName: string
  // 排序
  sort: number | null
  // 备注
  remark: string
}
const dictConfigFormModel = {
  dictKeyId: null,
  dictValueId: null,
  valueCode: '',
  valueName: '',
  sort: 1,
  remark: ''
}
const dictConfigFormState = ref<DictConfigFormState>(_.cloneDeep(dictConfigFormModel))
const dictConfigFormRef = ref()
const sortRef = ref()

const dictConfigRules = reactive({
  valueName: [{ required: true, message: '请输入名称' }],
  valueCode: [{ required: true, message: '请输入数据值' }],
  sort: [{ required: true, message: '请输入排序' }]
})

interface AddEditModalModel {
  visible: boolean
  type: '' | 'add' | 'edit'
}
const addEditModalState = ref<AddEditModalModel>({
  visible: false,
  type: ''
})
const handleCancle = () => {
  addEditModalState.value.visible = false
  dictConfigFormState.value = _.cloneDeep(dictConfigFormModel)
  dictConfigFormRef.value.resetFields()
}
const handleOk = () => {
  console.log('values', dictConfigFormState)
  dictConfigFormRef.value
    .validate()
    .then(async () => {
      try {
        if (addEditModalState.value.type === 'add') {
          await dictApi.addDictValue(dictConfigFormState.value)
          searchFormState.searchWord = ''
          onSearch()
          message.success('新增成功')
        } else {
          await dictApi.editDictValue(dictConfigFormState.value)
          queryDictValue()
          message.success('编辑成功')
        }
        addEditModalState.value.visible = false
        dictConfigFormState.value = _.cloneDeep(dictConfigFormModel)
        dictConfigFormRef.value.resetFields()
        console.log(dictConfigFormState.value)
      } catch (e) {
        console.log(e)
      }
    })
    .catch((error: ValidateErrorEntity) => {
      console.log('error', error)
    })
}

onMounted(() => {
  queryDictValue()
})
</script>
<script lang="ts">
export default {
  name: 'DictConfig'
}
</script>
<style lang="scss">
.dictConfigContainer {
  height: 100%;

  .handleWrap {
    height: 54px;
    padding: 10px;
    box-sizing: border-box;

    .left {
      width: 50%;

      .ant-input {
        box-sizing: border-box;
        width: 240px;
        height: 34px;
      }

      .ant-btn {
        height: 34px;
      }
    }

    .right {
      width: 40%;
      text-align: right;

      .ant-btn {
        height: 34px;
      }
    }
  }

  .contentWrap {
    height: calc(100% - 110px);
    padding: 10px;
    overflow: hidden;

    .ant-card {
      height: 100%;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      border-radius: 6px;
      cursor: default;

      &:hover {
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
      }

      .ant-card-body {
        padding: 15px;
      }

      .cardHead {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #000;

        .headIcon {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          background: #d1cdfc;
          text-align: center;
          min-width: 50px;

          .anticon-slack {
            font-size: 30px;
            color: #200de7;
            width: 30px;
            height: 30px;
            margin-top: 10px;
          }
        }

        .cardTitle {
          font-size: 14px;
          font-weight: 600;
          margin: 0 10px;
        }

        .ant-dropdown-link {
          color: #000;

          &:hover {
            color: #1890ff;
          }
        }
      }

      .cardInfo {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #999;
        margin: 10px 0;

        .anticon {
          margin-right: 4px;
        }
      }

      .cardRemarks {
        border-top: 1px solid #eee;
        font-size: 12px;
        color: #666;
        padding-top: 10px;
      }
    }
  }

  .pageWrap {
    text-align: right;
    margin-top: 20px;
    height: 38px;
    padding-right: 20px;
  }
}

.ant-modal-body {
  padding: 20px 30px 0 20px;
}

.dictConfigHandleForm {
  .ant-input {
    vertical-align: middle;
  }

  .ant-select {
    vertical-align: middle;
  }

  .anticon-environment {
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    color: #666;
    vertical-align: middle;
  }

  .anticon-delete {
    color: red;
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    vertical-align: middle;
  }

  .anticon-plus-circle {
    color: blue;
    font-size: 18px;
    margin-left: 10px;
    cursor: pointer;
    vertical-align: middle;
  }

  .targetItem {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.resetPwdTips {
  text-align: center;
  margin: 20px 0 30px;
  font-size: 20px;
}
</style>
