
html, body, #app, #my-app {
  height: 100%;
  overflow: hidden;
}

body {
  background-color: #f7f9fa;
  -webkit-font-smoothing: antialiased;
  // Prevent font enlargement in horizontal screen
  text-size-adjust: 100%;

  font-family: sans-serif, Roboto, sans-serif-medium, Arial;
  font-feature-settings: normal;
  color: #333;
  font-size: 14px;

  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.custom-scrollbar {
  overflow-y: auto;
  /* 启用垂直滚动条 */
}

/* 滚动条整体样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  /* 滚动条宽度 */
}

/* 滚动条轨道 */
.custom-scrollbar::-webkit-scrollbar-track {
  // background: #f1f1f1;
  /* 轨道背景颜色 */
  border-radius: 10px;
  /* 圆角 */
}

/* 滚动条滑块 */
.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c0bfbf;
  /* 滑块颜色 */
  border-radius: 10px;
  /* 圆角 */
}

/* 滑块悬停时的颜色 */
.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #888888;
  /* 悬停时滑块颜色 */
}

.clearfix::after {
  content: "";
  display: block;
  clear: both;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.single-line {
  white-space: nowrap;
  /* 禁止文本换行 */
  overflow: hidden;
  /* 隐藏超出容器的内容 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
}

.multi-line {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  /* 显示的行数 */
  overflow: hidden;
  text-overflow: ellipsis;
}
