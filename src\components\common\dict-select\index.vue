<!-- 下拉选择框-字典 -->
<template>
  <div :style="`width: ${width}`">
    <!-- :fieldNames="{ label: 'valueName', value: 'valueCode' }" -->
    <a-select
      v-model:value="selectValue"
      :style="`width: 100%`"
      :placeholder="props.placeholder"
      :allowClear="true"
      :size="size"
      :mode="mode"
      @change="onChange"
      :disabled="disabled"
      :options="dictDataList"
    ></a-select>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { dictApi } from '@/api/manageTool/dictApi'
import { message } from 'ant-design-vue'

const props = defineProps({
  dictCode: String,
  value: [Array, String],
  mode: {
    type: String,
    default: 'combobox'
  },
  width: {
    type: String,
    default: '200px'
  },
  placeholder: {
    type: String,
    default: '请选择'
  },
  size: {
    type: String,
    default: 'default'
  },
  // 禁用整个下拉选择框
  disabled: {
    type: Boolean,
    default: false
  },
  // 需要禁用的选项字典值编码
  disabledOption: {
    type: Array,
    default: () => []
  },
  // 需要隐藏的选项字典值编码
  hiddenOption: {
    type: Array,
    default: () => []
  }
})

// -------------------------- 查询 字典数据 --------------------------

const dictDataList = ref([])
async function initDictData() {
  try {
    const result = await dictApi.getDictValueList(props.dictCode)
    dictDataList.value = result.data
      .filter(item => !props.hiddenOption.includes(item.dataValue) && !item.disabledFlag)
      .map(item => ({ ...item, label: item.valueName, value: item.valueCode }))
    return result
  } catch (error) {
    message.error('数据字典获取失败')
    return 0
  }
}

onMounted(initDictData)

// -------------------------- 选中 相关、事件 --------------------------

const selectValue = ref(props.value)

watch(
  () => props.value,
  newValue => {
    // 如果传入的值是被禁用或被隐藏的选项，则移除这些选项
    if (Array.isArray(newValue)) {
      selectValue.value = newValue.filter(item => !props.disabledOption.includes(item) && !props.hiddenOption.includes(item))
    } else {
      selectValue.value = props.hiddenOption.includes(newValue) || props.disabledOption.includes(newValue) ? undefined : newValue
    }
  },
  { immediate: true }
)

const emit = defineEmits(['update:value', 'change'])

function onChange(value) {
  emit('update:value', value)
  emit('change', value)
}
</script>
